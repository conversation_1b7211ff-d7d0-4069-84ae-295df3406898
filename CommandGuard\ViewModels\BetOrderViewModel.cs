using CommandGuard.Enums;

namespace CommandGuard.ViewModels;

/// <summary>
/// 投注订单视图模型
/// 包含用户昵称和格式化显示信息，用于界面展示
/// </summary>
public class BetOrderViewModel
{
    /// <summary>
    /// 订单ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 用户账号
    /// </summary>
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 用户昵称
    /// </summary>
    public string NickName { get; set; } = string.Empty;

    /// <summary>
    /// 投注期号
    /// </summary>
    public string Issue { get; set; } = string.Empty;

    /// <summary>
    /// 投注项目
    /// </summary>
    public string PlayItem { get; set; } = string.Empty;

    /// <summary>
    /// 投注金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 投注赔率
    /// </summary>
    public decimal Odds { get; set; }

    /// <summary>
    /// 中奖金额（只有中奖时才有值）
    /// </summary>
    public decimal PotentialWinAmount { get; set; }

    /// <summary>
    /// 回水金额
    /// </summary>
    public decimal RebateAmount { get; set; }

    /// <summary>
    /// 实际中奖金额
    /// </summary>
    public decimal? ActualWinAmount { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public EnumBetOrderStatus Status { get; set; }

    /// <summary>
    /// 开奖结果（番摊结果：1、2、3、4）
    /// </summary>
    public string DrawResult { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 确认时间
    /// </summary>
    public DateTime? ConfirmedTime { get; set; }

    /// <summary>
    /// 结算时间
    /// </summary>
    public DateTime? SettledTime { get; set; }

    /// <summary>
    /// 获取用户显示名称
    /// 优先显示昵称，如果昵称为空则显示账号
    /// </summary>
    /// <returns>用户显示名称</returns>
    public string GetDisplayName()
    {
        return string.IsNullOrWhiteSpace(NickName) ? Account : NickName;
    }

    /// <summary>
    /// 获取状态描述
    /// </summary>
    /// <returns>状态的中文描述</returns>
    public string GetStatusDescription()
    {
        return Status switch
        {
            EnumBetOrderStatus.Confirmed => @"已确认",
            EnumBetOrderStatus.Cancelled => @"已取消",
            EnumBetOrderStatus.Win => @"中奖",
            EnumBetOrderStatus.Lose => @"未中奖",
            EnumBetOrderStatus.Draw => @"和局",
            _ => @"未知状态"
        };
    }

    /// <summary>
    /// 获取格式化的投注金额
    /// </summary>
    /// <returns>格式化的金额字符串</returns>
    public string GetFormattedAmount()
    {
        return Amount.ToString(@"F2");
    }

    /// <summary>
    /// 获取格式化的赔率
    /// </summary>
    /// <returns>格式化的赔率字符串</returns>
    public string GetFormattedOdds()
    {
        return Odds.ToString(@"F3");
    }

    /// <summary>
    /// 获取格式化的中奖金额
    /// </summary>
    /// <returns>格式化的中奖金额字符串</returns>
    public string GetFormattedPotentialWinAmount()
    {
        return PotentialWinAmount > 0 ? PotentialWinAmount.ToString(@"F2") : @"-";
    }

    /// <summary>
    /// 获取格式化的实际中奖金额
    /// </summary>
    /// <returns>格式化的中奖金额字符串</returns>
    public string GetFormattedActualWinAmount()
    {
        return ActualWinAmount?.ToString(@"F2") ?? @"-";
    }

    /// <summary>
    /// 获取格式化的回水金额
    /// </summary>
    /// <returns>格式化的回水金额字符串</returns>
    public string GetFormattedRebateAmount()
    {
        return RebateAmount.ToString(@"F2");
    }



    /// <summary>
    /// 获取格式化的创建时间
    /// </summary>
    /// <returns>格式化的时间字符串</returns>
    public string GetFormattedCreatedTime()
    {
        return CreatedTime.ToString(@"MM-dd HH:mm:ss");
    }

    /// <summary>
    /// 获取格式化的确认时间
    /// </summary>
    /// <returns>格式化的时间字符串</returns>
    public string GetFormattedConfirmedTime()
    {
        return ConfirmedTime?.ToString(@"MM-dd HH:mm:ss") ?? @"-";
    }

    /// <summary>
    /// 获取格式化的结算时间
    /// </summary>
    /// <returns>格式化的时间字符串</returns>
    public string GetFormattedSettledTime()
    {
        return SettledTime?.ToString(@"MM-dd HH:mm:ss") ?? @"-";
    }

    /// <summary>
    /// 获取投注详情描述
    /// </summary>
    /// <returns>投注详情字符串</returns>
    public string GetBetDescription()
    {
        return $@"{PlayItem}/{GetFormattedAmount()}";
    }

    /// <summary>
    /// 获取盈亏金额
    /// </summary>
    /// <returns>盈亏金额（正数为盈利，负数为亏损）</returns>
    public decimal GetProfitLoss()
    {
        return Status switch
        {
            EnumBetOrderStatus.Win => (ActualWinAmount ?? 0) - Amount,
            EnumBetOrderStatus.Lose => (ActualWinAmount ?? 0) - Amount, // 通常为 0 - Amount = -Amount
            EnumBetOrderStatus.Draw => 0, // 和局不计盈亏
            _ => 0 // 未结算的订单不计算盈亏
        };
    }

    /// <summary>
    /// 获取格式化的盈亏金额
    /// </summary>
    /// <returns>格式化的盈亏金额字符串</returns>
    public string GetFormattedProfitLoss()
    {
        var profitLoss = GetProfitLoss();
        if (profitLoss == 0) return @"-";

        // 只有正数且为小数时才添加+号，整数和负数保持原样
        return profitLoss > 0 && profitLoss % 1 != 0
            ? $@"+{profitLoss:F2}"
            : profitLoss.ToString("F2");
    }

    /// <summary>
    /// 判断是否可以取消
    /// </summary>
    /// <returns>是否可以取消</returns>
    public bool CanCancel()
    {
        return Status == EnumBetOrderStatus.Confirmed;
    }

    /// <summary>
    /// 判断是否已完成
    /// </summary>
    /// <returns>是否已完成</returns>
    public bool IsCompleted()
    {
        return Status == EnumBetOrderStatus.Cancelled ||
               Status == EnumBetOrderStatus.Win ||
               Status == EnumBetOrderStatus.Lose ||
               Status == EnumBetOrderStatus.Draw;
    }

    /// <summary>
    /// 判断是否中奖
    /// </summary>
    /// <returns>是否中奖</returns>
    public bool IsWin()
    {
        return Status == EnumBetOrderStatus.Win;
    }
}
