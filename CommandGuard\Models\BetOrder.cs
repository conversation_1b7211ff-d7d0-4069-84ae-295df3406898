using System.ComponentModel.DataAnnotations;
using CommandGuard.Enums;
using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 投注订单模型
/// 记录用户的投注信息和订单状态
/// </summary>
[Table(Name = @"BetOrders")]
[Index(@"idx_bet_order_number", nameof(OrderNumber), IsUnique = true)]
[Index(@"idx_bet_account", nameof(Account))]
[Index(@"idx_bet_issue", nameof(Issue))]
[Index(@"idx_bet_status", nameof(Status))]
[Index(@"idx_bet_created", nameof(CreatedTime))]
[Index(@"idx_bet_play_item", nameof(PlayItem))]
public class BetOrder
{
    #region 基本信息

    /// <summary>
    /// 订单ID，自增主键
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public long Id { get; set; }

    /// <summary>
    /// 注单号，9位长度的自增字符串
    /// </summary>
    [Required(ErrorMessage = @"注单号不能为空")]
    [StringLength(9, ErrorMessage = @"注单号长度必须为9位")]
    [Column(StringLength = 9)]
    public string OrderNumber { get; set; } = string.Empty;

    /// <summary>
    /// 投注用户账号
    /// </summary>
    [Required(ErrorMessage = @"用户账号不能为空")]
    [StringLength(50, ErrorMessage = @"用户账号长度不能超过50个字符")]
    [Column(StringLength = 50)]
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 投注用户昵称
    /// </summary>
    [StringLength(100, ErrorMessage = @"用户昵称长度不能超过100个字符")]
    [Column(StringLength = 100)]
    public string NickName { get; set; } = string.Empty;

    #endregion

    #region 投注信息

    /// <summary>
    /// 投注期号
    /// </summary>
    [Required(ErrorMessage = @"投注期号不能为空")]
    [StringLength(50, ErrorMessage = @"投注期号长度不能超过50个字符")]
    [Column(StringLength = 50)]
    public string Issue { get; set; } = string.Empty;

    /// <summary>
    /// 投注项目（如：1正、2番、12角等）
    /// </summary>
    [Required(ErrorMessage = @"投注项目不能为空")]
    [StringLength(20, ErrorMessage = @"投注项目长度不能超过20个字符")]
    [Column(StringLength = 20)]
    public string PlayItem { get; set; } = string.Empty;

    /// <summary>
    /// 投注金额
    /// </summary>
    [Range(0.01, double.MaxValue, ErrorMessage = @"投注金额必须大于0")]
    [Column(Precision = 18, Scale = 2)]
    public decimal Amount { get; set; }

    /// <summary>
    /// 投注时的赔率
    /// </summary>
    [Range(1.0, 100.0, ErrorMessage = @"赔率必须在1.0-100.0之间")]
    [Column(Precision = 8, Scale = 3)]
    public decimal Odds { get; set; }

    #endregion

    #region 金额信息

    /// <summary>
    /// 中奖金额（只有中奖时才有值，否则为0）
    /// </summary>
    [Column(Precision = 18, Scale = 2)]
    public decimal PotentialWinAmount { get; set; }

    /// <summary>
    /// 实际中奖金额（仅在中奖时有值）
    /// </summary>
    [Column(Precision = 18, Scale = 2)]
    public decimal? ActualWinAmount { get; set; }

    /// <summary>
    /// 回水金额（投注金额 × 回水比例）
    /// </summary>
    [Column(Precision = 18, Scale = 2)]
    public decimal RebateAmount { get; set; }

    /// <summary>
    /// 投注前用户余额
    /// </summary>
    [Column(Precision = 18, Scale = 2)]
    public decimal BalanceBeforeBet { get; set; }

    /// <summary>
    /// 投注后用户余额
    /// </summary>
    [Column(Precision = 18, Scale = 2)]
    public decimal BalanceAfterBet { get; set; }

    #endregion

    #region 状态和结果

    /// <summary>
    /// 订单状态
    /// </summary>
    public EnumBetOrderStatus Status { get; set; } = EnumBetOrderStatus.Confirmed;

    /// <summary>
    /// 开奖结果（番摊结果：1、2、3、4）
    /// </summary>
    [StringLength(1, ErrorMessage = @"开奖结果长度必须为1位")]
    [Column(StringLength = 1)]
    public string DrawResult { get; set; } = string.Empty;

    #endregion

    #region 时间信息

    /// <summary>
    /// 创建时间
    /// </summary>
    [Column(ServerTime = DateTimeKind.Local)]
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 确认时间（订单被确认的时间）
    /// </summary>
    public DateTime? ConfirmedTime { get; set; }

    /// <summary>
    /// 结算时间（开奖结算的时间）
    /// </summary>
    public DateTime? SettledTime { get; set; }

    /// <summary>
    /// 派奖时间（中奖订单派奖的时间）
    /// </summary>
    public DateTime? PaidTime { get; set; }

    #endregion

    #region 处理信息

    /// <summary>
    /// 处理人员（确认、取消、拒绝订单的操作员）
    /// </summary>
    [StringLength(50, ErrorMessage = @"处理人员长度不能超过50个字符")]
    [Column(StringLength = 50)]
    public string Processor { get; set; } = string.Empty;

    /// <summary>
    /// 处理备注
    /// </summary>
    [StringLength(500, ErrorMessage = @"处理备注长度不能超过500个字符")]
    [Column(StringLength = 500)]
    public string ProcessNote { get; set; } = string.Empty;

    /// <summary>
    /// 原始消息内容
    /// </summary>
    [StringLength(500, ErrorMessage = @"原始消息内容长度不能超过500个字符")]
    [Column(StringLength = 500)]
    public string OriginalMessage { get; set; } = string.Empty;

    /// <summary>
    /// 关联的消息ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    #endregion

    #region 飞单信息

    /// <summary>
    /// 飞单状态 - 投注数据抛售到平台的状态
    /// </summary>
    public EnumFlightOrderStatus EnumFlightStatus { get; set; } = EnumFlightOrderStatus.Pending;

    /// <summary>
    /// 飞单时间 - 开始抛售的时间
    /// </summary>
    public DateTime? FlightTime { get; set; }

    /// <summary>
    /// 飞单完成时间 - 抛售完成的时间（成功或失败）
    /// </summary>
    public DateTime? FlightCompletedTime { get; set; }

    /// <summary>
    /// 飞单备注 - 抛售过程的备注信息
    /// </summary>
    [StringLength(500, ErrorMessage = @"飞单备注长度不能超过500个字符")]
    [Column(StringLength = 500)]
    public string FlightNote { get; set; } = string.Empty;

    #endregion

    #region 实例方法

    /// <summary>
    /// 计算回水金额
    /// </summary>
    /// <param name="rebatePercent">回水比例（百分比）</param>
    public void CalculateRebateAmount(decimal rebatePercent)
    {
        RebateAmount = Amount * (rebatePercent / 100);
    }

    /// <summary>
    /// 设置中奖信息
    /// </summary>
    /// <param name="winAmount">中奖金额</param>
    /// <param name="drawResult">开奖结果（1、2、3、4）</param>
    public void SetWinInfo(decimal winAmount, string drawResult)
    {
        ActualWinAmount = winAmount;
        PotentialWinAmount = winAmount; // 中奖时设置中奖金额
        DrawResult = drawResult ?? string.Empty;
        Status = EnumBetOrderStatus.Win;
        SettledTime = DateTime.Now;
    }

    /// <summary>
    /// 设置未中奖信息
    /// </summary>
    /// <param name="drawResult">开奖结果（1、2、3、4）</param>
    public void SetLostInfo(string drawResult)
    {
        ActualWinAmount = 0;
        PotentialWinAmount = 0; // 未中奖时中奖金额为0
        DrawResult = drawResult ?? string.Empty;
        Status = EnumBetOrderStatus.Lose;
        SettledTime = DateTime.Now;
    }

    /// <summary>
    /// 设置和局信息
    /// </summary>
    /// <param name="drawResult">开奖结果（1、2、3、4）</param>
    public void SetDrawInfo(string drawResult)
    {
        ActualWinAmount = Amount; // 和局退还本金
        PotentialWinAmount = Amount; // 和局时显示退还金额
        DrawResult = drawResult ?? string.Empty;
        Status = EnumBetOrderStatus.Draw;
        SettledTime = DateTime.Now;
    }

    #endregion

    #region 静态方法

    // private static long _orderNumberCounter;
    // private static readonly object LockObject = new();
    // private static string _lastResetDate = string.Empty;
    

    #endregion
}