using System.Diagnostics;
using System.Text;
using CommandGuard.Configuration;
using CommandGuard.Enums;
using CommandGuard.Helpers;
using CommandGuard.Interfaces.Business;
using CommandGuard.Interfaces.Chat;
using CommandGuard.Interfaces.Infrastructure;
using CommandGuard.Interfaces.Lottery;
using CommandGuard.Models;
using CommandGuard.Services.Lottery;
using CommandGuard.ViewModels;
using Flurl.Http;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

/// <summary>
/// 主窗体类 - CommandGuard
/// 采用现代.NET架构：依赖注入、异步编程、数据绑定、异常处理
/// 支持实时数据更新、智能变化检测、优雅的用户体验
/// </summary>
public partial class FormMain : Form
{
    #region 字段和属性

    #region 控件和标记

    private System.Windows.Forms.Timer _refreshTimer = null!; // 定时刷新数据的计时器
    private System.Windows.Forms.Timer? _currentIssueBetUpdateTimer; // 定时刷新当前期号下注数据的计时器
    private string _lastLoadedIssue = string.Empty;
    private DateTime _lastCurrentIssueBetUpdateTime = DateTime.MinValue;

    #endregion

    #region 依赖注入服务

    private readonly ILogger<FormMain> _logger;
    private readonly IMemberService _memberService;
    private readonly IChatService _chatService;
    private readonly IRobotService _robotService;
    private readonly ICommandService _commandService;
    private readonly IFinancialService _financialService;
    private readonly IIssueTimeService _issueTimeService;
    private readonly IHttpApiService _httpApiService;
    private readonly IBetRecordService _betRecordService;
    private readonly IDepositWithdrawRecordService _depositWithdrawRecordService;
    private readonly IWinLossRebateRecordService _winLossRebateRecordService;
    private readonly IAgentRebateRecordService _agentRebateRecordService;
    private readonly IDrawService _drawService;
    private readonly ImageHelper _imageHelper;
    private readonly ISystemManagementService _systemManagementService;
    private readonly SettlementService _settlementService;
    private readonly IOddsService _oddsService;
    private readonly ISystemSettingService _systemSettingService;

    #endregion

    #region 数据集合

    private List<Member> _members = [];
    private List<MemberViewModel> _memberViewModels = [];
    private List<DepositRequest> _depositRequests = [];
    private List<WithdrawRequest> _withdrawRequests = [];
    private List<DepositRequestViewModel> _depositRequestViewModels = [];
    private List<WithdrawRequestViewModel> _withdrawRequestViewModels = [];
    private List<CurrentIssueBetViewModel> _currentIssueBetViewModels = [];
    private List<BetRecordViewModel> _betRecordViewModels = [];
    private List<DepositWithdrawRecordViewModel> _depositWithdrawRecordViewModels = [];
    private List<WinLossRebateRecordViewModel> _winLossRebateRecordViewModels = [];
    private List<AgentRebateRecordViewModel> _agentRebateRecordViewModels = [];

    /// <summary>
    /// 已发送的提醒记录集合，用于避免重复发送相同的提醒消息
    /// </summary>
    private readonly HashSet<string> _sentWarnings = [];

    /// <summary>
    /// 已发送的开盘通知记录，避免重复发送
    /// </summary>
    private readonly HashSet<string> _sentOpenNotifications = [];

    #endregion

    #region 数据绑定,通过持久的BindingSource对象，避免重复创建导致状态丢失

    private BindingSource _memberBindingSource = new();
    private BindingSource _depositBindingSource = new();
    private BindingSource _withdrawBindingSource = new();
    private BindingSource _currentIssueBetBindingSource = new();
    private BindingSource _betRecordBindingSource = new();
    private BindingSource _depositWithdrawRecordBindingSource = new();
    private BindingSource _winLossRebateRecordBindingSource = new();
    private BindingSource _agentRebateRecordBindingSource = new();

    #endregion

    #region 数据变化检测,通过保存上次的数据哈希值，用于检测数据是否真正发生变化

    private int _lastMemberViewModelsHash;
    private int _lastDepositRequestsHash;
    private int _lastWithdrawRequestsHash;
    private int _lastCurrentIssueBetHash;
    private int _lastGroupCount; // 上次的群组数量，用于检测群组信息变化

    #endregion

    #endregion

    #region 构造函数和初始化

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="memberService"></param>
    /// <param name="chatService"></param>
    /// <param name="robotService"></param>
    /// <param name="commandService"></param>
    /// <param name="financialService"></param>
    /// <param name="issueTimeService"></param>
    /// <param name="httpApiService"></param>
    /// <param name="betRecordService"></param>
    /// <param name="depositWithdrawRecordService"></param>
    /// <param name="winLossRebateRecordService"></param>
    /// <param name="agentRebateRecordService"></param>
    /// <param name="drawService"></param>
    /// <param name="imageHelper"></param>
    /// <param name="systemManagementService"></param>
    /// <param name="oddsService"></param>
    /// <param name="systemSettingService"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public FormMain(ILogger<FormMain> logger,
        IMemberService memberService,
        IChatService chatService,
        IRobotService robotService,
        ICommandService commandService,
        IFinancialService financialService,
        IIssueTimeService issueTimeService,
        IHttpApiService httpApiService,
        IBetRecordService betRecordService,
        IDepositWithdrawRecordService depositWithdrawRecordService,
        IWinLossRebateRecordService winLossRebateRecordService,
        IAgentRebateRecordService agentRebateRecordService,
        IDrawService drawService,
        ImageHelper imageHelper,
        ISystemManagementService systemManagementService,
        IOddsService oddsService,
        ISystemSettingService systemSettingService,
        SettlementService settlementService)
    {
        // 验证依赖注入的服务实例
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _memberService = memberService ?? throw new ArgumentNullException(nameof(memberService));
        _chatService = chatService ?? throw new ArgumentNullException(nameof(chatService));
        _robotService = robotService ?? throw new ArgumentNullException(nameof(robotService));
        _commandService = commandService ?? throw new ArgumentNullException(nameof(commandService));
        _financialService = financialService ?? throw new ArgumentNullException(nameof(financialService));
        _issueTimeService = issueTimeService ?? throw new ArgumentNullException(nameof(issueTimeService));
        _httpApiService = httpApiService ?? throw new ArgumentNullException(nameof(httpApiService));
        _betRecordService = betRecordService ?? throw new ArgumentNullException(nameof(betRecordService));
        _depositWithdrawRecordService = depositWithdrawRecordService ?? throw new ArgumentNullException(nameof(depositWithdrawRecordService));
        _winLossRebateRecordService = winLossRebateRecordService ?? throw new ArgumentNullException(nameof(winLossRebateRecordService));
        _agentRebateRecordService = agentRebateRecordService ?? throw new ArgumentNullException(nameof(agentRebateRecordService));
        _drawService = drawService ?? throw new ArgumentNullException(nameof(drawService));
        _imageHelper = imageHelper ?? throw new ArgumentNullException(nameof(imageHelper));
        _systemManagementService = systemManagementService ?? throw new ArgumentNullException(nameof(systemManagementService));
        _oddsService = oddsService ?? throw new ArgumentNullException(nameof(oddsService));
        _systemSettingService = systemSettingService ?? throw new ArgumentNullException(nameof(systemSettingService));
        _settlementService = settlementService ?? throw new ArgumentNullException(nameof(settlementService));

        // 初始化UI组件
        InitializeComponent();

        // 启用双缓冲以减少闪烁，优化绘制性能
        SetStyle(ControlStyles.AllPaintingInWmPaint |
                 ControlStyles.UserPaint |
                 ControlStyles.DoubleBuffer |
                 ControlStyles.ResizeRedraw |
                 ControlStyles.OptimizedDoubleBuffer, true);
        UpdateStyles();

        // 注册事件处理
        FormClosing += FormMain_FormClosing;
        FormClosed += FormMain_FormClosed;
        Shown += FormMain_Shown;

        // // 设置启动时自动最小化到托盘
        // ShowInTaskbar = false;
        // WindowState = FormWindowState.Minimized;
        _logger.LogInformation(@"主窗体构造函数执行完成");
    }

    #endregion

    #region FormMain_Load事件处理

    /// <summary>
    /// 窗体加载事件处理 - 系统启动的核心流程
    /// 优化加载性能：快速显示UI → 后台初始化服务 → 延迟加载数据
    /// </summary>
    private async void FormMain_Load(object sender, EventArgs e)
    {
        try
        {
            _logger.LogInformation(@"开始主窗体初始化流程");

            // 第一阶段：快速显示基本UI
            try
            {
                _logger.LogInformation(@"开始更新状态栏");

                _logger.LogInformation(@"开始初始化查询界面");
                // 快速初始化UI组件（同步操作）
                await InitializeQueryInterfaceAsync(); // 初始化查询界面

                _logger.LogInformation(@"基本UI初始化完成");
            }
            catch (Exception uiEx)
            {
                _logger.LogError(uiEx, @"UI初始化失败");
                throw;
            }

            // 第二阶段：后台初始化核心服务（异步，不阻塞UI）
            _ = Task.Run(async () =>
            {
                try
                {
                    await InitializeCoreServicesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, @"核心服务初始化失败");
                }
            });

            _logger.LogInformation(@"主窗体快速初始化完成，后台服务正在启动");

            // 初始化StatusStrip标签
            InitializeStatusLabels();

            // 初始化按钮状态（所有按钮初始为禁用状态）
            UpdateServiceControlButtonsState();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"主窗体初始化失败");

            var errorMessage = ex.InnerException != null
                ? $@"系统初始化失败：{ex.Message}{Environment.NewLine}详细信息：{ex.InnerException.Message}"
                : $@"系统初始化失败：{ex.Message}";

            MessageBox.Show(errorMessage, @"系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

            // 记录详细的错误堆栈信息
            _logger.LogError(@"错误堆栈：{StackTrace}", ex.StackTrace);
        }
    }

    /// <summary>
    /// 初始化核心服务（后台异步执行）
    /// </summary>
    private async Task InitializeCoreServicesAsync()
    {
        try
        {
            _logger.LogInformation(@"开始初始化核心服务");

            // 更新状态栏

            // 第一步：清理和初始化基础服务
            await _issueTimeService.ClearIssueTimeAsync(RuntimeConfiguration._robotServiceCancellation!.Token);
            await _drawService.ClearDrawAsync(RuntimeConfiguration._robotServiceCancellation.Token);


            await _systemSettingService.InitializeDefaultSettingsAsync();
            await _oddsService.InitializeDefaultOddsAsync();

            // 第二步：初始化DataGridView（在UI线程中执行）
            var tcs = new TaskCompletionSource<bool>();
            BeginInvoke(async () =>
            {
                try
                {
                    await InitializeDataGridViewsAsync();
                    tcs.SetResult(true);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, @"初始化DataGridView失败");
                    tcs.SetException(ex);
                }
            });
            await tcs.Task;

            // 第三步：创建时间数据和启动服务

            await _issueTimeService.CreateIssueTimeAsync();


            try
            {
                await _httpApiService.StartAsync();
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains(@"端口5000已被"))
            {
                // 端口冲突，显示弹窗提醒用户
                _logger.LogWarning(@"检测到端口冲突: {Message}", ex.Message);

                BeginInvoke(() =>
                {
                    MessageBox.Show(
                        ex.Message,
                        @"端口冲突",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                });

                throw; // 重新抛出异常，让上层处理
            }


            // 第四步：启动后台服务（不等待完成）

            _ = _issueTimeService.UpdateCurrentIssueTimeAsync(RuntimeConfiguration._robotServiceCancellation.Token);
            _ = _robotService.DoWorkAsync(RuntimeConfiguration._robotServiceCancellation.Token);

            // 第五步：在UI线程中初始化定时器
            BeginInvoke(() =>
            {
                try
                {
                    InitializeRefreshTimer();
                    InitializeCurrentIssueBetUpdateTimer();
                    _logger.LogInformation(@"核心服务初始化完成");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, @"定时器初始化失败");
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"核心服务初始化失败");
            throw;
        }
    }

    #endregion

    #region FormMain_Shown事件处理

    /// <summary>
    /// 窗体完全显示后的事件处理 - 延迟加载数据
    /// 在窗口完全显示后再加载数据，避免阻塞UI显示
    /// </summary>
    private async void FormMain_Shown(object? sender, EventArgs e)
    {
        try
        {
            _logger.LogInformation(@"窗体已显示，开始延迟加载数据");

            // 延迟一小段时间，确保窗口完全渲染
            await Task.Delay(100);

            // 在后台加载初始数据
            _ = Task.Run(async () =>
            {
                try
                {
                    await LoadInitialDataAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, @"延迟加载数据失败");
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"FormMain_Shown事件处理失败");
        }
    }

    /// <summary>
    /// 加载初始数据（后台执行）
    /// </summary>
    private async Task LoadInitialDataAsync()
    {
        try
        {
            _logger.LogInformation(@"开始加载初始数据");

            // 加载基础数据
            await LoadAllDataAsync();

            _logger.LogInformation(@"初始数据加载完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"加载初始数据失败");
            throw;
        }
    }

    #endregion

    #region 初始化定时器

    /// <summary>
    /// 初始化数据刷新定时器
    /// </summary>
    private void InitializeRefreshTimer()
    {
        _refreshTimer = new System.Windows.Forms.Timer
        {
            Interval = 1000 // 1秒刷新一次
        };

        _refreshTimer.Tick += async (_, _) =>
        {
            try
            {
                // 暂时停止定时器，避免重复执行
                _refreshTimer.Stop();

                // 刷新数据
                await LoadAllDataAsync().ConfigureAwait(true);

                // 更新期号时间UI
                _ = UpdateIssueTimeUiAsync();

                // 更新StatusStrip标签信息
                UpdateStatusStripInfo();

                // 检查群组信息是否有更新
                CheckAndUpdateGroupInfo();

                _logger.LogDebug(@"定时刷新数据完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, @"定时刷新数据失败");
            }
            finally
            {
                // 重新启动定时器
                if (!IsDisposed)
                {
                    _refreshTimer.Start();
                }
            }
        };

        _refreshTimer.Start();
        _logger.LogInformation(@"数据刷新定时器已启动，间隔: {Interval}ms", _refreshTimer.Interval);
    }

    /// <summary>
    /// 初始化当前期投注数据更新定时器
    /// </summary>
    private void InitializeCurrentIssueBetUpdateTimer()
    {
        _currentIssueBetUpdateTimer = new System.Windows.Forms.Timer
        {
            Interval = 3000 // 3秒刷新一次投注数据
        };

        _currentIssueBetUpdateTimer.Tick += async (_, _) =>
        {
            try
            {
                // 暂时停止定时器，避免重复执行
                _currentIssueBetUpdateTimer.Stop();

                // 增量更新当前期投注数据
                await LoadCurrentIssueBetOrdersAsync(true).ConfigureAwait(true);

                // 如果有数据变化，更新UI
                if (InvokeRequired)
                {
                    Invoke(() =>
                    {
                        try
                        {
                            // 只更新当前期投注数据的绑定源
                            var currentHash = CalculateDataHash(_currentIssueBetViewModels);
                            if (currentHash != _lastCurrentIssueBetHash)
                            {
                                _currentIssueBetBindingSource.DataSource = _currentIssueBetViewModels.ToList();
                                _lastCurrentIssueBetHash = currentHash;
                                _logger.LogDebug(@"当前期投注数据UI已更新，数量: {Count}", _currentIssueBetViewModels.Count);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, @"更新当前期投注数据UI失败");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, @"当前期投注数据定时更新失败");
            }
            finally
            {
                // 重新启动定时器
                if (!IsDisposed && _currentIssueBetUpdateTimer != null)
                {
                    _currentIssueBetUpdateTimer.Start();
                }
            }
        };

        _currentIssueBetUpdateTimer.Start();
        _logger.LogInformation(@"当前期投注数据更新定时器已启动，间隔: {Interval}ms", _currentIssueBetUpdateTimer.Interval);
    }

    #endregion

    #region 开始游戏服务控制

    /// <summary>
    /// 开始游戏按钮点击事件
    /// </summary>
    private async void button_StartService_Click(object sender, EventArgs e)
    {
        try
        {
            // 检查是否选择了工作群组
            var selectedGroupId = GetSelectedWorkGroupId();
            if (string.IsNullOrEmpty(selectedGroupId))
            {
                MessageBox.Show(@"请先选择工作群组后再开始游戏！", @"提示",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 设置工作群组ID到RuntimeConfiguration
            RuntimeConfiguration.UpdateWorkGroupId(selectedGroupId);
            var groupName = RuntimeConfiguration.GetGroupName(selectedGroupId);
            _logger.LogInformation(@"游戏开始，工作群组已设置，群组ID: {GroupId}, 群组名称: {GroupName}",
                selectedGroupId, groupName);

            // 更新游戏服务状态
            RuntimeConfiguration.IsGameServiceStarted = true;

            // 首次开启游戏服务时，检查是否可以下注
            var openTimeSpan = _issueTimeService.GetCurrentCachedOpenTimeSpan();
            var closeTimeSpan = _issueTimeService.GetCurrentCachedCloseTimeSpan();
            if (openTimeSpan < 0 && closeTimeSpan > 0)
            {
                RuntimeConfiguration.CanBet = true;
            }

            // 禁用工作群组选择
            comboBox_WorkGroupId.Enabled = false;

            // 更新按钮状态
            UpdateServiceControlButtonsState();

            // 更新收单状态（游戏服务已启动）
            UpdateOrderCollectionStatus(RuntimeConfiguration.CanBet);

            // 发送游戏开始前的图片
            await SendGameStartImagesAsync();

            // 通知聊天平台游戏已开始
            await NotifyChatPlatformGameStartedAsync(groupName);

            _logger.LogInformation(@"游戏服务已启动，工作群组ComboBox已禁用");
        }
        catch (Exception ex)
        {
            Debug.WriteLine(ex.ToString());

            // 启动失败时恢复状态
            RuntimeConfiguration.IsGameServiceStarted = false;
            comboBox_WorkGroupId.Enabled = true;

            // 更新按钮状态
            UpdateServiceControlButtonsState();

            await Task.Delay(0).ConfigureAwait(false);
        }
    }

    /// <summary>
    /// 停止游戏按钮点击事件 
    /// </summary>
    private async void button_StopService_Click(object sender, EventArgs e)
    {
        try
        {
            button_StopService.Enabled = false;
            button_StopService.Text = @"停止中...";

            _logger.LogInformation(@"用户点击停止游戏按钮");

            // 检查飞单功能是否还在运行
            var isFlightOrderRunning = !button_StartBet.Enabled && button_StopBet.Enabled;

            if (isFlightOrderRunning)
            {
                _logger.LogInformation(@"飞单功能正在运行，自动停止飞单功能");
                button_StopService.Text = @"停止飞单中...";

                // 先停止飞单功能
                try
                {
                    // 直接通过RuntimeConfiguration停止飞单功能
                    RuntimeConfiguration.IsFlightOrderEnabled = false;

                    _logger.LogInformation(@"飞单功能自动停止成功");
                }
                catch (Exception flightEx)
                {
                    _logger.LogError(flightEx, @"自动停止飞单功能时发生异常，但继续停止游戏服务");
                }
            }

            button_StopService.Text = @"停止游戏中...";

            // 更新游戏服务状态
            RuntimeConfiguration.IsGameServiceStarted = false;

            // 重置投注状态
            RuntimeConfiguration.CanBet = false;

            // 重新启用工作群组选择
            comboBox_WorkGroupId.Enabled = true;

            // 更新按钮状态
            UpdateServiceControlButtonsState();

            // 更新收单状态（游戏服务已停止）
            UpdateOrderCollectionStatus(false);

            // 通知聊天平台游戏已停止
            await NotifyChatPlatformGameStoppedAsync();

            _logger.LogInformation(@"用户手动停止HttpApi服务成功，工作群组ComboBox已重新启用");
        }
        catch (Exception ex)
        {
            button_StopService.Enabled = true;
            button_StopService.Text = @"停止游戏";

            _logger.LogError(ex, @"用户手动停止HttpApi服务失败");
            MessageBox.Show($@"停止HttpApi服务失败：{ex.Message}", @"错误",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            await Task.Delay(0).ConfigureAwait(false);
        }
    }

    /// <summary>
    /// 开始飞单按钮点击事件 - 启用飞单功能（中介收单模式）
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void button_StartBet_Click(object sender, EventArgs e)
    {
        try
        {
            button_StartBet.Enabled = false;
            button_StartBet.Text = @"启用中...";

            _logger.LogInformation(@"用户点击开始飞单按钮");

            // 检查游戏服务是否已启动
            if (!RuntimeConfiguration.IsGameServiceStarted)
            {
                _logger.LogInformation(@"游戏服务未启动，自动启动游戏服务");
                button_StartBet.Text = @"启动游戏中...";

                // 先启动游戏服务
                try
                {
                    // 更新游戏服务状态
                    RuntimeConfiguration.IsGameServiceStarted = true;

                    // 更新收单状态
                    UpdateOrderCollectionStatus(RuntimeConfiguration.CanBet);

                    _logger.LogInformation(@"游戏服务自动启动成功");
                }
                catch (Exception gameEx)
                {
                    // 恢复状态
                    RuntimeConfiguration.IsGameServiceStarted = false;
                    UpdateServiceControlButtonsState();

                    _logger.LogError(gameEx, @"自动启动游戏服务失败");
                    MessageBox.Show($@"启动游戏服务失败：{gameEx.Message}\n无法启用飞单功能。",
                        @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            }

            button_StartBet.Text = @"启用飞单中...";

            // 启用飞单功能
            RuntimeConfiguration.IsFlightOrderEnabled = true;

            // 更新按钮状态
            UpdateServiceControlButtonsState();

            _logger.LogInformation(@"飞单功能已启用，系统切换为中介收单模式");
        }
        catch (Exception ex)
        {
            // 恢复状态
            RuntimeConfiguration.IsFlightOrderEnabled = false;
            UpdateServiceControlButtonsState();

            _logger.LogError(ex, @"启用飞单功能时发生异常");
            MessageBox.Show($@"启用飞单功能失败：{ex.Message}",
                @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            await Task.Delay(0).ConfigureAwait(false);
        }
    }

    /// <summary>
    /// 停止飞单按钮点击事件 - 禁用飞单功能（庄家模式）
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void button_StopBet_Click(object sender, EventArgs e)
    {
        try
        {
            button_StopBet.Enabled = false;
            button_StopBet.Text = @"停止中...";

            _logger.LogInformation(@"用户点击停止飞单按钮");

            // 禁用飞单功能
            RuntimeConfiguration.IsFlightOrderEnabled = false;

            // 更新按钮状态
            UpdateServiceControlButtonsState();

            _logger.LogInformation(@"飞单功能已禁用，系统切换为庄家模式");
        }
        catch (Exception ex)
        {
            // 恢复状态
            RuntimeConfiguration.IsFlightOrderEnabled = true;
            UpdateServiceControlButtonsState();

            _logger.LogError(ex, @"禁用飞单功能时发生异常");
            MessageBox.Show($@"禁用飞单功能失败：{ex.Message}",
                @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 选择Q群按钮点击事件
    /// </summary>
    private void button_选择Q群_Click(object sender, EventArgs e)
    {
        try
        {
            // 首先检查游戏是否已经开始
            if (RuntimeConfiguration.IsGameServiceStarted)
            {
                // 游戏已开始，提示用户需要先停止游戏
                MessageBox.Show(
                    @"游戏服务正在运行中，无法重新选择Q群。" + Environment.NewLine + Environment.NewLine +
                    @"请先点击【停止游戏】按钮停止游戏服务，然后再选择Q群。",
                    @"无法选择Q群",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                _logger.LogInformation(@"用户尝试在游戏运行时选择Q群，已提醒需要先停止游戏");
                return;
            }

            // 游戏未开始，直接展开群组选择
            ShowGroupSelectionDropdown();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"选择Q群按钮点击事件处理失败");
            MessageBox.Show($@"操作失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 处理飞单操作
    /// </summary>
    /// <param name="issue">期号</param>
    private async Task ProcessFlightOrderAsync(string issue)
    {
        try
        {
            _logger.LogInformation(@"开始处理飞单操作，期号: {Issue}", issue);

            // 获取当前期的所有有效投注订单
            var betOrders = await _betRecordService.GetBetOrdersByIssueAsync(issue);
            var validBetOrders = betOrders.Where(b => b.Status != EnumBetOrderStatus.Cancelled).ToList();

            if (!validBetOrders.Any())
            {
                _logger.LogInformation(@"当前期没有有效投注，跳过飞单处理，期号: {Issue}", issue);
                return;
            }

            // 按投注项目汇总投注金额
            var playItemSummary = validBetOrders
                .GroupBy(b => b.PlayItem)
                .ToDictionary(g => g.Key, g => g.Sum(b => b.Amount));

            _logger.LogInformation(@"投注项目汇总完成，期号: {Issue}, 项目数: {Count}", issue, playItemSummary.Count);

            // 检查是否开启了对冲功能
            var isHedgeEnabled = await _systemSettingService.GetSettingValueAsync<bool>("开启对冲", false);

            Dictionary<string, decimal> finalBetData;
            if (isHedgeEnabled)
            {
                // 计算对冲后的投注数据
                finalBetData = CalculateHedgedBetData(playItemSummary);
                _logger.LogInformation(@"对冲计算完成，期号: {Issue}, 对冲后项目数: {Count}", issue, finalBetData.Count);
            }
            else
            {
                // 不对冲，直接使用原始汇总数据
                finalBetData = playItemSummary;
                _logger.LogInformation(@"未开启对冲功能，使用原始投注数据，期号: {Issue}", issue);
            }

            // 过滤掉金额为0的项目
            finalBetData = finalBetData.Where(kvp => kvp.Value > 0).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            if (finalBetData.Any())
            {
                // 生成飞单数据并提交到第三方平台
                var flightOrderData = GenerateFlightOrderData(issue, finalBetData);
                await SubmitFlightOrderAsync(issue, flightOrderData);
            }
            else
            {
                _logger.LogInformation(@"对冲后没有需要飞单的数据，期号: {Issue}", issue);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"处理飞单操作时发生异常，期号: {Issue}", issue);
        }
    }

    /// <summary>
    /// 生成投注统计消息
    /// </summary>
    /// <param name="issue">期号</param>
    /// <returns>格式化的投注统计消息</returns>
    private async Task<string> GenerateBetSummaryMessageAsync(string issue)
    {
        try
        {
            // 获取当前期的所有投注订单
            var betOrders = await _betRecordService.GetBetOrdersByIssueAsync(issue);

            // 过滤掉已取消的订单
            var validBetOrders = betOrders.Where(b => b.Status != EnumBetOrderStatus.Cancelled).ToList();

            if (!validBetOrders.Any())
            {
                return string.Empty; // 没有有效投注，不发送消息
            }

            // 获取所有会员信息
            var memberAccounts = validBetOrders.Select(b => b.Account).Distinct().ToList();
            var members = new Dictionary<string, Member>();

            foreach (var account in memberAccounts)
            {
                var member = await _memberService.GetMemberAsync(account);
                if (member != null)
                {
                    members[account] = member;
                }
            }

            // 按会员分组统计投注
            var memberBetGroups = validBetOrders
                .GroupBy(b => b.Account)
                .OrderBy(g => g.Key) // 按账号排序
                .ToList();

            var messageBuilder = new StringBuilder();
            messageBuilder.Append("——————————" + "\n");

            foreach (var memberGroup in memberBetGroups)
            {
                var account = memberGroup.Key;
                var member = members.GetValueOrDefault(account);

                // 获取会员昵称和余额
                var nickName = member?.NickName ?? account;
                var balance = member?.Balance ?? 0;

                // 按投注项目分组统计金额
                var playItemGroups = memberGroup
                    .GroupBy(b => b.PlayItem)
                    .Select(g => new { PlayItem = g.Key, TotalAmount = g.Sum(b => b.Amount) })
                    .OrderBy(g => g.PlayItem)
                    .ToList();

                // 构建会员投注信息
                messageBuilder.Append($"♦{nickName}:({balance})\n");

                var betItems = playItemGroups.Select(g => $"{g.PlayItem}/{g.TotalAmount}");
                messageBuilder.AppendLine(string.Join(" ", betItems));
            }

            messageBuilder.Remove(messageBuilder.Length - 2, 2);
            messageBuilder.Append("——————————\n");
            messageBuilder.Append($"停止答题[{issue}]期");

            return messageBuilder.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"生成投注统计消息失败，期号: {Issue}", issue);
            return string.Empty;
        }
    }

    /// <summary>
    /// 计算对冲后的投注数据
    /// </summary>
    /// <param name="playItemSummary">按投注项目汇总的投注金额</param>
    /// <returns>对冲后的投注数据</returns>
    private Dictionary<string, decimal> CalculateHedgedBetData(Dictionary<string, decimal> playItemSummary)
    {
        try
        {
            var hedgedData = new Dictionary<string, decimal>(playItemSummary);

            // 定义对冲规则：相互对冲的投注项目组（基于台湾宾果3番摊游戏规则）
            var hedgeGroups = new[]
            {
                // 单双对冲
                new[] { "单", "双" },

                // 正码对冲（1正与3正对冲，2正与4正对冲）
                new[] { "1正", "3正" },
                new[] { "2正", "4正" },

                // 番码完全对冲（四个号码互相对冲）
                new[] { "1番", "2番", "3番", "4番" },

                // 角码对冲（相对的角码对冲）
                new[] { "12角", "34角" },
                new[] { "23角", "14角" },

                // 念码对冲（基于开奖结果的对冲关系）
                // 1念X 与 3念X 对冲（因为1正与3正对冲）
                new[] { "1念2", "3念2" },
                new[] { "1念3", "3念1" },
                new[] { "1念4", "3念4" },

                // 2念X 与 4念X 对冲（因为2正与4正对冲）
                new[] { "2念1", "4念1" },
                new[] { "2念3", "4念3" },
                new[] { "2念4", "4念2" }
            };

            foreach (var hedgeGroup in hedgeGroups)
            {
                // 获取当前对冲组中所有项目的投注金额
                var groupAmounts = hedgeGroup
                    .Where(item => hedgedData.ContainsKey(item))
                    .ToDictionary(item => item, item => hedgedData[item]);

                if (groupAmounts.Count <= 1)
                {
                    continue; // 对冲组中只有一个或没有项目，无需对冲
                }

                // 计算对冲
                var maxAmount = groupAmounts.Values.Max();
                var maxItem = groupAmounts.First(kvp => kvp.Value == maxAmount).Key;

                // 计算其他项目的对冲金额
                foreach (var item in groupAmounts.Keys)
                {
                    if (item == maxItem)
                    {
                        // 最大金额项目减去其他项目的总和
                        var otherAmountsSum = groupAmounts.Where(kvp => kvp.Key != maxItem).Sum(kvp => kvp.Value);
                        hedgedData[item] = maxAmount - otherAmountsSum;
                    }
                    else
                    {
                        // 其他项目被完全对冲
                        hedgedData[item] = 0;
                    }
                }

                _logger.LogDebug(@"对冲组 [{Group}] 计算完成，最大项目: {MaxItem}, 对冲后金额: {Amount}",
                    string.Join(",", hedgeGroup), maxItem, hedgedData[maxItem]);
            }

            return hedgedData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"计算对冲数据时发生异常");
            return playItemSummary; // 异常时返回原始数据
        }
    }

    /// <summary>
    /// 生成飞单数据
    /// </summary>
    /// <param name="issue">期号</param>
    /// <param name="betData">投注数据</param>
    /// <returns>格式化的飞单数据（JSON数组格式）</returns>
    private string GenerateFlightOrderData(string issue, Dictionary<string, decimal> betData)
    {
        try
        {
            // 生成飞单数据列表
            var flightOrderList = new List<object>();
            int idCounter = 1;

            foreach (var item in betData.OrderBy(kvp => kvp.Key))
            {
                var flightOrderItem = new
                {
                    Id = idCounter++,
                    BetLottery = EnumBetLottery.台湾宾果3,
                    Issue = issue,
                    Content = item.Key,
                    ToBetBalance = (int)item.Value  // 转换为整数，去除小数部分
                };

                flightOrderList.Add(flightOrderItem);
            }

            var jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(flightOrderList, Newtonsoft.Json.Formatting.Indented);

            _logger.LogDebug(@"生成飞单数据完成，期号: {Issue}, 数据长度: {Length}, 投注项目数: {ItemCount}, 总金额: {TotalAmount}（已转换为整数）",
                issue, jsonString.Length, betData.Count, betData.Values.Sum());

            return jsonString;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"生成飞单数据时发生异常，期号: {Issue}", issue);
            return string.Empty;
        }
    }

    /// <summary>
    /// 提交飞单数据到第三方平台
    /// </summary>
    /// <param name="issue">期号</param>
    /// <param name="flightOrderData">飞单数据</param>
    private async Task SubmitFlightOrderAsync(string issue, string flightOrderData)
    {
        try
        {
            if (string.IsNullOrEmpty(flightOrderData))
            {
                _logger.LogWarning(@"飞单数据为空，跳过提交，期号: {Issue}", issue);
                return;
            }

            _logger.LogInformation(@"开始提交飞单数据到第三方平台，期号: {Issue}", issue);

            // 更新投注订单的飞单状态为"飞单中"
            await _betRecordService.UpdateBetOrderFlightStatusByIssueAsync(
                issue, EnumFlightOrderStatus.Processing, "开始提交飞单数据到第三方平台");

            // 立即刷新当前期投注数据显示
            _ = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(500); // 稍微延迟确保数据库更新完成
                    await LoadCurrentIssueBetOrdersAsync(true);

                    // 强制更新UI
                    if (InvokeRequired)
                    {
                        Invoke(() =>
                        {
                            _currentIssueBetBindingSource.DataSource = _currentIssueBetViewModels.ToList();
                            _logger.LogDebug(@"飞单状态更新后强制刷新UI完成");
                        });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, @"飞单状态更新后刷新UI失败");
                }
            });

            // 获取平台地址并构建飞单接口URL
            var platformHost = await _systemSettingService.GetSettingValueAsync<string>("PlatformHost", "http://127.0.0.1:6000");
            var flightOrderUrl = $"{platformHost}/ToBet/"; // 飞单提交接口

            _logger.LogInformation(@"飞单接口地址: {Url}", flightOrderUrl);
            _logger.LogDebug(@"飞单数据内容: {Data}", flightOrderData);
            _logger.LogInformation(@"开始发送飞单请求，超时时间: 15秒");

            // 使用 Flurl.Http 调用第三方平台飞单API
            var response = await flightOrderUrl
                .WithTimeout(TimeSpan.FromSeconds(15))
                .PostStringAsync(flightOrderData)
                .ReceiveString();

            // 使用 Flurl.Http 的响应是字符串类型
            var responseContent = response;
            _logger.LogInformation(@"飞单数据提交成功，期号: {Issue}, 响应: {Response}", issue, responseContent);

            // 检查响应内容是否包含"成功"字样
            if (responseContent.Contains("成功"))
            {
                // 更新投注订单的飞单状态为成功
                var updateCount = await _betRecordService.UpdateBetOrderFlightStatusByIssueAsync(
                    issue, EnumFlightOrderStatus.Success, "第三方平台飞单成功");

                _logger.LogInformation(@"飞单成功，期号: {Issue}, 更新了 {Count} 条投注记录的飞单状态", issue, updateCount);

                // 立即刷新UI显示飞单成功状态
                await RefreshCurrentIssueBetDataAsync();
            }
            else
            {
                // 响应成功但内容不包含"成功"，启动轮询线程
                _logger.LogWarning(@"飞单响应异常，期号: {Issue}, 响应内容: {Response}, 启动轮询线程检查飞单结果",
                    issue, responseContent);

                // 启动独立线程轮询飞单结果
                _ = Task.Run(async () => await PollFlightOrderResultAsync(issue));
            }
        }
        catch (FlurlHttpTimeoutException timeoutEx)
        {
            _logger.LogError(timeoutEx, @"飞单API请求超时，期号: {Issue}", issue);

            // 请求超时，启动轮询线程
            _logger.LogInformation(@"请求超时，期号: {Issue}, 启动轮询线程检查飞单结果", issue);
            _ = Task.Run(async () => await PollFlightOrderResultAsync(issue));
        }
        catch (FlurlHttpException flurlEx)
        {
            _logger.LogError(flurlEx, @"飞单API请求异常，期号: {Issue}, 状态码: {StatusCode}", issue, flurlEx.StatusCode);

            // HTTP异常，启动轮询线程
            _logger.LogInformation(@"HTTP异常，期号: {Issue}, 启动轮询线程检查飞单结果", issue);
            _ = Task.Run(async () => await PollFlightOrderResultAsync(issue));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"提交飞单数据时发生异常，期号: {Issue}", issue);

            // 其他异常，启动轮询线程
            _logger.LogInformation(@"系统异常，期号: {Issue}, 启动轮询线程检查飞单结果", issue);
            _ = Task.Run(async () => await PollFlightOrderResultAsync(issue));
        }
    }

    /// <summary>
    /// 轮询飞单结果
    /// </summary>
    /// <param name="issue">期号</param>
    private async Task PollFlightOrderResultAsync(string issue)
    {
        const int maxRetries = 10; // 最大轮询次数
        const int intervalSeconds = 2; // 轮询间隔（秒）

        try
        {
            _logger.LogInformation(@"开始轮询飞单结果，期号: {Issue}, 最大轮询次数: {MaxRetries}, 间隔: {Interval}秒",
                issue, maxRetries, intervalSeconds);

            // 获取平台地址
            var platformHost = await _systemSettingService.GetSettingValueAsync<string>("PlatformHost", "http://127.0.0.1:6000");
            var betResultUrl = $"{platformHost}/BetResult/"; // 飞单结果查询接口

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    _logger.LogDebug(@"第 {Attempt} 次轮询飞单结果，期号: {Issue}, 接口地址: {Url}",
                        attempt, issue, betResultUrl);

                    // 使用 Flurl.Http 发送POST请求查询飞单结果
                    List<BetHistory>? result = await betResultUrl
                        .WithTimeout(TimeSpan.FromSeconds(2))
                        .PostAsync()
                        .ReceiveJson<List<BetHistory>>();

                    _logger.LogDebug(@"轮询飞单结果响应，期号: {Issue}, 第 {Attempt} 次, 返回记录数: {Count}",
                        issue, attempt, result?.Count ?? 0);

                    // 检查是否有返回数据，如果有数据则认为飞单成功
                    if (result != null && result.Count > 0)
                    {
                        // 检查返回的投注记录中是否包含当前期号的数据
                        var currentIssueRecords = result.Where(r => r.Issue == issue).ToList();

                        if (currentIssueRecords.Count > 0)
                        {
                            // 找到当前期号的飞单结果，更新状态并退出轮询
                            var updateCount = await _betRecordService.UpdateBetOrderFlightStatusByIssueAsync(
                                issue, EnumFlightOrderStatus.Success, $"轮询确认飞单成功，返回{currentIssueRecords.Count}条记录");

                            _logger.LogInformation(@"轮询确认飞单成功，期号: {Issue}, 第 {Attempt} 次轮询, 返回{RecordCount}条记录, 更新了 {UpdateCount} 条投注记录",
                                issue, attempt, currentIssueRecords.Count, updateCount);

                            // 立即刷新UI显示飞单成功状态
                            await RefreshCurrentIssueBetDataAsync();
                            return; // 成功，退出轮询
                        }
                    }
                }
                catch (FlurlHttpTimeoutException ex)
                {
                    _logger.LogWarning(ex, @"轮询飞单结果超时，期号: {Issue}, 第 {Attempt} 次", issue, attempt);
                }
                catch (FlurlHttpException ex)
                {
                    _logger.LogWarning(ex, @"轮询飞单结果HTTP异常，期号: {Issue}, 第 {Attempt} 次, 状态码: {StatusCode}", issue, attempt, ex.StatusCode);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, @"轮询飞单结果异常，期号: {Issue}, 第 {Attempt} 次", issue, attempt);
                }

                // 如果不是最后一次尝试，等待指定间隔
                if (attempt < maxRetries)
                {
                    await Task.Delay(TimeSpan.FromSeconds(intervalSeconds));
                }
            }

            // 轮询完毕仍未获取到成功结果，标记为失败
            var failedUpdateCount = await _betRecordService.UpdateBetOrderFlightStatusByIssueAsync(
                issue, EnumFlightOrderStatus.Failed, $"轮询 {maxRetries} 次未获取到成功结果");

            _logger.LogWarning(@"轮询飞单结果失败，期号: {Issue}, 已轮询 {MaxRetries} 次, 更新了 {Count} 条投注记录为失败状态",
                issue, maxRetries, failedUpdateCount);

            // 立即刷新UI显示飞单失败状态
            await RefreshCurrentIssueBetDataAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"轮询飞单结果时发生异常，期号: {Issue}", issue);

            // 轮询异常，标记为失败
            try
            {
                var errorUpdateCount = await _betRecordService.UpdateBetOrderFlightStatusByIssueAsync(
                    issue, EnumFlightOrderStatus.Failed, $"轮询异常: {ex.Message}");

                _logger.LogInformation(@"轮询异常导致飞单失败，期号: {Issue}, 更新了 {Count} 条投注记录", issue, errorUpdateCount);
            }
            catch (Exception updateEx)
            {
                _logger.LogError(updateEx, @"更新轮询异常状态时发生错误，期号: {Issue}", issue);
            }
        }
    }

    /// <summary>
    /// 显示群组选择下拉列表
    /// </summary>
    private void ShowGroupSelectionDropdown()
    {
        try
        {
            // 检查是否有群组数据
            if (comboBox_WorkGroupId.Items.Count > 0)
            {
                // 模拟点击ComboBox展开下拉列表
                comboBox_WorkGroupId.DroppedDown = true;
                comboBox_WorkGroupId.Focus();

                _logger.LogDebug(@"已自动展开工作群组下拉列表，共 {Count} 个群组可选", comboBox_WorkGroupId.Items.Count);
            }
            else
            {
                // 如果没有群组数据，提示用户
                MessageBox.Show(@"暂无可用的群组信息，请确保聊天平台已连接并获取到群组数据。",
                    @"提示", MessageBoxButtons.OK, MessageBoxIcon.Information);

                _logger.LogWarning(@"尝试展开群组选择，但没有可用的群组数据");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"显示群组选择下拉列表失败");
            MessageBox.Show($@"展开群组列表失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    #endregion

    #region 数据刷新辅助方法

    /// <summary>
    /// 刷新当前期投注数据并更新UI
    /// </summary>
    private async Task RefreshCurrentIssueBetDataAsync()
    {
        try
        {
            _logger.LogInformation(@"开始强制刷新当前期投注数据，当前显示期号: {LastLoadedIssue}", _lastLoadedIssue);

            await LoadCurrentIssueBetOrdersAsync(true);

            // 强制更新UI
            if (InvokeRequired)
            {
                Invoke(() =>
                {
                    _currentIssueBetBindingSource.DataSource = _currentIssueBetViewModels.ToList();
                    _logger.LogInformation(@"当前期投注数据UI强制刷新完成，数据条数: {Count}", _currentIssueBetViewModels.Count);
                });
            }
            else
            {
                _currentIssueBetBindingSource.DataSource = _currentIssueBetViewModels.ToList();
                _logger.LogInformation(@"当前期投注数据UI强制刷新完成，数据条数: {Count}", _currentIssueBetViewModels.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"刷新当前期投注数据失败");
        }
    }

    #endregion

    #region 加载数据方法

    /// <summary>
    /// 加载所有数据
    /// </summary>
    private async Task LoadAllDataAsync()
    {
        try
        {
            // 并行加载基础数据
            var memberTask = LoadMembersAsync();
            var depositTask = LoadDepositRequestsAsync();
            var withdrawTask = LoadWithdrawRequestsAsync();
            var systemSettingsTask = LoadSystemSettingsDataAsync();

            await Task.WhenAll(memberTask, depositTask, withdrawTask, systemSettingsTask).ConfigureAwait(false);

            // 单独加载当前期投注数据（可能需要基础数据）
            await LoadCurrentIssueBetOrdersAsync().ConfigureAwait(false);

            // 在UI线程中绑定数据
            if (InvokeRequired)
            {
                Invoke(BindAllData);
            }
            else
            {
                BindAllData();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"加载数据失败");
            throw;
        }
    }

    /// <summary>
    /// 加载会员数据
    /// </summary>
    private async Task LoadMembersAsync()
    {
        try
        {
            // 从数据库加载会员数据
            _members = await _memberService.GetMembersAsync().ConfigureAwait(false);

            // 创建会员视图模型，包含计算字段
            _memberViewModels = [];
            foreach (var member in _members)
            {
                // 计算未结算积分（当前期的投注总额）
                var unsettledAmount = await CalculateUnsettledAmountAsync(member.Account).ConfigureAwait(false);

                // 计算上期盈亏（最后一期的盈亏）
                var lastIssueProfitLoss = await CalculateLastIssueProfitLossAsync(member.Account).ConfigureAwait(false);

                // 计算总上分和总下分
                var (totalDeposit, totalWithdraw) = await CalculateTotalDepositWithdrawAsync(member.Account).ConfigureAwait(false);

                _memberViewModels.Add(new MemberViewModel
                {
                    Id = member.Id,
                    Account = member.Account,
                    NickName = member.NickName,

                    Balance = member.Balance,
                    UnsettledAmount = unsettledAmount,
                    LastIssueProfitLoss = lastIssueProfitLoss,
                    RebatePercent = member.RebatePercent,
                    AgentRebatePercent = member.AgentRebatePercent,
                    AgentName = string.IsNullOrWhiteSpace(member.AgentName) ? null : member.AgentName,
                    UserType = member.UserType,
                    IsEnabled = !member.Deleted,
                    CreatedTime = member.CreateTime,
                    TotalDeposit = totalDeposit,
                    TotalWithdraw = totalWithdraw
                });
            }

            // UI层面重新排序：先按真假人排序（真人优先），再按积分从高到低排序
            _memberViewModels = _memberViewModels
                .OrderBy(m => m.UserType == @"假人" ? 1 : 0) // 真人(0)在前，假人(1)在后
                .ThenByDescending(m => m.Balance) // 积分从高到低
                .ToList();

            _logger.LogDebug(@"加载了 {Count} 个会员记录，创建了 {ViewModelCount} 个视图模型，已按真假人和积分重新排序",
                _members.Count, _memberViewModels.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"加载会员数据失败");
            _members = [];
            _memberViewModels = [];
        }
    }

    /// <summary>
    /// 加载上分申请数据
    /// </summary>
    private async Task LoadDepositRequestsAsync()
    {
        try
        {
            // 从数据库加载上分申请数据
            _depositRequests = await _depositWithdrawRecordService.GetPendingDepositRequestsAsync().ConfigureAwait(false);

            // 创建包含昵称的视图模型
            _depositRequestViewModels = [];
            foreach (var request in _depositRequests)
            {
                var member = _members.FirstOrDefault(m => m.Account == request.Account);
                _depositRequestViewModels.Add(new DepositRequestViewModel
                {
                    Id = request.Id,
                    Account = request.Account,
                    NickName = member?.NickName ?? request.Account,
                    Amount = request.Amount,
                    Status = request.Status,
                    CreatedTime = request.CreatedTime
                });
            }

            _logger.LogDebug(@"加载了 {Count} 条上分申请", _depositRequests.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"加载上分申请失败");
            _depositRequests = [];
            _depositRequestViewModels = [];
        }
    }

    /// <summary>
    /// 加载下分申请数据
    /// </summary>
    private async Task LoadWithdrawRequestsAsync()
    {
        try
        {
            // 从数据库加载下分申请数据
            _withdrawRequests = await _depositWithdrawRecordService.GetPendingWithdrawRequestsAsync().ConfigureAwait(false);

            // 创建包含昵称的视图模型
            _withdrawRequestViewModels = [];
            foreach (var request in _withdrawRequests)
            {
                var member = _members.FirstOrDefault(m => m.Account == request.Account);
                _withdrawRequestViewModels.Add(new WithdrawRequestViewModel
                {
                    Id = request.Id,
                    Account = request.Account,
                    NickName = member?.NickName ?? request.Account,
                    Amount = request.Amount,
                    Status = request.Status,
                    CreatedTime = request.CreatedTime
                });
            }

            _logger.LogDebug(@"加载了 {Count} 条下分申请", _withdrawRequests.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"加载下分申请失败");
            _withdrawRequests = [];
            _withdrawRequestViewModels = [];
        }
    }

    /// <summary>
    /// 加载当前期投注数据（全量加载）
    /// </summary>
    private async Task LoadCurrentIssueBetOrdersAsync()
    {
        await LoadCurrentIssueBetOrdersAsync(false).ConfigureAwait(false);
    }

    /// <summary>
    /// 加载当前期投注数据
    /// </summary>
    /// <param name="isIncremental">是否为增量更新</param>
    private async Task LoadCurrentIssueBetOrdersAsync(bool isIncremental)
    {
        try
        {
            // 获取当前期号
            var currentIssueTime = _issueTimeService.GetCurrentCachedIssueTime();
            if (currentIssueTime == null || string.IsNullOrWhiteSpace(currentIssueTime.Issue))
            {
                _logger.LogWarning(@"当前期号为空，无法加载投注数据");
                if (_currentIssueBetViewModels.Count > 0)
                {
                    _currentIssueBetViewModels = [];
                    _lastLoadedIssue = string.Empty;
                    _lastCurrentIssueBetUpdateTime = DateTime.MinValue;
                }

                return;
            }

            var currentIssue = currentIssueTime.Issue;
            var now = DateTime.Now;
            var isCurrentIssueOpen = now >= currentIssueTime.OpenTime && now < currentIssueTime.CloseTime;

            // 判断是否需要清空数据并重新加载
            bool shouldClearAndReload = false;

            if (currentIssue != _lastLoadedIssue)
            {
                if (string.IsNullOrEmpty(_lastLoadedIssue))
                {
                    // 首次加载
                    shouldClearAndReload = true;
                    _logger.LogInformation(@"首次加载当前期投注数据: {Issue}", currentIssue);
                }
                else if (isCurrentIssueOpen)
                {
                    // 新期号已经开盘，清空上期数据，加载新期数据
                    shouldClearAndReload = true;
                    _logger.LogInformation(@"新期号已开盘，清空上期数据并加载新期数据: {OldIssue} -> {NewIssue}", _lastLoadedIssue, currentIssue);
                }
                else
                {
                    // 新期号还未开盘，继续显示上期数据，但仍需检查上期数据的更新（如飞单状态）
                    _logger.LogInformation(@"新期号未开盘，继续显示上期数据: {LastIssue}, 新期号: {CurrentIssue}, 开盘时间: {OpenTime}",
                        _lastLoadedIssue, currentIssue, currentIssueTime.OpenTime.ToString("HH:mm:ss"));

                    // 如果是增量更新，仍需要检查上期数据的状态变化（如飞单状态更新）
                    if (isIncremental && !string.IsNullOrEmpty(_lastLoadedIssue))
                    {
                        _logger.LogDebug(@"检查上期数据更新: {LastIssue}", _lastLoadedIssue);
                        // 使用上期期号进行增量更新
                        currentIssue = _lastLoadedIssue;
                    }
                    else
                    {
                        return; // 非增量更新时直接返回，保持当前显示的数据
                    }
                }
            }

            if (shouldClearAndReload)
            {
                isIncremental = false;
            }

            List<BetOrder> betOrders;

            if (isIncremental && _lastCurrentIssueBetUpdateTime > DateTime.MinValue && currentIssue == _lastLoadedIssue)
            {
                // 增量加载：只在同一期号且当前期开盘时才进行增量更新
                if (isCurrentIssueOpen)
                {
                    betOrders = await _betRecordService.GetCurrentIssueBetOrdersIncrementalAsync(currentIssue, _lastCurrentIssueBetUpdateTime).ConfigureAwait(false);
                    _logger.LogDebug(@"增量加载当前期投注数据，期号: {Issue}, 最后更新时间: {LastUpdateTime}, 新增数量: {Count}",
                        currentIssue, _lastCurrentIssueBetUpdateTime, betOrders.Count);
                }
                else
                {
                    // 当前期已封盘，但仍需要检查飞单状态等重要信息的更新
                    betOrders = await _betRecordService.GetCurrentIssueBetOrdersIncrementalAsync(currentIssue, _lastCurrentIssueBetUpdateTime).ConfigureAwait(false);
                    _logger.LogDebug(@"当前期已封盘，检查飞单状态等更新，期号: {Issue}, 封盘时间: {CloseTime}, 更新数量: {Count}",
                        currentIssue, currentIssueTime.CloseTime.ToString("HH:mm:ss"), betOrders.Count);

                    // 如果没有更新，直接返回
                    if (betOrders.Count == 0)
                    {
                        return;
                    }
                }
            }
            else
            {
                // 全量加载：获取当前期的所有投注订单
                betOrders = await _betRecordService.GetCurrentIssueBetOrdersAsync(currentIssue).ConfigureAwait(false);
                _currentIssueBetViewModels = [];
                _logger.LogDebug(@"全量加载当前期投注数据，期号: {Issue}, 总数量: {Count}", currentIssue, betOrders.Count);
            }

            // 处理新的投注订单数据
            foreach (var order in betOrders)
            {
                var member = _members.FirstOrDefault(m => m.Account == order.Account);
                var viewModel = new CurrentIssueBetViewModel
                {
                    Id = order.Id,
                    Issue = order.Issue,
                    Account = order.Account,
                    NickName = !string.IsNullOrWhiteSpace(order.NickName) ? order.NickName : (member?.NickName ?? order.Account),
                    PlayItem = order.PlayItem,
                    Amount = order.Amount,
                    EnumFlightStatus = order.EnumFlightStatus,
                    CreatedTime = order.CreatedTime,
                    FlightTime = order.FlightTime
                };

                if (isIncremental)
                {
                    // 增量更新：检查是否已存在，存在则更新，不存在则添加
                    var existingIndex = _currentIssueBetViewModels.FindIndex(x => x.Id == order.Id);
                    if (existingIndex >= 0)
                    {
                        // 更新现有记录
                        _currentIssueBetViewModels[existingIndex] = viewModel;
                        _logger.LogDebug(@"更新投注记录，ID: {Id}, 状态: {Status}", order.Id, order.EnumFlightStatus);
                    }
                    else
                    {
                        // 添加新记录
                        _currentIssueBetViewModels.Add(viewModel);
                        _logger.LogDebug(@"新增投注记录，ID: {Id}, 用户: {Account}, 金额: {Amount}", order.Id, order.Account, order.Amount);
                    }
                }
                else
                {
                    // 全量加载：直接添加
                    _currentIssueBetViewModels.Add(viewModel);
                }
            }

            // 按创建时间倒序排列（最新的在前面）
            _currentIssueBetViewModels = _currentIssueBetViewModels.OrderByDescending(x => x.CreatedTime).ToList();

            // 更新最后加载的期号和时间
            _lastLoadedIssue = currentIssue;
            _lastCurrentIssueBetUpdateTime = DateTime.Now;

            _logger.LogDebug(@"加载当前期投注数据完成，期号: {Issue}, 数量: {Count}, 更新类型: {UpdateType}",
                currentIssue, _currentIssueBetViewModels.Count, isIncremental ? "增量" : "全量");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"加载当前期投注数据失败，更新类型: {UpdateType}", isIncremental ? "增量" : "全量");
            if (!isIncremental)
            {
                _currentIssueBetViewModels = [];
                _lastLoadedIssue = string.Empty;
                _lastCurrentIssueBetUpdateTime = DateTime.MinValue;
            }
        }
    }


    #region 主界面绑定所有数据到DataGridView

    /// <summary>
    /// 主界面绑定所有数据到DataGridView
    /// 使用智能数据变化检测，只有在数据真正发生变化时才更新UI
    /// 方案A：最简单的选择清除方案，避免复杂的状态管理
    /// </summary>
    private void BindAllData()
    {
        try
        {
            // 暂停布局更新，减少闪烁
            SuspendLayout();
            dgvMembers.SuspendLayout();
            dgvDepositRequests.SuspendLayout();
            dgvWithdrawRequests.SuspendLayout();
            dgvCurrentIssueBets.SuspendLayout();

            try
            {
                // 智能更新数据源，只有在数据真正发生变化时才刷新UI
                var memberChanged = UpdateBindingSource(_memberBindingSource, _memberViewModels, ref _lastMemberViewModelsHash, dgvMembers);
                var depositChanged = UpdateBindingSource(_depositBindingSource, _depositRequestViewModels, ref _lastDepositRequestsHash, dgvDepositRequests);
                var withdrawChanged = UpdateBindingSource(_withdrawBindingSource, _withdrawRequestViewModels, ref _lastWithdrawRequestsHash, dgvWithdrawRequests);
                var currentIssueBetChanged = UpdateBindingSource(_currentIssueBetBindingSource, _currentIssueBetViewModels, ref _lastCurrentIssueBetHash, dgvCurrentIssueBets);
                var anyDataChanged = memberChanged || depositChanged || withdrawChanged || currentIssueBetChanged;

                _logger.LogDebug(@"数据检查完成 - 会员: {MemberCount}({MemberChanged}), 上分申请: {DepositCount}({DepositChanged}), 下分申请: {WithdrawCount}({WithdrawChanged}), 当前期投注: {CurrentIssueBetCount}({CurrentIssueBetChanged}), 总变化: {AnyChanged}",
                    _members.Count, memberChanged ? "变化" : "无变化",
                    _depositRequests.Count, depositChanged ? "变化" : "无变化",
                    _withdrawRequests.Count, withdrawChanged ? "变化" : "无变化",
                    _currentIssueBetViewModels.Count, currentIssueBetChanged ? "变化" : "无变化",
                    anyDataChanged ? "是" : "否");
            }
            finally
            {
                // 恢复布局更新
                dgvCurrentIssueBets.ResumeLayout();
                dgvWithdrawRequests.ResumeLayout();
                dgvDepositRequests.ResumeLayout();
                dgvMembers.ResumeLayout();
                ResumeLayout();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"数据绑定失败");
            throw;
        }
    }

    #endregion

    #endregion

    #region 主界面的表格点击事件

    /// <summary>
    /// 会员表格按钮点击事件
    /// </summary>
    private async void DgvMembers_CellClick(object? sender, DataGridViewCellEventArgs e)
    {
        try
        {
            if (e.RowIndex < 0 || e.ColumnIndex < 0) return;

            if (sender is not DataGridView grid) return;
            var columnName = grid.Columns[e.ColumnIndex].Name;

            if (columnName == @"DeleteButton")
            {
                try
                {
                    // 获取选中的会员数据（修正类型转换）
                    var memberViewModel = (MemberViewModel)grid.Rows[e.RowIndex].DataBoundItem;
                    if (memberViewModel == null) return;

                    // 确认删除操作
                    var result = MessageBox.Show(
                        $@"确定要删除会员 '{memberViewModel.Account}' ({memberViewModel.NickName}) 吗？{Environment.NewLine}此操作将进行软删除，可以通过管理员恢复。",
                        @"确认删除",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // 执行软删除
                        var deleteResult = await _memberService.DeleteMemberAsync(memberViewModel.Account).ConfigureAwait(false);

                        if (deleteResult > 0)
                        {
                            _logger.LogInformation(@"会员删除成功，账号: {Account}", memberViewModel.Account);

                            // 刷新数据
                            await LoadMembersAsync().ConfigureAwait(false);

                            // 更新UI
                            BeginInvoke(() => { BindAllData(); });
                        }
                        else
                        {
                            _logger.LogWarning(@"会员删除失败，账号: {Account}", memberViewModel.Account);
                            MessageBox.Show(@"删除失败，请重试。", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, @"删除会员时发生错误");
                    MessageBox.Show($@"删除失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    await Task.Delay(0);
                }
            }
            else if (columnName == @"UserType")
            {
                try
                {
                    // 获取选中的会员数据
                    var memberViewModel = (MemberViewModel)grid.Rows[e.RowIndex].DataBoundItem;
                    if (memberViewModel == null) return;

                    // 显示真假人切换确认对话框
                    await ShowUserTypeToggleDialogAsync(memberViewModel);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, @"切换真假人类型时发生错误");
                    MessageBox.Show($@"切换用户类型失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"会员表格按钮点击事件失败");
        }
    }

    /// <summary>
    /// 会员表格双击事件 - 编辑昵称
    /// </summary>
    private async void DgvMembers_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
    {
        try
        {
            if (e.RowIndex < 0 || e.ColumnIndex < 0) return;

            if (sender is not DataGridView grid) return;
            var columnName = grid.Columns[e.ColumnIndex].Name;

            // 获取选中的会员数据
            var memberViewModel = (MemberViewModel)grid.Rows[e.RowIndex].DataBoundItem;
            if (memberViewModel == null) return;

            // 根据列名决定编辑类型
            switch (columnName)
            {
                case @"NickName":
                    // 显示编辑昵称对话框
                    await ShowEditNickNameDialogAsync(memberViewModel);
                    break;
                case @"RebatePercent":
                    // 显示编辑回水比例对话框
                    await ShowEditRebatePercentDialogAsync(memberViewModel);
                    break;
                case @"Balance":
                    // 显示积分操作菜单（加分/减分）
                    ShowBalanceOperationMenu(memberViewModel, grid, e.RowIndex, e.ColumnIndex);
                    break;
                case @"Account":
                    // 显示拉手操作菜单（设置拉手/移除拉手）
                    ShowAgentOperationMenu(memberViewModel, grid, e.RowIndex, e.ColumnIndex);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"会员表格双击事件失败");
        }
    }

    /// <summary>
    /// 显示编辑昵称对话框
    /// </summary>
    /// <param name="memberViewModel">要编辑的会员视图模型</param>
    private async Task ShowEditNickNameDialogAsync(MemberViewModel memberViewModel)
    {
        try
        {
            // 创建输入对话框
            using var inputDialog = new Form
            {
                Text = @"编辑会员昵称",
                Size = new Size(400, 180),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                ShowInTaskbar = false
            };

            // 创建标签
            var label = new Label
            {
                Text = $@"会员账号: {memberViewModel.Account}",
                Location = new Point(20, 20),
                Size = new Size(350, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建文本框
            var textBox = new TextBox
            {
                Text = memberViewModel.NickName,
                Location = new Point(20, 50),
                Size = new Size(350, 25),
                Font = new Font(@"Microsoft YaHei UI", 9F),
                MaxLength = 200 // 限制最大长度
            };

            // 创建确定按钮
            var btnOk = new Button
            {
                Text = @"确定",
                Location = new Point(215, 90),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK,
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建取消按钮
            var btnCancel = new Button
            {
                Text = @"取消",
                Location = new Point(295, 90),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel,
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 添加控件到对话框
            inputDialog.Controls.AddRange([label, textBox, btnOk, btnCancel]);
            inputDialog.AcceptButton = btnOk;
            inputDialog.CancelButton = btnCancel;

            // 选中文本框内容，方便用户编辑
            textBox.SelectAll();
            textBox.Focus();

            // 显示对话框
            if (inputDialog.ShowDialog(this) == DialogResult.OK)
            {
                var newNickName = textBox.Text.Trim();

                // 验证输入
                if (string.IsNullOrWhiteSpace(newNickName))
                {
                    MessageBox.Show(@"昵称不能为空！", @"输入错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (newNickName == memberViewModel.NickName)
                {
                    // 昵称没有变化，不需要更新
                    return;
                }

                // 更新会员昵称
                await UpdateMemberNickNameAsync(memberViewModel.Account, newNickName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"显示编辑昵称对话框失败");
            MessageBox.Show($@"编辑昵称失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 显示编辑回水比例对话框
    /// </summary>
    /// <param name="memberViewModel">要编辑的会员视图模型</param>
    private async Task ShowEditRebatePercentDialogAsync(MemberViewModel memberViewModel)
    {
        try
        {
            // 创建输入对话框
            using var inputDialog = new Form
            {
                Text = @"编辑回水比例",
                Size = new Size(400, 200),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                ShowInTaskbar = false
            };

            // 创建标签
            var label = new Label
            {
                Text = $@"会员账号: {memberViewModel.Account} ({memberViewModel.NickName})",
                Location = new Point(20, 20),
                Size = new Size(350, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建提示标签
            var hintLabel = new Label
            {
                Text = @"回水比例 (0.0 - 20.0):",
                Location = new Point(20, 50),
                Size = new Size(150, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建数值输入框
            var numericUpDown = new NumericUpDown
            {
                Value = memberViewModel.RebatePercent,
                Location = new Point(180, 48),
                Size = new Size(190, 25),
                Font = new Font(@"Microsoft YaHei UI", 9F),
                Minimum = 0.0m,
                Maximum = 20.0m,
                DecimalPlaces = 1,
                Increment = 0.1m
            };

            // 创建确定按钮
            var btnOk = new Button
            {
                Text = @"确定",
                Location = new Point(215, 110),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK,
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建取消按钮
            var btnCancel = new Button
            {
                Text = @"取消",
                Location = new Point(295, 110),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel,
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 添加控件到对话框
            inputDialog.Controls.AddRange([label, hintLabel, numericUpDown, btnOk, btnCancel]);
            inputDialog.AcceptButton = btnOk;
            inputDialog.CancelButton = btnCancel;

            // 选中数值输入框
            numericUpDown.Focus();
            numericUpDown.Select(0, numericUpDown.Text.Length);

            // 显示对话框
            if (inputDialog.ShowDialog(this) == DialogResult.OK)
            {
                var newRebatePercent = numericUpDown.Value;

                if (newRebatePercent == memberViewModel.RebatePercent)
                {
                    // 回水比例没有变化，不需要更新
                    return;
                }

                // 更新会员回水比例
                await UpdateMemberRebatePercentAsync(memberViewModel.Account, newRebatePercent);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"显示编辑回水比例对话框失败");
            MessageBox.Show($@"编辑回水比例失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 显示积分操作菜单（加分/减分）
    /// </summary>
    /// <param name="memberViewModel">会员视图模型</param>
    /// <param name="grid">数据表格</param>
    /// <param name="rowIndex">行索引</param>
    /// <param name="columnIndex">列索引</param>
    private void ShowBalanceOperationMenu(MemberViewModel memberViewModel, DataGridView grid, int rowIndex, int columnIndex)
    {
        try
        {
            // 创建右键菜单
            var contextMenu = new ContextMenuStrip
            {
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 加分选项
            var addPointsItem = new ToolStripMenuItem
            {
                Text = @"💰 加分",
                Image = null,
                ImageScaling = ToolStripItemImageScaling.None
            };
            addPointsItem.Click += async (_, _) => await ShowAddPointsDialogAsync(memberViewModel);

            // 减分选项
            var deductPointsItem = new ToolStripMenuItem
            {
                Text = @"💸 减分",
                Image = null,
                ImageScaling = ToolStripItemImageScaling.None
            };
            deductPointsItem.Click += async (_, _) => await ShowDeductPointsDialogAsync(memberViewModel);

            // 添加菜单项
            contextMenu.Items.Add(addPointsItem);
            contextMenu.Items.Add(deductPointsItem);

            // 计算菜单显示位置（在单元格位置显示）
            var cellRect = grid.GetCellDisplayRectangle(columnIndex, rowIndex, false);
            var menuLocation = grid.PointToScreen(new Point(cellRect.X + cellRect.Width / 2, cellRect.Y + cellRect.Height / 2));

            // 显示菜单
            contextMenu.Show(menuLocation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"显示积分操作菜单失败");
            MessageBox.Show($@"显示操作菜单失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 显示加分对话框
    /// </summary>
    /// <param name="memberViewModel">要加分的会员视图模型</param>
    private async Task ShowAddPointsDialogAsync(MemberViewModel memberViewModel)
    {
        try
        {
            // 创建输入对话框
            using var inputDialog = new Form();
            inputDialog.Text = @"会员加分";
            inputDialog.Size = new Size(420, 220);
            inputDialog.StartPosition = FormStartPosition.CenterParent;
            inputDialog.FormBorderStyle = FormBorderStyle.FixedDialog;
            inputDialog.MaximizeBox = false;
            inputDialog.MinimizeBox = false;
            inputDialog.ShowInTaskbar = false;

            // 创建会员信息标签
            var memberLabel = new Label
            {
                Text = $@"会员账号: {memberViewModel.Account} ({memberViewModel.NickName})",
                Location = new Point(20, 20),
                Size = new Size(370, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建当前余额标签
            var balanceLabel = new Label
            {
                Text = $@"当前余额: {memberViewModel.Balance:F2}",
                Location = new Point(20, 45),
                Size = new Size(370, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F),
                ForeColor = Color.Blue
            };

            // 创建加分金额标签
            var amountLabel = new Label
            {
                Text = @"加分金额:",
                Location = new Point(20, 75),
                Size = new Size(80, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建金额输入框（只允许整数）
            var amountNumericUpDown = new NumericUpDown
            {
                Value = 0,
                Location = new Point(110, 73),
                Size = new Size(150, 25),
                Font = new Font(@"Microsoft YaHei UI", 9F),
                Minimum = 0m, // 最小值改为0
                Maximum = 999999m, // 最大值改为整数
                DecimalPlaces = 0, // 不允许小数位
                Increment = 1.0m
            };

            // 创建备注标签
            var remarkLabel = new Label
            {
                Text = @"备注:",
                Location = new Point(20, 105),
                Size = new Size(50, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建备注输入框
            var remarkTextBox = new TextBox
            {
                Text = @"管理员加分",
                Location = new Point(80, 103),
                Size = new Size(310, 25),
                Font = new Font(@"Microsoft YaHei UI", 9F),
                MaxLength = 200
            };

            // 创建确定按钮
            var btnOk = new Button
            {
                Text = @"确定加分",
                Location = new Point(235, 140),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK,
                Font = new Font(@"Microsoft YaHei UI", 9F),
                BackColor = Color.LightGreen
            };

            // 创建取消按钮
            var btnCancel = new Button
            {
                Text = @"取消",
                Location = new Point(315, 140),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel,
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 添加控件到对话框
            inputDialog.Controls.AddRange([
                memberLabel, balanceLabel, amountLabel, amountNumericUpDown,
                remarkLabel, remarkTextBox, btnOk, btnCancel
            ]);
            inputDialog.AcceptButton = btnOk;
            inputDialog.CancelButton = btnCancel;

            // 焦点设置到金额输入框
            amountNumericUpDown.Focus();
            amountNumericUpDown.Select(0, amountNumericUpDown.Text.Length);

            // 显示对话框
            if (inputDialog.ShowDialog(this) == DialogResult.OK)
            {
                var addAmount = amountNumericUpDown.Value;
                var remark = remarkTextBox.Text.Trim();

                if (addAmount <= 0)
                {
                    MessageBox.Show(@"加分金额必须大于0！", @"输入错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 验证是否为整数（虽然NumericUpDown已经限制了，但为了安全起见）
                if (addAmount != Math.Floor(addAmount))
                {
                    MessageBox.Show(@"加分金额必须为整数！", @"输入错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 执行加分操作
                await ProcessPointsOperationAsync(memberViewModel.Account, addAmount, remark, true);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"显示加分对话框失败");
            MessageBox.Show($@"显示加分对话框失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 显示减分对话框
    /// </summary>
    /// <param name="memberViewModel">要减分的会员视图模型</param>
    private async Task ShowDeductPointsDialogAsync(MemberViewModel memberViewModel)
    {
        try
        {
            // 创建输入对话框
            using var inputDialog = new Form();
            inputDialog.Text = @"会员减分";
            inputDialog.Size = new Size(420, 220);
            inputDialog.StartPosition = FormStartPosition.CenterParent;
            inputDialog.FormBorderStyle = FormBorderStyle.FixedDialog;
            inputDialog.MaximizeBox = false;
            inputDialog.MinimizeBox = false;
            inputDialog.ShowInTaskbar = false;

            // 创建会员信息标签
            var memberLabel = new Label
            {
                Text = $@"会员账号: {memberViewModel.Account} ({memberViewModel.NickName})",
                Location = new Point(20, 20),
                Size = new Size(370, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建当前余额标签
            var balanceLabel = new Label
            {
                Text = $@"当前余额: {memberViewModel.Balance:F2}",
                Location = new Point(20, 45),
                Size = new Size(370, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F),
                ForeColor = Color.Red
            };

            // 创建减分金额标签
            var amountLabel = new Label
            {
                Text = @"减分金额:",
                Location = new Point(20, 75),
                Size = new Size(80, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建金额输入框（只允许整数）
            var amountNumericUpDown = new NumericUpDown
            {
                Value = 0,
                Location = new Point(110, 73),
                Size = new Size(150, 25),
                Font = new Font(@"Microsoft YaHei UI", 9F),
                Minimum = 0m, // 最小值改为0
                Maximum = Math.Floor(memberViewModel.Balance), // 最大值为当前余额的整数部分
                DecimalPlaces = 0, // 不允许小数位
                Increment = 1.0m
            };

            // 创建备注标签
            var remarkLabel = new Label
            {
                Text = @"备注:",
                Location = new Point(20, 105),
                Size = new Size(50, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建备注输入框
            var remarkTextBox = new TextBox
            {
                Text = @"管理员减分",
                Location = new Point(80, 103),
                Size = new Size(310, 25),
                Font = new Font(@"Microsoft YaHei UI", 9F),
                MaxLength = 200
            };

            // 创建确定按钮
            var btnOk = new Button
            {
                Text = @"确定减分",
                Location = new Point(235, 140),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK,
                Font = new Font(@"Microsoft YaHei UI", 9F),
                BackColor = Color.LightCoral
            };

            // 创建取消按钮
            var btnCancel = new Button
            {
                Text = @"取消",
                Location = new Point(315, 140),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel,
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 添加控件到对话框
            inputDialog.Controls.AddRange([
                memberLabel, balanceLabel, amountLabel, amountNumericUpDown,
                remarkLabel, remarkTextBox, btnOk, btnCancel
            ]);
            inputDialog.AcceptButton = btnOk;
            inputDialog.CancelButton = btnCancel;

            // 焦点设置到金额输入框
            amountNumericUpDown.Focus();
            amountNumericUpDown.Select(0, amountNumericUpDown.Text.Length);

            // 显示对话框
            if (inputDialog.ShowDialog(this) == DialogResult.OK)
            {
                var deductAmount = amountNumericUpDown.Value;
                var remark = remarkTextBox.Text.Trim();

                if (deductAmount <= 0)
                {
                    MessageBox.Show(@"减分金额必须大于0！", @"输入错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 验证是否为整数（虽然NumericUpDown已经限制了，但为了安全起见）
                if (deductAmount != Math.Floor(deductAmount))
                {
                    MessageBox.Show(@"减分金额必须为整数！", @"输入错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (deductAmount > memberViewModel.Balance)
                {
                    MessageBox.Show(@"减分金额不能超过当前余额！", @"输入错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 执行减分操作
                await ProcessPointsOperationAsync(memberViewModel.Account, deductAmount, remark, false);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"显示减分对话框失败");
            MessageBox.Show($@"显示减分对话框失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 显示拉手操作菜单（设置拉手/移除拉手）
    /// </summary>
    /// <param name="memberViewModel">会员视图模型</param>
    /// <param name="grid">数据表格</param>
    /// <param name="rowIndex">行索引</param>
    /// <param name="columnIndex">列索引</param>
    private void ShowAgentOperationMenu(MemberViewModel memberViewModel, DataGridView grid, int rowIndex, int columnIndex)
    {
        try
        {
            // 创建右键菜单
            var contextMenu = new ContextMenuStrip
            {
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 设置拉手选项
            var setAgentItem = new ToolStripMenuItem
            {
                Text = @"👥 设置拉手",
                Image = null,
                ImageScaling = ToolStripItemImageScaling.None
            };
            setAgentItem.Click += async (_, _) => await ShowSetAgentDialogAsync(memberViewModel);

            // 移除拉手选项
            var removeAgentItem = new ToolStripMenuItem
            {
                Text = @"❌ 移除拉手",
                Image = null,
                ImageScaling = ToolStripItemImageScaling.None,
                Enabled = !string.IsNullOrWhiteSpace(memberViewModel.AgentName) // 只有有拉手的才能移除
            };
            removeAgentItem.Click += async (_, _) => await RemoveAgentAsync(memberViewModel);

            // 添加菜单项
            contextMenu.Items.Add(setAgentItem);
            contextMenu.Items.Add(removeAgentItem);

            // 计算菜单显示位置（在单元格位置显示）
            var cellRect = grid.GetCellDisplayRectangle(columnIndex, rowIndex, false);
            var menuLocation = grid.PointToScreen(new Point(cellRect.X + cellRect.Width / 2, cellRect.Y + cellRect.Height / 2));

            // 显示菜单
            contextMenu.Show(menuLocation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"显示拉手操作菜单失败");
            MessageBox.Show($@"显示操作菜单失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 处理积分操作（加分/减分）
    /// </summary>
    /// <param name="account">会员账号</param>
    /// <param name="amount">操作金额</param>
    /// <param name="remark">备注</param>
    /// <param name="isAdd">是否为加分操作</param>
    private async Task ProcessPointsOperationAsync(string account, decimal amount, string remark, bool isAdd)
    {
        try
        {
            var operationType = isAdd ? @"加分" : @"减分";
            _logger.LogInformation(@"开始{OperationType}操作，账号: {Account}, 金额: {Amount}, 备注: {Remark}",
                operationType, account, amount, remark);

            // 调用服务处理积分操作
            var success = await _memberService.UpdateMemberBalanceAsync(account, isAdd ? amount : -amount, remark).ConfigureAwait(false);

            if (success)
            {
                _logger.LogInformation(@"{OperationType}操作成功，账号: {Account}, 金额: {Amount}",
                    operationType, account, amount);

                // 刷新数据
                await LoadMembersAsync().ConfigureAwait(false);

                // 更新UI
                BeginInvoke(() => { BindAllData(); });

                // 成功时不显示弹窗提示，只在状态栏显示结果
            }
            else
            {
                _logger.LogWarning(@"{OperationType}操作失败，账号: {Account}", operationType, account);
                MessageBox.Show($@"{operationType}操作失败，请重试。", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            var operationType = isAdd ? @"加分" : @"减分";
            _logger.LogError(ex, @"{OperationType}操作时发生错误，账号: {Account}", operationType, account);
            MessageBox.Show($@"{operationType}操作失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }


    /// <summary>
    /// 显示设置拉手对话框
    /// </summary>
    /// <param name="memberViewModel">要设置拉手的会员视图模型</param>
    private async Task ShowSetAgentDialogAsync(MemberViewModel memberViewModel)
    {
        try
        {
            // 创建输入对话框
            using var inputDialog = new Form
            {
                Text = @"设置拉手",
                Size = new Size(450, 280),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                ShowInTaskbar = false
            };

            // 创建会员信息标签
            var memberLabel = new Label
            {
                Text = $@"会员账号: {memberViewModel.Account} ({memberViewModel.NickName})",
                Location = new Point(20, 20),
                Size = new Size(400, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建当前拉手标签
            var currentAgentLabel = new Label
            {
                Text = $@"当前拉手: {(string.IsNullOrWhiteSpace(memberViewModel.AgentName) ? @"无" : memberViewModel.AgentName)}",
                Location = new Point(20, 45),
                Size = new Size(400, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F),
                ForeColor = string.IsNullOrWhiteSpace(memberViewModel.AgentName) ? Color.Gray : Color.Blue
            };

            // 创建拉手名称标签
            var agentNameLabel = new Label
            {
                Text = @"拉手名称:",
                Location = new Point(20, 80),
                Size = new Size(80, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建拉手名称输入框
            var agentNameTextBox = new TextBox
            {
                Text = memberViewModel.AgentName ?? string.Empty,
                Location = new Point(110, 78),
                Size = new Size(200, 25),
                Font = new Font(@"Microsoft YaHei UI", 9F),
                MaxLength = 50
            };

            // 创建反水点数标签
            var rebatePointsLabel = new Label
            {
                Text = @"反水点数:",
                Location = new Point(20, 115),
                Size = new Size(80, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建反水点数输入框
            var rebatePointsNumericUpDown = new NumericUpDown
            {
                Value = memberViewModel.AgentRebatePercent,
                Location = new Point(110, 113),
                Size = new Size(150, 25),
                Font = new Font(@"Microsoft YaHei UI", 9F),
                Minimum = 0.0m,
                Maximum = 20.0m,
                DecimalPlaces = 1,
                Increment = 0.1m
            };

            // 创建说明标签
            var hintLabel = new Label
            {
                Text = @"说明: 反水点数为给拉手的返点比例 (0.0 - 20.0)",
                Location = new Point(20, 150),
                Size = new Size(400, 20),
                Font = new Font(@"Microsoft YaHei UI", 8F),
                ForeColor = Color.Gray
            };

            // 创建确定按钮
            var btnOk = new Button
            {
                Text = @"确定设置",
                Location = new Point(265, 190),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK,
                Font = new Font(@"Microsoft YaHei UI", 9F),
                BackColor = Color.LightBlue
            };

            // 创建取消按钮
            var btnCancel = new Button
            {
                Text = @"取消",
                Location = new Point(345, 190),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel,
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 添加控件到对话框
            inputDialog.Controls.AddRange([
                memberLabel, currentAgentLabel, agentNameLabel, agentNameTextBox,
                rebatePointsLabel, rebatePointsNumericUpDown, hintLabel, btnOk, btnCancel
            ]);
            inputDialog.AcceptButton = btnOk;
            inputDialog.CancelButton = btnCancel;

            // 焦点设置到拉手名称输入框
            agentNameTextBox.Focus();
            agentNameTextBox.SelectAll();

            // 显示对话框
            if (inputDialog.ShowDialog(this) == DialogResult.OK)
            {
                var agentName = agentNameTextBox.Text.Trim();
                var rebatePoints = rebatePointsNumericUpDown.Value;

                // 验证输入
                if (string.IsNullOrWhiteSpace(agentName))
                {
                    MessageBox.Show(@"拉手名称不能为空！", @"输入错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (agentName == memberViewModel.Account)
                {
                    MessageBox.Show(@"不能将自己设置为拉手！", @"输入错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 执行设置拉手操作
                await ProcessSetAgentAsync(memberViewModel.Account, agentName, rebatePoints);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"显示设置拉手对话框失败");
            MessageBox.Show($@"显示设置拉手对话框失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 处理设置拉手操作
    /// </summary>
    /// <param name="memberAccount">会员账号</param>
    /// <param name="agentName">拉手名称</param>
    /// <param name="rebatePoints">反水点数</param>
    private async Task ProcessSetAgentAsync(string memberAccount, string agentName, decimal rebatePoints)
    {
        try
        {
            _logger.LogInformation(@"开始设置拉手，会员: {MemberAccount}, 拉手: {AgentName}, 反水点数: {RebatePoints}",
                memberAccount, agentName, rebatePoints);

            // 调用服务设置拉手
            var success = await _memberService.SetMemberAgentAsync(memberAccount, agentName, rebatePoints).ConfigureAwait(false);

            if (success)
            {
                _logger.LogInformation(@"设置拉手成功，会员: {MemberAccount}, 拉手: {AgentName}",
                    memberAccount, agentName);

                // 刷新数据
                await LoadMembersAsync().ConfigureAwait(false);

                // 更新UI
                BeginInvoke(() => { BindAllData(); });

                // 成功时不显示弹窗提示，只在状态栏显示结果
            }
            else
            {
                _logger.LogWarning(@"设置拉手失败，会员: {MemberAccount}", memberAccount);
                MessageBox.Show(@"设置拉手失败，请检查拉手名称是否正确。", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"设置拉手时发生错误，会员: {MemberAccount}", memberAccount);
            MessageBox.Show($@"设置拉手失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 移除拉手操作
    /// </summary>
    /// <param name="memberViewModel">要移除拉手的会员视图模型</param>
    private async Task RemoveAgentAsync(MemberViewModel memberViewModel)
    {
        try
        {
            // 检查是否有拉手
            if (string.IsNullOrWhiteSpace(memberViewModel.AgentName))
            {
                MessageBox.Show(@"该会员当前没有设置拉手。", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 确认移除操作
            var confirmResult = MessageBox.Show(
                $@"会员：{memberViewModel.Account} ({memberViewModel.NickName}){Environment.NewLine}" +
                $@"当前拉手：{memberViewModel.AgentName}{Environment.NewLine}{Environment.NewLine}" +
                $@"确定要移除该会员的拉手设置吗？",
                @"❌ 确认移除拉手",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2); // 默认选择"否"

            if (confirmResult == DialogResult.Yes)
            {
                _logger.LogInformation(@"开始移除拉手，会员: {MemberAccount}, 原拉手: {AgentAccount}",
                    memberViewModel.Account, memberViewModel.AgentName);

                // 调用服务移除拉手
                var success = await _memberService.RemoveMemberAgentAsync(memberViewModel.Account).ConfigureAwait(false);

                if (success)
                {
                    _logger.LogInformation(@"移除拉手成功，会员: {MemberAccount}", memberViewModel.Account);

                    // 刷新数据
                    await LoadMembersAsync().ConfigureAwait(false);

                    // 更新UI
                    BeginInvoke(() => { BindAllData(); });

                    // 成功时不显示弹窗提示，只在状态栏显示结果
                }
                else
                {
                    _logger.LogWarning(@"移除拉手失败，会员: {MemberAccount}", memberViewModel.Account);
                    MessageBox.Show(@"移除拉手失败，请重试。", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"移除拉手时发生错误，会员: {MemberAccount}", memberViewModel.Account);
            MessageBox.Show($@"移除拉手失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 显示上分确认对话框
    /// </summary>
    /// <param name="depositRequest">上分申请对象</param>
    /// <param name="viewModel">上分申请视图模型</param>
    private async Task ShowDepositApprovalDialogAsync(DepositRequest depositRequest, DepositRequestViewModel viewModel)
    {
        try
        {
            // 创建确认对话框
            using var confirmDialog = new Form
            {
                Text = @"确认上分申请",
                Size = new Size(420, 250),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                ShowInTaskbar = false
            };

            // 创建申请信息标签
            var requestInfoLabel = new Label
            {
                Text = $@"会员账号: {viewModel.Account} ({viewModel.NickName})",
                Location = new Point(20, 20),
                Size = new Size(370, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建申请金额标签
            var amountLabel = new Label
            {
                Text = $@"申请金额: {viewModel.Amount:F2}",
                Location = new Point(20, 45),
                Size = new Size(370, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F),
                ForeColor = Color.Blue
            };

            // 创建申请时间标签
            var timeLabel = new Label
            {
                Text = $@"申请时间: {viewModel.CreatedTime:yyyy-MM-dd HH:mm:ss}",
                Location = new Point(20, 70),
                Size = new Size(370, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建状态标签
            var statusLabel = new Label
            {
                Text = $@"当前状态: {viewModel.GetStatusDescription()}",
                Location = new Point(20, 95),
                Size = new Size(370, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 创建"同时设为假人"复选框
            var setFakeUserCheckBox = new CheckBox
            {
                Text = @"同时设为假人",
                Location = new Point(20, 130),
                Size = new Size(120, 20),
                Font = new Font(@"Microsoft YaHei UI", 9F, FontStyle.Bold),
                ForeColor = Color.Red,
                Checked = false
            };

            // 创建确定按钮
            var btnOk = new Button
            {
                Text = @"确定同意",
                Location = new Point(235, 170),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK,
                Font = new Font(@"Microsoft YaHei UI", 9F),
                BackColor = Color.LightGreen
            };

            // 创建取消按钮
            var btnCancel = new Button
            {
                Text = @"取消",
                Location = new Point(315, 170),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel,
                Font = new Font(@"Microsoft YaHei UI", 9F)
            };

            // 添加控件到对话框
            confirmDialog.Controls.AddRange([
                requestInfoLabel, amountLabel, timeLabel, statusLabel,
                setFakeUserCheckBox, btnOk, btnCancel
            ]);
            confirmDialog.AcceptButton = btnOk;
            confirmDialog.CancelButton = btnCancel;

            // 显示对话框
            if (confirmDialog.ShowDialog(this) == DialogResult.OK)
            {
                var setAsFakeUser = setFakeUserCheckBox.Checked;

                // 执行上分操作
                await ProcessDepositApprovalAsync(depositRequest, setAsFakeUser);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"显示上分确认对话框失败");
            MessageBox.Show($@"显示确认对话框失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 处理上分确认操作
    /// </summary>
    /// <param name="depositRequest">上分申请对象</param>
    /// <param name="setAsFakeUser">是否同时设为假人</param>
    private async Task ProcessDepositApprovalAsync(DepositRequest depositRequest, bool setAsFakeUser)
    {
        try
        {
            _logger.LogInformation(@"开始处理上分申请，申请ID: {RequestId}, 会员: {Account}, 金额: {Amount}, 设为假人: {SetAsFakeUser}",
                depositRequest.Id, depositRequest.Account, depositRequest.Amount, setAsFakeUser);


            // 执行上分操作
            var success = await _depositWithdrawRecordService.ReviewDepositRequestAsync(
                depositRequest.Id,
                true,
                @"管理员",
                @"批准上分"
            ).ConfigureAwait(false);

            if (success)
            {
                _logger.LogInformation(@"上分申请处理成功，申请ID: {RequestId}", depositRequest.Id);

                // 如果需要设为假人，执行设置操作
                if (setAsFakeUser)
                {
                    var setFakeUserSuccess = await _memberService.UpdateMemberUserTypeAsync(depositRequest.Account, @"假人").ConfigureAwait(false);

                    if (setFakeUserSuccess)
                    {
                        _logger.LogInformation(@"会员已设为假人，账号: {Account}", depositRequest.Account);
                    }
                    else
                    {
                        _logger.LogWarning(@"设置假人失败，账号: {Account}", depositRequest.Account);
                        // 即使设置假人失败，上分操作已经成功，所以不影响整体结果
                    }
                }

                // 刷新数据
                await LoadAllDataAsync().ConfigureAwait(true);

                // 发送聊天消息通知用户
                await SendDepositApprovalNotificationAsync(depositRequest, setAsFakeUser);

                // 更新状态栏
                // var statusMessage = setAsFakeUser && success ? $@"上分申请已批准，会员 '{depositRequest.Account}' 已设为假人" : $@"上分申请已批准，金额: {depositRequest.Amount:F2}";
            }
            else
            {
                _logger.LogWarning(@"上分申请处理失败，申请ID: {RequestId}", depositRequest.Id);
                MessageBox.Show(@"上分申请处理失败，请重试。", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"处理上分申请时发生错误，申请ID: {RequestId}", depositRequest.Id);
            MessageBox.Show($@"处理上分申请失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 发送上分申请批准通知
    /// </summary>
    /// <param name="depositRequest">上分申请对象</param>
    /// <param name="setAsFakeUser">是否同时设为假人</param>
    private async Task SendDepositApprovalNotificationAsync(DepositRequest depositRequest, bool setAsFakeUser)
    {
        try
        {
            // 获取会员信息
            var member = await _memberService.GetMemberAsync(depositRequest.Account);

            // 构建通知消息
            var message = $@"{EmojiHelper.GetCheckMark()}查分[{depositRequest.Amount:F2}]成功{EmojiHelper.GetMoneyBag()}:{member!.Balance}";

            // 发送@用户的群消息
            await _chatService.SendGroupMessageAsync(message, depositRequest.Account);

            _logger.LogInformation(@"上分批准通知已发送，会员: {Account}, 金额: {Amount}, 设为假人: {SetAsFakeUser}",
                depositRequest.Account, depositRequest.Amount, setAsFakeUser);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"发送上分批准通知失败，会员: {Account}", depositRequest.Account);
            // 不抛出异常，避免影响主要业务流程
        }
    }

    /// <summary>
    /// 发送上分申请拒绝通知
    /// </summary>
    /// <param name="depositRequest">上分申请对象</param>
    private async Task SendDepositRejectionNotificationAsync(DepositRequest depositRequest)
    {
        try
        {
            // 构建拒绝通知消息
            // 获取会员信息
            var member = await _memberService.GetMemberAsync(depositRequest.Account);

            // 构建通知消息
            var message = $@"{EmojiHelper.GetCheckMark()}查分[{depositRequest.Amount:F2}]失败{EmojiHelper.GetMoneyBag()}:{member!.Balance}";

            // 发送@用户的群消息
            await _chatService.SendGroupMessageAsync(message, depositRequest.Account);

            _logger.LogInformation(@"上分拒绝通知已发送，会员: {Account}, 金额: {Amount}",
                depositRequest.Account, depositRequest.Amount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"发送上分拒绝通知失败，会员: {Account}", depositRequest.Account);
            // 不抛出异常，避免影响主要业务流程
        }
    }

    /// <summary>
    /// 显示真假人切换确认对话框
    /// </summary>
    /// <param name="memberViewModel">要切换的会员视图模型</param>
    private async Task ShowUserTypeToggleDialogAsync(MemberViewModel memberViewModel)
    {
        try
        {
            var currentUserType = memberViewModel.UserType;
            var targetUserType = currentUserType == @"真人" ? @"假人" : @"真人";
            var actionText = currentUserType == @"真人" ? @"设为假人" : @"设为真人";
            var iconType = currentUserType == @"真人" ? @"🤖" : @"👤";

            // 第一次确认对话框
            var firstConfirmResult = MessageBox.Show(
                $@"会员：{memberViewModel.Account} ({memberViewModel.NickName}){Environment.NewLine}" +
                $@"当前：{currentUserType} → {targetUserType}{Environment.NewLine}{Environment.NewLine}" +
                $@"确定要{actionText}吗？",
                $@"{iconType} 切换会员类型",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2); // 默认选择"否"

            if (firstConfirmResult != DialogResult.Yes)
            {
                return; // 用户取消操作
            }

            // 第二次确认对话框（简化版）
            var secondConfirmResult = MessageBox.Show(
                $@"再次确认：{Environment.NewLine}{Environment.NewLine}" +
                $@"将会员 '{memberViewModel.Account}' 从 '{currentUserType}' 切换为 '{targetUserType}'？",
                $@"⚠️ 二次确认",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning,
                MessageBoxDefaultButton.Button2); // 默认选择"否"

            if (secondConfirmResult == DialogResult.Yes)
            {
                // 执行真假人切换操作
                await ProcessUserTypeToggleAsync(memberViewModel.Account, targetUserType);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"显示真假人切换确认对话框失败");
            MessageBox.Show($@"显示确认对话框失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 处理真假人切换操作
    /// </summary>
    /// <param name="account">会员账号</param>
    /// <param name="newUserType">新的用户类型</param>
    private async Task ProcessUserTypeToggleAsync(string account, string newUserType)
    {
        try
        {
            _logger.LogInformation(@"开始切换会员类型，账号: {Account}, 新类型: {NewUserType}", account, newUserType);

            // 调用服务更新用户类型
            var success = await _memberService.UpdateMemberUserTypeAsync(account, newUserType).ConfigureAwait(false);

            if (success)
            {
                _logger.LogInformation(@"会员类型切换成功，账号: {Account}, 新类型: {NewUserType}", account, newUserType);

                // 刷新数据
                await LoadMembersAsync().ConfigureAwait(false);

                // 更新UI
                BeginInvoke(() => { BindAllData(); });

                // 成功时不显示弹窗提示，只在状态栏显示结果
            }
            else
            {
                _logger.LogWarning(@"会员类型切换失败，账号: {Account}", account);
                MessageBox.Show(@"用户类型切换失败，请重试。", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"切换会员类型时发生错误，账号: {Account}", account);
            MessageBox.Show($@"用户类型切换失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 更新会员回水比例
    /// </summary>
    /// <param name="account">会员账号</param>
    /// <param name="newRebatePercent">新回水比例</param>
    private async Task UpdateMemberRebatePercentAsync(string account, decimal newRebatePercent)
    {
        try
        {
            _logger.LogInformation(@"开始更新会员回水比例，账号: {Account}, 新回水比例: {NewRebatePercent}", account, newRebatePercent);

            // 调用服务更新回水比例
            var success = await _memberService.UpdateMemberRebatePercentAsync(account, newRebatePercent).ConfigureAwait(false);

            if (success)
            {
                _logger.LogInformation(@"会员回水比例更新成功，账号: {Account}, 新回水比例: {NewRebatePercent}", account, newRebatePercent);

                // 刷新数据
                await LoadMembersAsync().ConfigureAwait(false);

                // 更新UI
                BeginInvoke(() => { BindAllData(); });

                // 成功时不显示弹窗提示，只在状态栏显示结果
            }
            else
            {
                _logger.LogWarning(@"会员回水比例更新失败，账号: {Account}", account);
                MessageBox.Show(@"回水比例更新失败，请重试。", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"更新会员回水比例时发生错误，账号: {Account}", account);
            MessageBox.Show($@"回水比例更新失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 更新会员昵称
    /// </summary>
    /// <param name="account">会员账号</param>
    /// <param name="newNickName">新昵称</param>
    private async Task UpdateMemberNickNameAsync(string account, string newNickName)
    {
        try
        {
            _logger.LogInformation(@"开始更新会员昵称，账号: {Account}, 新昵称: {NewNickName}", account, newNickName);

            // 调用服务更新昵称
            var success = await _memberService.UpdateMemberNickNameAsync(account, newNickName).ConfigureAwait(false);

            if (success)
            {
                _logger.LogInformation(@"会员昵称更新成功，账号: {Account}, 新昵称: {NewNickName}", account, newNickName);

                // 刷新数据
                await LoadMembersAsync().ConfigureAwait(false);

                // 更新UI
                BeginInvoke(() => { BindAllData(); });

                // 成功时不再显示弹窗提示，只在状态栏显示结果
            }
            else
            {
                _logger.LogWarning(@"会员昵称更新失败，账号: {Account}", account);
                MessageBox.Show(@"昵称更新失败，请重试。", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"更新会员昵称时发生错误，账号: {Account}", account);
            MessageBox.Show($@"昵称更新失败：{ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 上分申请表格按钮点击事件
    /// </summary>
    private async void DgvDepositRequests_CellClick(object? sender, DataGridViewCellEventArgs e)
    {
        try
        {
            if (e.RowIndex < 0 || e.ColumnIndex < 0) return;

            if (sender is not DataGridView grid) return;
            var columnName = grid.Columns[e.ColumnIndex].Name;

            if (columnName != @"ApproveButton" && columnName != @"RejectButton") return;

            // 从视图模型获取申请ID，然后找到原始申请对象
            var viewModel = _depositRequestViewModels[e.RowIndex];
            var depositRequest = _depositRequests.FirstOrDefault(r => r.Id == viewModel.Id);
            if (depositRequest == null || depositRequest.Status != EnumDepositStatus.Pending)
            {
                _logger.LogWarning(@"申请不存在或已经处理过了，申请ID: {RequestId}", viewModel.Id);
                return;
            }

            var isApprove = columnName == @"ApproveButton";
            var action = isApprove ? @"接受" : @"拒绝";

            if (isApprove)
            {
                // 同意操作：显示确认对话框
                await ShowDepositApprovalDialogAsync(depositRequest, viewModel);
            }
            else
            {
                // 拒绝操作：直接处理
                try
                {
                    var success = await _depositWithdrawRecordService.ReviewDepositRequestAsync(
                        depositRequest.Id,
                        false,
                        @"管理员",
                        @"拒绝上分"
                    ).ConfigureAwait(false);

                    if (success)
                    {
                        _logger.LogInformation(@"上分申请{Action}成功，申请ID: {RequestId}", action, depositRequest.Id);

                        // 刷新数据
                        await LoadAllDataAsync().ConfigureAwait(true);

                        // 发送聊天消息通知用户
                        await SendDepositRejectionNotificationAsync(depositRequest);
                    }
                    else
                    {
                        _logger.LogWarning(@"上分申请{Action}失败，申请ID: {RequestId}", action, depositRequest.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, @"处理上分申请失败，申请ID: {RequestId}", depositRequest.Id);
                }
                finally
                {
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"上分申请表格按钮点击事件失败");
        }
    }

    /// <summary>
    /// 下分申请表格按钮点击事件
    /// </summary>
    private async void DgvWithdrawRequests_CellClick(object? sender, DataGridViewCellEventArgs e)
    {
        try
        {
            if (e.RowIndex < 0 || e.ColumnIndex < 0) return;

            if (sender is not DataGridView grid) return;
            var columnName = grid.Columns[e.ColumnIndex].Name;

            if (columnName != @"ApproveButton" && columnName != @"RejectButton") return;

            // 从视图模型获取申请ID，然后找到原始申请对象
            var viewModel = _withdrawRequestViewModels[e.RowIndex];
            var withdrawRequest = _withdrawRequests.FirstOrDefault(r => r.Id == viewModel.Id);
            if (withdrawRequest == null || withdrawRequest.Status != EnumWithdrawStatus.Pending)
            {
                _logger.LogWarning(@"申请不存在或已经处理过了，申请ID: {RequestId}", viewModel.Id);
                return;
            }

            var isApprove = columnName == @"ApproveButton";
            var action = isApprove ? @"接受" : @"拒绝";

            try
            {
                var success = await _depositWithdrawRecordService.ReviewWithdrawRequestAsync(
                    withdrawRequest.Id,
                    isApprove,
                    @"管理员",
                    isApprove ? @"批准下分" : @"拒绝下分"
                ).ConfigureAwait(false);

                if (success)
                {
                    _logger.LogInformation(@"下分申请{Action}成功，申请ID: {RequestId}", action, withdrawRequest.Id);

                    // 刷新数据
                    await LoadAllDataAsync().ConfigureAwait(true);
                }
                else
                {
                    _logger.LogWarning(@"下分申请{Action}失败，申请ID: {RequestId}", action, withdrawRequest.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, @"处理下分申请失败，申请ID: {RequestId}", withdrawRequest.Id);
            }
            finally
            {
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"下分申请表格按钮点击事件失败");
        }
    }

    #endregion

    #region 上下分申请表格自绘事件

    /// <summary>
    /// 上分申请表格按钮自定义绘制
    /// </summary>
    private void DgvDepositRequests_CellPainting(object? sender, DataGridViewCellPaintingEventArgs e)
    {
        // 只处理按钮列
        if (e is { ColumnIndex: >= 0, RowIndex: >= 0 })
        {
            var columnName = dgvDepositRequests.Columns[e.ColumnIndex].Name;
            if (columnName == @"ApproveButton" || columnName == @"RejectButton")
            {
                // 确定按钮颜色
                Color buttonColor = columnName == @"ApproveButton" ? Color.LightGreen : Color.LightCoral;
                Color textColor = Color.Black;

                // 绘制按钮背景
                e.Paint(e.CellBounds, DataGridViewPaintParts.All & ~DataGridViewPaintParts.ContentForeground);

                // 绘制按钮
                using (var brush = new SolidBrush(buttonColor))
                {
                    e.Graphics?.FillRectangle(brush, e.CellBounds);
                }

                // 绘制按钮边框
                using (var pen = new Pen(Color.Gray))
                {
                    e.Graphics?.DrawRectangle(pen, e.CellBounds.X, e.CellBounds.Y,
                        e.CellBounds.Width - 1, e.CellBounds.Height - 1);
                }

                // 绘制按钮文字
                if (e.Graphics != null)
                {
                    string buttonText = columnName == @"ApproveButton" ? @"接受上分" : @"拒绝上分";
                    using (var textBrush = new SolidBrush(textColor))
                    {
                        var textFormat = new StringFormat
                        {
                            Alignment = StringAlignment.Center,
                            LineAlignment = StringAlignment.Center
                        };
                        var font = e.CellStyle?.Font ?? SystemFonts.DefaultFont;
                        e.Graphics.DrawString(buttonText, font, textBrush, e.CellBounds, textFormat);
                    }
                }

                e.Handled = true;
            }
        }
    }

    /// <summary>
    /// 下分申请表格按钮自定义绘制
    /// </summary>
    private void DgvWithdrawRequests_CellPainting(object? sender, DataGridViewCellPaintingEventArgs e)
    {
        // 只处理按钮列
        if (e is { ColumnIndex: >= 0, RowIndex: >= 0 })
        {
            var columnName = dgvWithdrawRequests.Columns[e.ColumnIndex].Name;
            if (columnName == @"ApproveButton" || columnName == @"RejectButton")
            {
                // 确定按钮颜色
                Color buttonColor = columnName == @"ApproveButton" ? Color.LightGreen : Color.LightCoral;
                Color textColor = Color.Black;

                // 绘制按钮背景
                e.Paint(e.CellBounds, DataGridViewPaintParts.All & ~DataGridViewPaintParts.ContentForeground);

                // 绘制按钮
                using (var brush = new SolidBrush(buttonColor))
                {
                    e.Graphics?.FillRectangle(brush, e.CellBounds);
                }

                // 绘制按钮边框
                using (var pen = new Pen(Color.Gray))
                {
                    e.Graphics?.DrawRectangle(pen, e.CellBounds.X, e.CellBounds.Y,
                        e.CellBounds.Width - 1, e.CellBounds.Height - 1);
                }

                // 绘制按钮文字
                if (e.Graphics != null)
                {
                    string buttonText = columnName == @"ApproveButton" ? @"接受下分" : @"拒绝下分";
                    using var textBrush = new SolidBrush(textColor);
                    var textFormat = new StringFormat
                    {
                        Alignment = StringAlignment.Center,
                        LineAlignment = StringAlignment.Center
                    };
                    var font = e.CellStyle?.Font ?? SystemFonts.DefaultFont;
                    e.Graphics.DrawString(buttonText, font, textBrush, e.CellBounds, textFormat);
                }

                e.Handled = true;
            }
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 计算用户的未结算积分（当前期的投注总额）
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <returns>未结算积分</returns>
    private async Task<decimal> CalculateUnsettledAmountAsync(string account)
    {
        try
        {
            // 获取当前期号
            var currentIssueTime = _issueTimeService.GetCurrentCachedIssueTime();
            if (currentIssueTime == null || string.IsNullOrWhiteSpace(currentIssueTime.Issue))
            {
                return 0;
            }

            // 获取当前期该用户的所有未结算投注
            var betOrders = await _betRecordService.GetCurrentIssueBetOrdersAsync(currentIssueTime.Issue).ConfigureAwait(false);
            var userBetOrders = betOrders.Where(o => o.Account == account &&
                                                     o.Status == EnumBetOrderStatus.Confirmed).ToList();

            return userBetOrders.Sum(o => o.Amount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"计算未结算积分失败，账号: {Account}", account);
            return 0;
        }
    }

    /// <summary>
    /// 计算用户的上期盈亏
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <returns>上期盈亏金额</returns>
    private async Task<decimal> CalculateLastIssueProfitLossAsync(string account)
    {
        try
        {
            // 获取该用户最近的已结算投注订单
            var recentBetOrders = await _betRecordService.GetBetOrdersByAccountAsync(account, 50).ConfigureAwait(false);
            var settledOrders = recentBetOrders.Where(o => o.Status == EnumBetOrderStatus.Win ||
                                                           o.Status == EnumBetOrderStatus.Lose ||
                                                           o.Status == EnumBetOrderStatus.Draw).ToList();

            if (!settledOrders.Any())
            {
                return 0;
            }

            // 按期号分组，获取最后一期的数据
            var lastIssueOrders = settledOrders.GroupBy(o => o.Issue)
                .OrderByDescending(g => g.Key)
                .FirstOrDefault()?.ToList();

            if (lastIssueOrders == null || !lastIssueOrders.Any())
            {
                return 0;
            }

            // 计算盈亏：中奖金额 - 投注金额
            decimal totalBetAmount = lastIssueOrders.Sum(o => o.Amount);
            decimal totalWinAmount = lastIssueOrders.Where(o => o.Status == EnumBetOrderStatus.Win ||
                                                                o.Status == EnumBetOrderStatus.Lose ||
                                                                o.Status == EnumBetOrderStatus.Draw)
                .Sum(o => o.ActualWinAmount ?? 0);

            return totalWinAmount - totalBetAmount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"计算上期盈亏失败，账号: {Account}", account);
            return 0;
        }
    }

    /// <summary>
    /// 计算用户的总上分和总下分
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <returns>总上分和总下分的元组</returns>
    private async Task<(decimal totalDeposit, decimal totalWithdraw)> CalculateTotalDepositWithdrawAsync(string account)
    {
        try
        {
            // 查询该用户的所有上下分记录
            var depositWithdrawRecords = await _depositWithdrawRecordService.QueryDepositWithdrawRecordsAsync(
                DateTime.MinValue, DateTime.MaxValue, account, null, true).ConfigureAwait(false);

            if (!depositWithdrawRecords.Any())
            {
                return (0, 0);
            }

            // 计算总上分（类型为"上分"的记录）
            var totalDeposit = depositWithdrawRecords
                .Where(r => r.Type == @"上分")
                .Sum(r => r.Amount);

            // 计算总下分（类型为"下分"的记录）
            var totalWithdraw = depositWithdrawRecords
                .Where(r => r.Type == @"下分")
                .Sum(r => r.Amount);

            return (totalDeposit, totalWithdraw);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"计算总上分和总下分失败，账号: {Account}", account);
            return (0, 0);
        }
    }

    #region 计算哈希值

    /// <summary>
    /// 计算数据列表的哈希值，用于检测数据变化
    /// 基于对象内容而不是对象引用计算哈希值，确保只有在数据真正变化时才更新UI
    /// </summary>
    /// <param name="data">数据列表</param>
    /// <returns>哈希值</returns>
    private int CalculateDataHash<T>(List<T> data)
    {
        if (data.Count == 0)
            return 0;

        // 使用数据项数量和每个项的内容哈希值组合计算
        unchecked
        {
            int hash = data.Count;
            foreach (var item in data)
            {
                if (item == null)
                {
                    hash = hash * 31;
                    continue;
                }

                // 根据对象类型计算基于内容的哈希值
                int itemHash = item switch
                {
                    Member member => CalculateMemberHash(member),
                    MemberViewModel memberVm => CalculateMemberViewModelHash(memberVm),
                    DepositRequestViewModel depositVm => CalculateDepositRequestViewModelHash(depositVm),
                    WithdrawRequestViewModel withdrawVm => CalculateWithdrawRequestViewModelHash(withdrawVm),
                    CurrentIssueBetViewModel currentIssueBetVm => CalculateCurrentIssueBetViewModelHash(currentIssueBetVm),
                    _ => item.GetHashCode() // 对于其他类型，回退到默认实现
                };

                hash = hash * 31 + itemHash;
            }

            return hash;
        }
    }

    /// <summary>
    /// 计算Member对象的内容哈希值
    /// </summary>
    private int CalculateMemberHash(Member member)
    {
        unchecked
        {
            int hash = 17;
            hash = hash * 31 + member.Id.GetHashCode();
            hash = hash * 31 + (member.Account?.GetHashCode() ?? 0);
            hash = hash * 31 + (member.NickName?.GetHashCode() ?? 0);

            hash = hash * 31 + member.Balance.GetHashCode();
            hash = hash * 31 + member.RebatePercent.GetHashCode();
            hash = hash * 31 + (member.AgentName?.GetHashCode() ?? 0);
            hash = hash * 31 + member.CreateTime.GetHashCode();
            hash = hash * 31 + (member.UserType?.GetHashCode() ?? 0);
            hash = hash * 31 + member.Deleted.GetHashCode();
            return hash;
        }
    }

    /// <summary>
    /// 计算MemberViewModel对象的内容哈希值
    /// </summary>
    private int CalculateMemberViewModelHash(MemberViewModel vm)
    {
        unchecked
        {
            int hash = 17;
            hash = hash * 31 + vm.Id.GetHashCode();
            hash = hash * 31 + (vm.Account?.GetHashCode() ?? 0);
            hash = hash * 31 + (vm.NickName?.GetHashCode() ?? 0);
            hash = hash * 31 + vm.Balance.GetHashCode();
            hash = hash * 31 + vm.UnsettledAmount.GetHashCode();
            hash = hash * 31 + vm.LastIssueProfitLoss.GetHashCode();
            hash = hash * 31 + vm.RebatePercent.GetHashCode();
            hash = hash * 31 + (vm.UserType?.GetHashCode() ?? 0);
            hash = hash * 31 + vm.IsEnabled.GetHashCode();
            hash = hash * 31 + vm.CreatedTime.GetHashCode();
            return hash;
        }
    }

    /// <summary>
    /// 计算DepositRequestViewModel对象的内容哈希值
    /// </summary>
    private int CalculateDepositRequestViewModelHash(DepositRequestViewModel vm)
    {
        unchecked
        {
            int hash = 17;
            hash = hash * 31 + vm.Id.GetHashCode();
            hash = hash * 31 + (vm.Account?.GetHashCode() ?? 0);
            hash = hash * 31 + (vm.NickName?.GetHashCode() ?? 0);
            hash = hash * 31 + vm.Amount.GetHashCode();
            hash = hash * 31 + vm.Status.GetHashCode();
            hash = hash * 31 + vm.CreatedTime.GetHashCode();
            return hash;
        }
    }

    /// <summary>
    /// 计算WithdrawRequestViewModel对象的内容哈希值
    /// </summary>
    private int CalculateWithdrawRequestViewModelHash(WithdrawRequestViewModel vm)
    {
        unchecked
        {
            int hash = 17;
            hash = hash * 31 + vm.Id.GetHashCode();
            hash = hash * 31 + (vm.Account?.GetHashCode() ?? 0);
            hash = hash * 31 + (vm.NickName?.GetHashCode() ?? 0);
            hash = hash * 31 + vm.Amount.GetHashCode();
            hash = hash * 31 + vm.Status.GetHashCode();
            hash = hash * 31 + vm.CreatedTime.GetHashCode();
            return hash;
        }
    }

    /// <summary>
    /// 计算CurrentIssueBetViewModel对象的内容哈希值
    /// </summary>
    private int CalculateCurrentIssueBetViewModelHash(CurrentIssueBetViewModel vm)
    {
        unchecked
        {
            int hash = 17;
            hash = hash * 31 + vm.Id.GetHashCode();
            hash = hash * 31 + vm.Issue.GetHashCode();
            hash = hash * 31 + vm.Account.GetHashCode();
            hash = hash * 31 + vm.NickName.GetHashCode();
            hash = hash * 31 + vm.PlayItem.GetHashCode();
            hash = hash * 31 + vm.Amount.GetHashCode();
            hash = hash * 31 + vm.EnumFlightStatus.GetHashCode();
            hash = hash * 31 + vm.CreatedTime.GetHashCode();
            hash = hash * 31 + (vm.FlightTime?.GetHashCode() ?? 0);
            return hash;
        }
    }

    #endregion

    #region 智能更新BindingSource数据，只有在数据真正发生变化时才刷新UI

    /// <summary>
    /// 智能更新BindingSource数据，只有在数据真正发生变化时才刷新UI
    /// </summary>
    /// <param name="bindingSource">要更新的BindingSource</param>
    /// <param name="newData">新数据列表</param>
    /// <param name="lastHash">上次数据的哈希值引用</param>
    /// <param name="dataGridView">关联的DataGridView，用于在首次绑定时清除选择</param>
    /// <returns>是否发生了数据变化</returns>
    private bool UpdateBindingSource<T>(BindingSource bindingSource, List<T> newData, ref int lastHash, DataGridView? dataGridView = null)
    {
        // 计算新数据的哈希值
        int newHash = CalculateDataHash(newData);

        if (bindingSource.DataSource == null)
        {
            // 首次绑定
            bindingSource.DataSource = newData;
            lastHash = newHash;

            // 🎯 方案A：首次绑定后也直接清除选择
            if (dataGridView != null)
            {
                dataGridView.BeginInvoke(() =>
                {
                    if (dataGridView.RowCount > 0)
                    {
                        dataGridView.ClearSelection();
                        _logger.LogDebug(@"首次数据绑定完成，已清除 {DataGridViewName} 的默认选择", dataGridView.Name);
                    }
                });
            }

            return true;
        }

        if (newHash != lastHash)
        {
            // 数据发生变化，更新UI
            bindingSource.DataSource = newData;
            bindingSource.ResetBindings(false);

            // 🎯 方案A：最简单的解决方案 - 直接清除ResetBindings()产生的自动选择
            if (dataGridView != null)
            {
                dataGridView.ClearSelection();
                _logger.LogDebug(@"检测到数据变化，已更新UI并清除选择 - 新哈希值: {Hash}", newHash);
            }
            else
            {
                _logger.LogDebug(@"检测到数据变化，已更新UI - 新哈希值: {Hash}", newHash);
            }

            lastHash = newHash;
            return true;
        }

        // 数据无变化，跳过UI更新
        _logger.LogTrace(@"数据无变化，跳过UI更新 - 哈希值: {Hash}", newHash);
        return false;
    }

    #endregion

    #endregion

    #region StatusStrip标签更新方法

    /// <summary>
    /// 更新StatusStrip中指定标签的文本
    /// </summary>
    /// <param name="labelIndex">标签索引 (1-7)</param>
    /// <param name="text">要显示的文本</param>
    private void UpdateStatusLabel(int labelIndex, string text)
    {
        if (InvokeRequired)
        {
            Invoke(new Action<int, string>(UpdateStatusLabel), labelIndex, text);
            return;
        }

        var label = labelIndex switch
        {
            1 => toolStripStatusLabel1,
            2 => toolStripStatusLabel2,
            3 => toolStripStatusLabel3,
            4 => toolStripStatusLabel4,
            5 => toolStripStatusLabel5,
            6 => toolStripStatusLabel6,
            7 => toolStripStatusLabel7,
            _ => null
        };

        if (label != null)
        {
            label.Text = text;
        }
    }

    /// <summary>
    /// 批量更新多个StatusStrip标签
    /// </summary>
    /// <param name="labelTexts">标签文本数组，索引对应标签编号(1-7)</param>
    private void UpdateStatusLabels(params string[] labelTexts)
    {
        for (int i = 0; i < Math.Min(labelTexts.Length, 7); i++)
        {
            if (!string.IsNullOrEmpty(labelTexts[i]))
            {
                UpdateStatusLabel(i + 1, labelTexts[i]);
            }
        }
    }

    /// <summary>
    /// 清空所有StatusStrip标签
    /// </summary>
    private void ClearAllStatusLabels()
    {
        for (int i = 1; i <= 7; i++)
        {
            UpdateStatusLabel(i, string.Empty);
        }
    }

    /// <summary>
    /// 初始化StatusStrip标签的默认内容
    /// </summary>
    private void InitializeStatusLabels()
    {
        UpdateStatusLabel(1, @"已连接[1]线路");
        UpdateStatusLabel(2, @"今日输赢:");
        UpdateStatusLabel(3, @"0.00");
        UpdateStatusLabel(4, @"当前第--期投注额:");
        UpdateStatusLabel(5, @"0.00");
        UpdateStatusLabel(6, @"上期总输赢:");
        UpdateStatusLabel(7, @"0.00");
    }

    /// <summary>
    /// 更新今日输赢金额
    /// </summary>
    /// <param name="amount">今日输赢金额</param>
    private void UpdateTodayWinLoss(decimal amount)
    {
        UpdateStatusLabel(3, amount.ToString(@"F2"));
    }

    /// <summary>
    /// 更新当前期投注信息
    /// </summary>
    /// <param name="issueNumber">期号</param>
    /// <param name="betAmount">投注总额</param>
    private void UpdateCurrentIssueBetInfo(string issueNumber, decimal betAmount)
    {
        UpdateStatusLabel(4, $@"当前第{issueNumber}期投注额:");
        UpdateStatusLabel(5, betAmount.ToString(@"F2"));
    }

    /// <summary>
    /// 更新上期总输赢金额
    /// </summary>
    /// <param name="amount">上期总输赢金额</param>
    private void UpdateLastIssueWinLoss(decimal amount)
    {
        UpdateStatusLabel(7, amount.ToString(@"F2"));
    }

    /// <summary>
    /// 更新StatusStrip所有动态信息
    /// </summary>
    private async void UpdateStatusStripInfo()
    {
        try
        {
            // 获取当前期号信息
            var currentIssue = _issueTimeService.GetCurrentCachedIssueTime();
            if (currentIssue != null)
            {
                // 计算当前期投注总额
                var currentBetAmount = await CalculateCurrentIssueTotalBetAmountAsync(currentIssue.Issue);
                UpdateCurrentIssueBetInfo(currentIssue.Issue, currentBetAmount);
            }

            // 计算今日输赢
            var todayWinLoss = await CalculateTodayWinLossAsync();
            UpdateTodayWinLoss(todayWinLoss);

            // 计算上期总输赢
            var lastIssueWinLoss = await CalculateLastIssueWinLossAsync();
            UpdateLastIssueWinLoss(lastIssueWinLoss);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"更新StatusStrip信息时发生异常");
        }
    }

    /// <summary>
    /// 计算当前期投注总额
    /// </summary>
    /// <param name="issueNumber">期号</param>
    /// <returns>投注总额</returns>
    private async Task<decimal> CalculateCurrentIssueTotalBetAmountAsync(string issueNumber)
    {
        try
        {
            if (string.IsNullOrEmpty(issueNumber))
                return 0;

            // 获取当前期的所有投注订单
            var betOrders = await _betRecordService.GetBetOrdersByIssueAsync(issueNumber);

            // 计算总投注额（排除已取消的订单）
            var totalAmount = betOrders
                .Where(b => b.Status != EnumBetOrderStatus.Cancelled)
                .Sum(b => b.Amount);

            return totalAmount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"计算当前期投注总额失败，期号: {IssueNumber}", issueNumber);
            return 0;
        }
    }

    /// <summary>
    /// 计算今日输赢总额
    /// </summary>
    /// <returns>今日输赢总额（从用户角度）</returns>
    private async Task<decimal> CalculateTodayWinLossAsync()
    {
        try
        {
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);

            // 使用BetRecordService查询今日投注记录（包含假人用户）
            var todayBetRecords = await _betRecordService.QueryBetRecordsAsync(today, tomorrow, null, null, true);

            // 计算总盈亏（从用户角度）
            var totalProfitLoss = _betRecordService.CalculateTotalProfitLoss(todayBetRecords);

            return totalProfitLoss;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"计算今日输赢失败");
            return 0;
        }
    }

    /// <summary>
    /// 计算上期总输赢
    /// </summary>
    /// <returns>上期总输赢（从用户角度）</returns>
    private async Task<decimal> CalculateLastIssueWinLossAsync()
    {
        try
        {
            // 获取最新的开奖信息来确定上期期号
            var latestDraw = await _drawService.GetDrawLastAsync(RuntimeConfiguration._robotServiceCancellation!.Token);
            if (latestDraw == null || string.IsNullOrEmpty(latestDraw.Issue))
            {
                _logger.LogDebug(@"无法获取最新开奖信息，上期总输赢返回0");
                return 0;
            }

            _logger.LogDebug(@"获取到最新开奖期号: {Issue}，开始计算上期总输赢", latestDraw.Issue);

            // 扩大查询时间范围，确保包含最近几天的数据
            var startDate = DateTime.Today.AddDays(-3); // 查询最近3天
            var endDate = DateTime.Today.AddDays(1);    // 包含今天全天

            var lastIssueBetRecords = await _betRecordService.QueryBetRecordsAsync(startDate, endDate, latestDraw.Issue, null, true);

            _logger.LogDebug(@"查询上期投注记录完成，期号: {Issue}, 时间范围: {StartDate} - {EndDate}, 记录数: {Count}",
                latestDraw.Issue, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"), lastIssueBetRecords.Count);

            // 只计算已结算的投注记录的盈亏
            var settledRecords = lastIssueBetRecords.Where(r =>
                r.SettlementStatus == EnumBetOrderStatus.Win ||
                r.SettlementStatus == EnumBetOrderStatus.Lose ||
                r.SettlementStatus == EnumBetOrderStatus.Draw).ToList();

            _logger.LogDebug(@"已结算记录数: {SettledCount}, 总记录数: {TotalCount}", settledRecords.Count, lastIssueBetRecords.Count);

            // 详细记录各状态的投注记录数量
            var winCount = settledRecords.Count(r => r.SettlementStatus == EnumBetOrderStatus.Win);
            var loseCount = settledRecords.Count(r => r.SettlementStatus == EnumBetOrderStatus.Lose);
            var drawCount = settledRecords.Count(r => r.SettlementStatus == EnumBetOrderStatus.Draw);
            var confirmedCount = lastIssueBetRecords.Count(r => r.SettlementStatus == EnumBetOrderStatus.Confirmed);

            _logger.LogDebug(@"投注记录状态统计 - 中奖: {Win}, 失败: {Lose}, 和局: {Draw}, 待结算: {Confirmed}",
                winCount, loseCount, drawCount, confirmedCount);

            // 计算总盈亏（从用户角度）
            var totalProfitLoss = _betRecordService.CalculateTotalProfitLoss(settledRecords);

            // 如果有已结算记录，记录详细的盈亏信息
            if (settledRecords.Count > 0)
            {
                var totalBetAmount = settledRecords.Sum(r => r.Amount);
                var totalWinAmount = settledRecords.Where(r => r.SettlementStatus == EnumBetOrderStatus.Win).Sum(r => r.Amount * r.Odds);

                _logger.LogInformation(@"上期总输赢详细信息 - 期号: {Issue}, 投注总额: {BetAmount}, 中奖总额: {WinAmount}, 总盈亏: {TotalProfitLoss}",
                    latestDraw.Issue, totalBetAmount, totalWinAmount, totalProfitLoss);
            }
            else
            {
                _logger.LogInformation(@"上期总输赢计算完成，期号: {Issue}, 无已结算记录，总盈亏: {TotalProfitLoss}", latestDraw.Issue, totalProfitLoss);
            }

            return totalProfitLoss;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"计算上期总输赢失败");
            return 0;
        }
    }

    #endregion

    #region 显示相关方法

    /// <summary>
    /// 检查并发送封盘后投注统计 - 期号投注数据汇总
    ///
    /// 功能：
    /// - 在封盘后发送当期投注统计信息
    /// - 汇总投注人数、投注金额、投注分布
    /// - 防止重复发送相同期号的统计
    ///
    /// 触发条件：
    /// - 游戏服务已启动
    /// - 当前已封盘（不可投注）
    /// - 封盘倒计时已结束（≤0秒）
    /// - 该期号未发送过统计
    ///
    /// 统计内容：
    /// - 总投注人数和金额
    /// - 各投注类型的分布情况
    /// - 格式化的统计报告
    /// </summary>
    /// <param name="closeTimeSpan">距离封盘的剩余秒数</param>
    /// <param name="canBet">当前是否可以投注</param>
    /// <param name="issueTime">当前期号信息</param>
    private async Task CheckAndSendBetSummaryAsync(int closeTimeSpan, bool canBet, IssueTime issueTime)
    {
        try
        {
            // 只在游戏开始且已封盘时检查
            if (!RuntimeConfiguration.IsGameServiceStarted || canBet || closeTimeSpan > 0)
            {
                return;
            }

            // 生成唯一的统计标识，避免重复发送
            var summaryKey = $"bet_summary_{issueTime.Issue}";

            // 检查是否已经发送过这期的投注统计
            if (_sentWarnings.Contains(summaryKey))
            {
                return;
            }

            // 获取当前期的投注数据并生成统计消息
            var summaryMessage = await GenerateBetSummaryMessageAsync(issueTime.Issue);

            if (!string.IsNullOrEmpty(summaryMessage))
            {
                // 发送投注统计消息
                await _chatService.SendGroupMessageAsync(summaryMessage);

                // 记录已发送的统计，避免重复发送
                _sentWarnings.Add(summaryKey);
                _logger.LogInformation(@"已发送封盘后投注统计，期号: {Issue}", issueTime.Issue);

                // 发送避免争议公告
                var announcementMessage = @"避免争议：QQ有时会屏蔽消息或者封群封号，全部数据以机器人后台录入为准，玩的代表接受。";
                await _chatService.SendGroupMessageAsync(announcementMessage);

                // 发送防骗提示
                var scamWarningMessage = $"近期骗子冒充管理QQ骗人，请大家注意，有事请找微信（或者认准QQ群里的管理号）{Environment.NewLine} 如:被骗者 本群一概不负责！！！{Environment.NewLine}正在开.....";
                await _chatService.SendGroupMessageAsync(scamWarningMessage);

                // 检查是否开启了飞单功能
                if (RuntimeConfiguration.IsFlightOrderEnabled)
                {
                    await ProcessFlightOrderAsync(issueTime.Issue);
                }
            }
            else
            {
                var messageBuilder = new StringBuilder();
                messageBuilder.Append("——————————" + "\n");
                messageBuilder.Append("——————————\n");
                messageBuilder.Append($"停止答题[{issueTime.Issue}]期");
                await _chatService.SendGroupMessageAsync(summaryMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"检查并发送封盘后投注统计时发生异常");
        }
    }

    /// <summary>
    /// 检查并发送30秒封盘提醒 - 投注截止前提醒
    ///
    /// 功能：
    /// - 在封盘前30秒发送提醒消息
    /// - 提醒用户抓紧时间投注
    /// - 防止重复发送相同期号的提醒
    ///
    /// 触发条件：
    /// - 游戏服务已启动
    /// - 当前可以投注
    /// - 距离封盘剩余28-32秒之间
    /// - 该期号未发送过提醒
    ///
    /// 消息内容：包含剩余时间和期号信息
    /// </summary>
    /// <param name="closeTimeSpan">距离封盘的剩余秒数</param>
    /// <param name="canBet">当前是否可以投注</param>
    private async Task CheckAndSend30SecondWarningAsync(int closeTimeSpan, bool canBet)
    {
        try
        {
            // 只在游戏开始且可以投注时检查
            if (!RuntimeConfiguration.IsGameServiceStarted || !canBet)
            {
                return;
            }

            // 检查是否在30秒内且大于1秒（避免开启游戏时就已经小于30秒的情况）
            if (closeTimeSpan is <= 30 and > 1)
            {
                // 获取当前期号
                var currentIssue = _issueTimeService.GetCurrentCachedIssueTime();
                if (currentIssue == null)
                {
                    return;
                }

                // 生成唯一的提醒标识，避免重复发送
                var warningKey = $"30sec_warning_{currentIssue.Issue}";

                // 检查是否已经发送过这期的封盘提醒
                if (_sentWarnings.Contains(warningKey))
                {
                    return;
                }

                // 发送30秒提醒消息
                var message = $@"{EmojiHelper.GetHeart()}考试时间还有{closeTimeSpan}#秒{EmojiHelper.GetHeart()}";
                await _chatService.SendGroupMessageAsync(message);

                // 记录已发送的提醒，避免重复发送
                _sentWarnings.Add(warningKey);

                // 清理过期的提醒记录（保留最近10个）
                if (_sentWarnings.Count > 10)
                {
                    var oldestWarnings = _sentWarnings.Take(_sentWarnings.Count - 10).ToList();
                    foreach (var oldWarning in oldestWarnings)
                    {
                        _sentWarnings.Remove(oldWarning);
                    }
                }

                _logger.LogInformation(@"已发送封盘提醒，期号: {Issue}", currentIssue.Issue);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"检查并发送30秒封盘提醒时发生异常");
        }
    }

    /// <summary>
    /// 更新期号时间相关的UI显示
    /// </summary>
    private async Task UpdateIssueTimeUiAsync()
    {
        try
        {
            // 从IssueTimeService中获取当前期号信息
            var issueTime = _issueTimeService.GetCurrentCachedIssueTime();
            if (issueTime is null)
            {
                return;
            }

            // 获取开盘和封盘倒计时
            var openTimeSpan = _issueTimeService.GetCurrentCachedOpenTimeSpan();
            var closeTimeSpan = _issueTimeService.GetCurrentCachedCloseTimeSpan();

            // 根据状态显示不同内容
            if (openTimeSpan < 0 && closeTimeSpan > 0)
            {
                Invoke(() =>
                {
                    label_正在投注期数标题.Text = @"正在投注期数";
                    label_正在投注期数.Text = $@"{issueTime.Issue}";
                    label_封盘倒计时.Text = TimeHelper.FormatTimeRemaining(closeTimeSpan);
                });
            }
            else
            {
                // 封盘状态，设置全局投注状态为false
                RuntimeConfiguration.CanBet = false;

                Invoke(() =>
                {
                    label_正在投注期数标题.Text = @"即将开盘";
                    label_正在投注期数.Text = $@"{issueTime.Issue}";
                    label_封盘倒计时.Text = TimeHelper.FormatTimeRemaining(openTimeSpan);
                });
            }

            // 更新收单状态
            UpdateOrderCollectionStatus(RuntimeConfiguration.CanBet);

            // 检查是否需要发送开盘通知
            // await CheckAndSendOpenNotificationAsync(RuntimeConfiguration.CanBet, openTimeSpan);

            // 检查是否需要发送30秒封盘提醒
            await CheckAndSend30SecondWarningAsync(closeTimeSpan, RuntimeConfiguration.CanBet);

            // 检查是否需要发送封盘后投注统计
            await CheckAndSendBetSummaryAsync(closeTimeSpan, RuntimeConfiguration.CanBet, issueTime);

            // 更新开奖信息
            UpdateDrawResultDisplay();

            // 更新统计信息（暂时显示占位符）
            label_总人数.Text = @"总人数: 0";
            label_总积分.Text = @"总积分: 0";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"更新期号时间UI时发生异常");
        }
    }

    /// <summary>
    /// 更新收单状态显示
    /// </summary>
    /// <param name="canBet">是否可以投注</param>
    private void UpdateOrderCollectionStatus(bool canBet)
    {
        try
        {
            // 检查游戏服务是否已启动（通过按钮状态判断）
            var isGameServiceRunning = !button_StartService.Enabled && button_StopService.Enabled;

            // 只有在游戏服务已启动且可投注时才显示"正在收单"
            var isCollectingOrders = isGameServiceRunning && canBet;

            if (isCollectingOrders)
            {
                // 正在收单状态
                label_收单状态.Text = @"正在收单";
                label_收单状态.BackColor = Color.LightGreen;
                label_收单状态.ForeColor = Color.DarkGreen;
            }
            else
            {
                // 停止收单状态
                label_收单状态.Text = @"停止收单";
                label_收单状态.BackColor = Color.Cyan; // 恢复原始背景色
                label_收单状态.ForeColor = Color.Red;
            }

            _logger.LogDebug(@"收单状态已更新: {Status}, 游戏服务运行: {GameRunning}, 可投注: {CanBet}",
                label_收单状态.Text, isGameServiceRunning, canBet);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"更新收单状态时发生异常");
        }
    }

    /// <summary>
    /// 更新开奖结果显示
    /// </summary>
    private async void UpdateDrawResultDisplay()
    {
        try
        {
            // 获取最新开奖信息
            var latestDraw = await _drawService.GetDrawLastAsync(RuntimeConfiguration._robotServiceCancellation!.Token);

            if (latestDraw is null || string.IsNullOrEmpty(latestDraw.DrawNum) || latestDraw.DrawNum.Contains("--"))
            {
                // 没有开奖数据，显示占位符
                Invoke(() =>
                {
                    label_开奖期数.Text = @"--";
                    label_开奖号码.Text = @"--";
                    label_番摊结果.Text = @"--";
                });
                return;
            }

            // 使用SettlementService计算番摊结果（统一逻辑）
            var drawNumber = _settlementService.CalculateFanTanResult(latestDraw.DrawNum);

            if (drawNumber == 0)
            {
                // 无法计算番摊结果，显示占位符
                Invoke(() =>
                {
                    label_开奖期数.Text = latestDraw.Issue;
                    label_开奖号码.Text = @"--";
                    label_番摊结果.Text = @"--";
                });
                return;
            }

            // 解析开奖号码并计算总和（用于显示）
            var (numbers, isValid) = _settlementService.ParseTaiwanBingoNumbers(latestDraw.DrawNum);
            var sum = isValid ? numbers.Sum() : 0;

            // 格式化开奖号码显示：如果总和大于100，只显示十位和个位
            var displaySum = sum > 100 ? sum % 100 : sum;

            // 更新UI显示
            Invoke(() =>
            {
                label_开奖期数.Text = latestDraw.Issue;
                label_开奖号码.Text = displaySum.ToString();
                label_番摊结果.Text = drawNumber.ToString();

                // 设置番摊结果的颜色
                label_番摊结果.ForeColor = drawNumber switch
                {
                    1 => Color.Red,
                    2 => Color.Blue,
                    3 => Color.Green,
                    4 => Color.Purple,
                    _ => Color.Black
                };
            });

            _logger.LogDebug(@"更新开奖结果显示 - 期号: {Issue}, 前20个号码总和: {Sum}, 显示数值: {DisplaySum}, 番摊结果: {FanTanResult}",
                latestDraw.Issue, sum, displaySum, drawNumber);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"更新开奖结果显示时发生异常");

            // 发生异常时显示占位符
            Invoke(() =>
            {
                label_开奖期数.Text = @"--";
                label_开奖号码.Text = @"--";
                label_番摊结果.Text = @"--";
            });
        }
    }

    #endregion

    #region 资源释放

    /// <summary>
    /// 窗体关闭事件处理
    /// </summary>
    private async void FormMain_FormClosing(object? sender, FormClosingEventArgs e)
    {
        try
        {
            _logger.LogInformation(@"开始关闭程序，正在清理资源...");

            // 1. 停止定时器
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
            _currentIssueBetUpdateTimer?.Stop();
            _currentIssueBetUpdateTimer?.Dispose();
            _logger.LogDebug(@"定时器已停止并释放");

            // 2. 取消所有后台任务
            try
            {
                RuntimeConfiguration._robotServiceCancellation?.Cancel();
                _logger.LogDebug(@"后台服务取消令牌已触发");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, @"取消后台服务时发生异常");
            }

            // 3. 停止HTTP API服务
            try
            {
                await _httpApiService.StopAsync();
                _logger.LogDebug(@"HTTP API服务已停止");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, @"停止HTTP API服务时发生异常");
            }

            // 4. 释放BindingSource资源
            try
            {
                _memberBindingSource?.Dispose();
                _depositBindingSource?.Dispose();
                _withdrawBindingSource?.Dispose();
                _logger.LogDebug(@"BindingSource资源已释放");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, @"释放BindingSource资源时发生异常");
            }

            // 5. 释放字体资源
            try
            {
                // 注意：静态字体对象在应用程序关闭时会自动释放，这里不需要手动释放
                _logger.LogDebug(@"字体资源释放检查完成");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, @"释放字体资源时发生异常");
            }

            // 5. 等待后台任务完成清理
            _logger.LogInformation(@"等待后台任务完成清理...");
            await Task.Delay(2000);

            // 6. 强制垃圾回收
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            _logger.LogInformation(@"FormMain资源释放完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"释放FormMain资源时发生异常");
        }
    }

    /// <summary>
    /// 窗体已关闭事件处理
    /// </summary>
    private void FormMain_FormClosed(object? sender, FormClosedEventArgs e)
    {
        try
        {
            _logger.LogInformation(@"FormMain已关闭，强制退出应用程序");

            // 强制退出应用程序
            Application.Exit();

            // 如果Application.Exit()没有立即生效，使用Environment.Exit()
            Task.Run(async () =>
            {
                await Task.Delay(2000);
                _logger.LogWarning(@"应用程序未能正常退出，强制终止进程");
                Environment.Exit(0);
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"FormClosed事件处理失败");
            Environment.Exit(1);
        }
    }

    #endregion

    #region 按钮状态管理

    /// <summary>
    /// 更新所有服务控制按钮的状态
    /// </summary>
    private void UpdateServiceControlButtonsState()
    {
        try
        {
            if (InvokeRequired)
            {
                Invoke(UpdateServiceControlButtonsState);
                return;
            }

            // 检查是否有可用的群组
            var hasGroups = comboBox_WorkGroupId.Items.Count > 0;

            // 检查游戏服务状态
            var isGameServiceStarted = RuntimeConfiguration.IsGameServiceStarted;

            // 检查飞单功能状态
            var isFlightOrderEnabled = RuntimeConfiguration.IsFlightOrderEnabled;

            // 更新按钮状态
            UpdateStartServiceButton(hasGroups, isGameServiceStarted);
            UpdateStopServiceButton(isGameServiceStarted);
            UpdateStartBetButton(hasGroups, isGameServiceStarted, isFlightOrderEnabled);
            UpdateStopBetButton(isFlightOrderEnabled);

            _logger.LogDebug(@"服务控制按钮状态已更新 - 有群组: {HasGroups}, 游戏服务: {GameService}, 飞单功能: {FlightOrder}",
                hasGroups, isGameServiceStarted, isFlightOrderEnabled);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"更新服务控制按钮状态失败");
        }
    }

    /// <summary>
    /// 更新开始游戏按钮状态
    /// </summary>
    private void UpdateStartServiceButton(bool hasGroups, bool isGameServiceStarted)
    {
        // 只有在有群组且游戏服务未启动时才启用
        button_StartService.Enabled = hasGroups && !isGameServiceStarted;

        if (isGameServiceStarted)
        {
            button_StartService.Text = @"游戏中";
            button_StartService.BackColor = Color.LightGreen;
        }
        else
        {
            button_StartService.Text = @"开始游戏";
            button_StartService.BackColor = SystemColors.Control;
        }
    }

    /// <summary>
    /// 更新停止游戏按钮状态
    /// </summary>
    private void UpdateStopServiceButton(bool isGameServiceStarted)
    {
        // 只有在游戏服务已启动时才启用
        button_StopService.Enabled = isGameServiceStarted;
        button_StopService.Text = @"停止游戏";
    }

    /// <summary>
    /// 更新开始飞单按钮状态
    /// </summary>
    private void UpdateStartBetButton(bool hasGroups, bool isGameServiceStarted, bool isFlightOrderEnabled)
    {
        // 只有在有群组且飞单功能未启用时才启用（不需要等游戏服务启动）
        button_StartBet.Enabled = hasGroups && !isFlightOrderEnabled;

        if (isFlightOrderEnabled)
        {
            button_StartBet.Text = @"飞单中";
            button_StartBet.BackColor = Color.LightBlue;
        }
        else
        {
            button_StartBet.Text = @"开始飞单";
            button_StartBet.BackColor = SystemColors.Control;
        }
    }

    /// <summary>
    /// 更新停止飞单按钮状态
    /// </summary>
    private void UpdateStopBetButton(bool isFlightOrderEnabled)
    {
        // 只有在飞单功能已启用时才启用
        button_StopBet.Enabled = isFlightOrderEnabled;
        button_StopBet.Text = @"停止飞单";
    }

    #endregion

    #region 工作群组管理

    /// <summary>
    /// 检查群组信息是否有更新，如果有则更新ComboBox
    /// </summary>
    private void CheckAndUpdateGroupInfo()
    {
        try
        {
            var currentGroupCount = RuntimeConfiguration.GetGroupCount();
            if (currentGroupCount != _lastGroupCount)
            {
                _logger.LogInformation(@"检测到群组信息变化，从 {OldCount} 个群组更新为 {NewCount} 个群组",
                    _lastGroupCount, currentGroupCount);

                UpdateWorkGroupComboBox();
                _lastGroupCount = currentGroupCount;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"检查群组信息变化时发生异常");
        }
    }

    /// <summary>
    /// 更新工作群组ComboBox的数据源
    /// 从RuntimeConfiguration中获取群组信息并更新到UI
    /// </summary>
    public void UpdateWorkGroupComboBox()
    {
        try
        {
            if (InvokeRequired)
            {
                Invoke(UpdateWorkGroupComboBox);
                return;
            }

            // 保存当前选择的群组ID
            var currentSelectedGroupId = GetSelectedWorkGroupId();

            // 清空现有项目
            comboBox_WorkGroupId.Items.Clear();

            // 获取群组信息
            var groupCount = RuntimeConfiguration.GetGroupCount();
            if (groupCount > 0)
            {
                // 从RuntimeConfiguration获取群组信息并添加到ComboBox
                var robotInfo = RuntimeConfiguration.GetRobotInfo();
                foreach (var group in robotInfo.GroupDic)
                {
                    var displayText = string.IsNullOrWhiteSpace(group.Value)
                        ? group.Key
                        : $@"{group.Key} - {group.Value}";

                    comboBox_WorkGroupId.Items.Add(new GroupItem(group.Key, displayText));
                }

                // 尝试恢复之前的选择
                if (!string.IsNullOrEmpty(currentSelectedGroupId))
                {
                    var itemToSelect = comboBox_WorkGroupId.Items.Cast<object>()
                        .FirstOrDefault(item => item is GroupItem groupItem && groupItem.GroupId == currentSelectedGroupId);

                    if (itemToSelect != null)
                    {
                        comboBox_WorkGroupId.SelectedItem = itemToSelect;
                    }
                    else
                    {
                        // 如果之前的选择不存在，选择第一项
                        comboBox_WorkGroupId.SelectedIndex = 0;
                    }
                }
                else
                {
                    // 没有之前的选择，选择第一项
                    comboBox_WorkGroupId.SelectedIndex = 0;
                }

                _logger.LogInformation(@"工作群组ComboBox已更新，共 {GroupCount} 个群组，已选择第一项", groupCount);
            }
            else
            {
                // 没有群组信息，ComboBox保持为空
                _logger.LogDebug(@"没有可用的群组信息，ComboBox保持为空");
            }

            // 更新按钮状态
            UpdateServiceControlButtonsState();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"更新工作群组ComboBox失败");
        }
    }

    /// <summary>
    /// 获取当前选择的工作群组ID
    /// </summary>
    /// <returns>选择的群组ID，如果没有选择则返回空字符串</returns>
    private string GetSelectedWorkGroupId()
    {
        try
        {
            if (comboBox_WorkGroupId.SelectedItem is GroupItem groupItem)
            {
                return groupItem.GroupId;
            }

            return string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"获取选择的工作群组ID失败");
            return string.Empty;
        }
    }


    /// <summary>
    /// 发送游戏开始前的图片
    /// </summary>
    private async Task SendGameStartImagesAsync()
    {
        try
        {
            _logger.LogInformation(@"开始发送游戏开始前的图片");

            // 定义要发送的图片文件名
            var imageFiles = new[] { "draw.jpeg", "tan6.jpeg", "tan7.jpeg" };

            // 获取应用程序目录下的Images文件夹
            var appDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var imagesDirectory = Path.Combine(appDirectory, "Images");

            foreach (var imageFile in imageFiles)
            {
                try
                {
                    // 构建图片完整路径（在Images文件夹中）
                    var imagePath = Path.Combine(imagesDirectory, imageFile);

                    // 检查文件是否存在
                    if (File.Exists(imagePath))
                    {
                        // 发送图片
                        await _chatService.SendImageAsync(imagePath);
                        _logger.LogInformation(@"成功发送图片: {ImageFile}", imageFile);

                        // 图片之间添加短暂延迟，避免发送过快
                        await Task.Delay(1000);
                    }
                    else
                    {
                        _logger.LogWarning(@"图片文件不存在: {ImagePath}", imagePath);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, @"发送图片失败: {ImageFile}", imageFile);
                }
            }

            _logger.LogInformation(@"游戏开始前的图片发送完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"发送游戏开始前的图片时发生异常");
        }
    }

    /// <summary>
    /// 通知聊天平台游戏已开始
    /// </summary>
    /// <param name="groupName">群组名称</param>
    private async Task NotifyChatPlatformGameStartedAsync(string groupName)
    {
        try
        {
            var message = @"大家好";
            await _chatService.SendGroupMessageAsync(message);
            message = @"★开始游戏★";
            await _chatService.SendGroupMessageAsync(message);

            // 判断状态发送开盘通知消息
            if (RuntimeConfiguration.CanBet)
            {
                // 发送开盘通知消息
                message = $@"{EmojiHelper.GetSun()}开★始★答★题{EmojiHelper.GetSun()}";
                await _chatService.SendGroupMessageAsync(message);
            }

            _logger.LogInformation(@"游戏开始通知已发送到聊天平台，群组: {GroupName}", groupName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"发送游戏开始通知到聊天平台失败");
            // 不抛出异常，避免影响游戏启动流程
        }
    }

    /// <summary>
    /// 通知聊天平台游戏已停止
    /// </summary>
    private async Task NotifyChatPlatformGameStoppedAsync()
    {
        try
        {
            var message = @"★停止考试★";
            await _chatService.SendGroupMessageAsync(message);
            _logger.LogInformation(@"游戏停止通知已发送到聊天平台");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"发送游戏停止通知到聊天平台失败");
            // 不抛出异常，避免影响游戏停止流程
        }
    }

    #endregion


    #region 群组项目类

    /// <summary>
    /// 群组项目类，用于ComboBox的数据项
    /// </summary>
    private class GroupItem
    {
        public string GroupId { get; }
        public string DisplayText { get; }

        public GroupItem(string groupId, string displayText)
        {
            GroupId = groupId;
            DisplayText = displayText;
        }

        public override string ToString()
        {
            return DisplayText;
        }
    }

    #endregion
}