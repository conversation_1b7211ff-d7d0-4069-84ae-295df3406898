using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

/// <summary>
/// 系统设置行数据结构
/// </summary>
public class SettingRowData
{
    /// <summary>
    /// 原始设置键名
    /// </summary>
    public string OriginalKey { get; set; } = string.Empty;

    /// <summary>
    /// 数据类型
    /// </summary>
    public string DataType { get; set; } = string.Empty;

    /// <summary>
    /// 是否为Boolean类型
    /// </summary>
    public bool IsBooleanType { get; set; }
}

/// <summary>
/// 系统设置页面
/// </summary>
public partial class FormMain
{
    #region 系统设置管理相关方法

    /// <summary>
    /// 标记是否正在加载数据（防止加载时触发自动保存）
    /// </summary>
    private bool _isLoadingSettings = false;

    /// <summary>
    /// 加载系统设置数据到控件
    /// </summary>
    private async Task LoadSystemSettingsDataAsync()
    {
        try
        {
            // 设置加载标志，防止触发自动保存
            _isLoadingSettings = true;

            // 获取所有系统设置
            var settings = await _systemSettingService.GetAllSettingsAsync();

            // 确保在UI线程中更新控件
            if (InvokeRequired)
            {
                Invoke(() => LoadSettingsToControls(settings));
            }
            else
            {
                LoadSettingsToControls(settings);
            }

            _logger.LogInformation(@"已加载 {Count} 个系统设置项到控件", settings.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"加载系统设置数据失败");

            // 确保在UI线程中显示错误消息
            if (InvokeRequired)
            {
                Invoke(() => MessageBox.Show(@"加载系统设置数据失败：" + ex.Message, @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error));
            }
            else
            {
                MessageBox.Show(@"加载系统设置数据失败：" + ex.Message, @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        finally
        {
            // 确保清除加载标志
            _isLoadingSettings = false;
        }
    }

    /// <summary>
    /// 将设置数据加载到控件中
    /// </summary>
    /// <param name="settings">系统设置列表</param>
    private void LoadSettingsToControls(List<SystemSetting> settings)
    {
        try
        {
            // 创建设置字典以便快速查找
            var settingDict = settings.ToDictionary(s => s.SettingKey, s => s.SettingValue);

            // 加载Boolean类型设置到CheckBox控件
            LoadBooleanSetting(settingDict, @"发送6路图", checkBox_发送6路图);
            LoadBooleanSetting(settingDict, @"发送7路图", checkBox_发送7路图);
            LoadBooleanSetting(settingDict, @"开启对冲", checkBox_开启对冲);
            LoadBooleanSetting(settingDict, @"假人自动上分", checkBox_假人自动上分);
            LoadBooleanSetting(settingDict, @"自动回水", checkBox_自动回水);
            LoadBooleanSetting(settingDict, @"开启图片背景", checkBox_开启图片背景);

            // 加载数值类型设置到NumericUpDown控件
            LoadDecimalSetting(settingDict, @"返点比例", numericUpDown_返点比例);

            _logger.LogDebug(@"系统设置已加载到控件");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"加载设置到控件失败");
        }
    }

    /// <summary>
    /// 加载Boolean类型设置到CheckBox
    /// </summary>
    private void LoadBooleanSetting(Dictionary<string, string> settingDict, string key, CheckBox checkBox)
    {
        if (settingDict.TryGetValue(key, out var value))
        {
            checkBox.Checked = value.Equals(@"True", StringComparison.OrdinalIgnoreCase);
        }
    }

    /// <summary>
    /// 加载Decimal类型设置到NumericUpDown
    /// </summary>
    private void LoadDecimalSetting(Dictionary<string, string> settingDict, string key, NumericUpDown numericUpDown)
    {
        if (settingDict.TryGetValue(key, out var value) && decimal.TryParse(value, out var decimalValue))
        {
            numericUpDown.Value = Math.Max(numericUpDown.Minimum, Math.Min(numericUpDown.Maximum, decimalValue));
        }
    }

    /// <summary>
    /// 加载Integer类型设置到NumericUpDown
    /// </summary>
    private void LoadIntegerSetting(Dictionary<string, string> settingDict, string key, NumericUpDown numericUpDown)
    {
        if (settingDict.TryGetValue(key, out var value) && int.TryParse(value, out var intValue))
        {
            numericUpDown.Value = Math.Max(numericUpDown.Minimum, Math.Min(numericUpDown.Maximum, intValue));
        }
    }





    /// <summary>
    /// 获取设置项的友好中文名称
    /// </summary>
    private string GetSettingFriendlyName(string key)
    {
        return key switch
        {
            // 业务开关类
            "是否发送开奖图" => "发送开奖图",
            "发送6路图" => "发送6行路子图",
            "发送7路图" => "发送7行路子图",
            "是否允许撤单" => "允许撤单",
            "开启对冲" => "对冲吃单",
            "假人自动上分" => "假人自动上分",
            "自动回水" => "自动回水",
            "开启图片背景" => "显示图片背景",

            // 业务参数类
            "会员返点比例" => "会员返点比例",
            "拉手返点比例" => "拉手返点比例",
            "开盘时间" => "开盘时间",
            "封盘时间" => "封盘时间",
            "最低飞单金额" => "最低飞单金额",

            // 界面设置类
            "ImgType" => "图片类型",
            "ImageTitleBg" => "图片标题背景",

            // 机器人设置类
            "PlatformHost" => "平台地址",

            // 英文配置项的友好名称
            "MemberRebateRate" => "会员返点比例",
            "AgentRebateRate" => "拉手返点比例",
            "IsFlightOrderEnabled" => "飞单模式",
            "CloseTimeSpan" => "封盘时间",
            "MaxBetAmount" => "最大投注金额",
            "MinBetAmount" => "最小投注金额",
            _ => key // 如果没有匹配的友好名称，返回原始key
        };
    }

    /// <summary>
    /// 获取设置项的详细描述
    /// </summary>
    private string GetSettingDescription(string key)
    {
        return key switch
        {
            // 业务开关类
            "是否发送开奖图" => "控制是否在开奖后自动发送开奖图片到群组",
            "发送6路图" => "控制是否在开奖后自动发送6行摊路图到群组",
            "发送7路图" => "控制是否在开奖后自动发送7行摊路图到群组",
            "是否允许撤单" => "控制用户是否可以撤销已提交的投注",
            "开启对冲" => "控制是否启用对冲吃单功能",
            "假人自动上分" => "控制假人账户是否自动上分",
            "自动回水" => "控制是否自动给用户返水",
            "开启图片背景" => "控制路子图是否显示标题背景",

            // 业务参数类
            "返点比例" => "新创建会员的默认返点比例，单位：%",

            // 界面设置类
            "ImgType" => "图片类型设置参数",
            "ImageTitleBg" => "控制路子图是否显示标题背景，1=显示，0=不显示",

            // 飞单平台设置类
            "PlatformHost" => "飞单平台Api地址",

            // 英文配置项的描述
            "MemberRebateRate" => "新创建会员的默认返点比例，单位：%",
            "IsFlightOrderEnabled" => "True=中介收单模式，False=庄家模式",
            "CloseTimeSpan" => "每期停止接受投注的时间，单位：秒",
            "MaxBetAmount" => "单次投注允许的最大金额",
            "MinBetAmount" => "单次投注允许的最小金额",
            _ => "系统配置参数"
        };
    }

    /// <summary>
    /// 获取设置项的数据类型（用于验证）
    /// </summary>
    private string GetSettingDataType(string key)
    {
        return key switch
        {
            // 中文配置项
            "返点比例" => "decimal",
            "是否发送开奖图" or "发送6路图" or "是否允许撤单" or "开启对冲" or "假人自动上分" or "自动回水" or "开启飞单" => "bool",
            "开盘时间" or "封盘时间" or "最低飞单金额" or "ImgType" => "int",

            // 英文配置项
            "MemberRebateRate" => "decimal",
            "IsFlightOrderEnabled" or "ImageTitleBg" or "TaiwanBingo3Enabled" or "AutoSettlementEnabled" => "bool",
            "OpenTimeSpan" or "CloseTimeSpan" or "MaxBetAmount" or "MinBetAmount" or "DailyWithdrawLimit" or "BetTimeoutSeconds" => "int",

            _ => "string"
        };
    }

    /// <summary>
    /// CheckBox值变化时自动保存 - 实时设置保存
    ///
    /// 功能：
    /// - 检测CheckBox状态变化
    /// - 自动映射控件名称到设置键名
    /// - 立即保存到数据库
    /// - 保存失败时自动恢复原值
    /// - 防止数据加载时触发保存
    ///
    /// 用户体验：
    /// - 即时保存，无需手动操作
    /// - 失败时有明确提示
    /// - 不会丢失用户操作
    /// </summary>
    private async void CheckBox_系统设置_CheckedChanged(object? sender, EventArgs e)
    {
        // 如果正在加载数据，不触发自动保存
        if (_isLoadingSettings) return;

        if (sender is not CheckBox checkBox) return;

        try
        {
            // 根据CheckBox确定对应的设置键名
            var settingKey = checkBox.Name switch
            {
                "checkBox_发送6路图" => "发送6路图",
                "checkBox_发送7路图" => "发送7路图",
                "checkBox_开启对冲" => "开启对冲",
                "checkBox_假人自动上分" => "假人自动上分",
                "checkBox_自动回水" => "自动回水",
                "checkBox_开启图片背景" => "开启图片背景",
                _ => null
            };

            if (string.IsNullOrEmpty(settingKey)) return;

            // 保存设置值
            var value = checkBox.Checked ? "True" : "False";
            var success = await _systemSettingService.SetSettingValueAsync(settingKey, value);

            if (success)
            {
                _logger.LogDebug(@"自动保存系统设置成功: {Key} = {Value}", settingKey, value);
            }
            else
            {
                _logger.LogWarning(@"自动保存系统设置失败: {Key} = {Value}", settingKey, value);

                // 保存失败时恢复原值
                checkBox.CheckedChanged -= CheckBox_系统设置_CheckedChanged;
                checkBox.Checked = !checkBox.Checked;
                checkBox.CheckedChanged += CheckBox_系统设置_CheckedChanged;

                MessageBox.Show($@"保存设置 '{GetSettingFriendlyName(settingKey)}' 失败",
                    @"保存失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"自动保存系统设置时发生异常");
            MessageBox.Show(@"保存设置时发生错误：" + ex.Message,
                @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// NumericUpDown值变化时自动保存 - 数值参数实时保存
    ///
    /// 功能：
    /// - 检测数值控件变化（支持小数调整）
    /// - 自动映射控件名称到设置键名
    /// - 立即保存到数据库
    /// - 保存失败时有明确提示
    /// - 防止数据加载时触发保存
    ///
    /// 支持的控件：
    /// - 返点比例：支持0.1%精度调整
    /// - 其他数值参数可扩展
    /// </summary>
    private async void NumericUpDown_系统设置_ValueChanged(object? sender, EventArgs e)
    {
        // 如果正在加载数据，不触发自动保存
        if (_isLoadingSettings) return;

        if (sender is not NumericUpDown numericUpDown) return;

        try
        {
            // 根据NumericUpDown确定对应的设置键名
            var settingKey = numericUpDown.Name switch
            {
                "numericUpDown_返点比例" => "返点比例",
                _ => null
            };

            if (string.IsNullOrEmpty(settingKey)) return;

            // 保存设置值
            var value = numericUpDown.Value.ToString();
            var success = await _systemSettingService.SetSettingValueAsync(settingKey, value);

            if (success)
            {
                _logger.LogDebug(@"自动保存系统设置成功: {Key} = {Value}", settingKey, value);
            }
            else
            {
                _logger.LogWarning(@"自动保存系统设置失败: {Key} = {Value}", settingKey, value);
                MessageBox.Show($@"保存设置 '{GetSettingFriendlyName(settingKey)}' 失败",
                    @"保存失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"自动保存系统设置时发生异常");
            MessageBox.Show(@"保存设置时发生错误：" + ex.Message,
                @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }



    /// <summary>
    /// 验证设置值的数据类型
    /// </summary>
    private bool ValidateSettingValue(string key, string value, string dataType)
    {
        if (string.IsNullOrWhiteSpace(value)) return true; // 允许空值

        return dataType switch
        {
            "bool" => bool.TryParse(value, out _),
            "int" => int.TryParse(value, out _),
            "decimal" => decimal.TryParse(value, out _),
            "string" => true,
            _ => true
        };
    }





    #endregion

    /// <summary>
    /// 清除一切记录按钮点击事件
    /// </summary>
    private async void button_清除一切记录_Click(object sender, EventArgs e)
    {
        try
        {
            // 显示确认对话框
            var confirmMessage = @"⚠️ 危险操作确认

您即将清除以下所有数据：
• 会员信息
• 投注记录
• 上下分记录
• 输赢流水记录
• 拉手返点记录
• 财务记录
• 消息记录
• 日志文件（logs目录）

注意：
✓ 保留用户自定义的系统参数设置
✓ 保留管理员账户
✗ 此操作不可恢复！

确认要继续吗？";

            var result = MessageBox.Show(confirmMessage, @"清除一切记录确认",
                MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2);

            if (result == DialogResult.Yes)
            {
                // 二次确认
                var secondConfirm = MessageBox.Show(@"最后确认：您真的要清除一切记录吗？\n\n此操作不可恢复！",
                    @"最终确认", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2);

                if (secondConfirm == DialogResult.Yes)
                {
                    button_清除一切记录.Enabled = false;
                    button_清除一切记录.Text = @"清除中...";

                    _logger.LogWarning(@"用户确认清除一切记录操作");

                    var success = await _systemManagementService.ClearAllRecordsAsync(@"管理员");

                    if (success)
                    {
                        _logger.LogWarning(@"清除一切记录操作成功");
                        MessageBox.Show(@"清除一切记录操作完成！\n\n系统已清除所有业务数据，但保留了用户设置。\n建议重启程序以刷新界面数据。",
                            @"操作完成", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // 刷新界面数据
                        await RefreshAllDataAsync();
                    }
                    else
                    {
                        _logger.LogError(@"清除一切记录操作失败");
                        MessageBox.Show(@"清除一切记录操作失败！\n请检查日志了解详细信息。",
                            @"操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"清除一切记录操作异常");
            MessageBox.Show($@"清除一切记录操作失败：{ex.Message}",
                @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            button_清除一切记录.Enabled = true;
            button_清除一切记录.Text = @"清除一切记录";
        }
    }

    /// <summary>
    /// 初始化出厂设置按钮点击事件
    /// </summary>
    private async void button_初始化出厂设置_Click(object sender, EventArgs e)
    {
        try
        {
            // 显示确认对话框
            var confirmMessage = @"🚨 极度危险操作确认

您即将执行出厂设置初始化，这将：

📊 清除所有数据：
• 会员信息
• 投注记录
• 上下分记录
• 输赢流水记录
• 拉手返点记录
• 财务记录
• 消息记录
• 日志文件（logs目录）

⚙️ 重置所有系统参数：
• 返点比例恢复为出厂默认值
• 飞单状态重置
• 所有自定义配置丢失

⚠️ 此操作将系统完全恢复到出厂状态！
⚠️ 此操作不可恢复！

确认要继续吗？";

            var result = MessageBox.Show(confirmMessage, @"初始化出厂设置确认",
                MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2);

            if (result == DialogResult.Yes)
            {
                // 二次确认
                var secondConfirm = MessageBox.Show(@"🚨 最后警告 🚨\n\n您真的要将系统恢复到出厂设置吗？\n\n• 所有数据将被删除\n• 所有设置将被重置\n• 此操作不可恢复！",
                    @"最终确认", MessageBoxButtons.YesNo, MessageBoxIcon.Stop, MessageBoxDefaultButton.Button2);

                if (secondConfirm == DialogResult.Yes)
                {
                    button_初始化出厂设置.Enabled = false;
                    button_初始化出厂设置.Text = @"初始化中...";

                    _logger.LogWarning(@"用户确认初始化出厂设置操作");

                    var success = await _systemManagementService.InitializeFactorySettingsAsync(@"管理员");

                    if (success)
                    {
                        _logger.LogWarning(@"初始化出厂设置操作成功");
                        MessageBox.Show(@"初始化出厂设置操作完成！\n\n系统已恢复到出厂状态。\n程序将自动重启以应用所有更改。",
                            @"操作完成", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // 重启应用程序
                        Application.Restart();
                    }
                    else
                    {
                        _logger.LogError(@"初始化出厂设置操作失败");
                        MessageBox.Show(@"初始化出厂设置操作失败！\n请检查日志了解详细信息。",
                            @"操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"初始化出厂设置操作异常");
            MessageBox.Show($@"初始化出厂设置操作失败：{ex.Message}",
                @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            button_初始化出厂设置.Enabled = true;
            button_初始化出厂设置.Text = @"初始化出厂设置";
        }
    }

    /// <summary>
    /// 刷新所有界面数据
    /// </summary>
    private async Task RefreshAllDataAsync()
    {
        try
        {
            _logger.LogInformation(@"开始刷新所有界面数据");

            // 刷新会员数据
            await LoadMembersAsync();

            // 刷新申请数据
            await LoadDepositRequestsAsync();
            await LoadWithdrawRequestsAsync();

            // 刷新当前期投注数据
            await LoadCurrentIssueBetOrdersAsync();

            // 刷新系统设置界面
            await LoadSystemSettingsDataAsync();

            _logger.LogInformation(@"所有界面数据刷新完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"刷新界面数据失败");
        }
    }
}