using System.Collections.Concurrent;
using CommandGuard.Enums;
using CommandGuard.Models;
using Microsoft.Extensions.DependencyInjection;

namespace CommandGuard.Configuration;

/// <summary>
/// 运行时配置管理类
///
/// 用途：
/// - 管理应用程序运行时的聊天平台配置
/// - 跟踪游戏服务和飞单功能的运行状态
/// - 在不同窗体和服务之间传递运行时状态信息
/// - 提供全局访问的配置和状态管理
///
/// 设计说明：
/// - 使用静态属性便于全局访问
/// - 线程安全的配置和状态更新
/// - 作为全局变量使用，无需依赖注入
/// </summary>
public static class RuntimeConfiguration
{
    #region 私有字段

    /// <summary>
    /// 线程安全锁对象
    /// </summary>
    private static readonly object Lock = new();

    /// <summary>
    /// 机器人信息（私有字段）
    /// </summary>
    private static RobotInfo _robotInfo = new();

    /// <summary>
    /// 当前选择的聊天平台
    /// </summary>
    private static EnumChatApp _selectedChatApp = EnumChatApp.一起聊吧;

    /// <summary>
    /// 游戏服务是否已启动
    /// </summary>
    private static bool _isGameServiceStarted;

    /// <summary>
    /// 飞单功能是否已开启
    /// </summary>
    private static bool _isFlightOrderEnabled;

    /// <summary>
    /// 当前是否可以投注
    /// </summary>
    private static bool _canBet;

    public static CancellationTokenSource? _robotServiceCancellation = new();

    /// <summary>
    /// 服务提供者，用于程序关闭时释放资源
    /// </summary>
    public static ServiceProvider? ServiceProvider { get; set; }

    #endregion

    #region 公共属性

    /// <summary>
    /// 当前选择的聊天平台
    ///
    /// 特性：
    /// - 线程安全的读写操作
    /// - 默认值为"一起聊吧"平台
    ///
    /// 用法：
    /// - 在启动配置窗体中设置用户选择的平台
    /// - 在主窗体和服务中读取当前平台配置
    /// - 在聊天服务中根据平台执行不同逻辑
    /// </summary>
    public static EnumChatApp SelectedChatApp
    {
        get
        {
            lock (Lock)
            {
                return _selectedChatApp;
            }
        }
        set
        {
            lock (Lock)
            {
                _selectedChatApp = value;
            }
        }
    }

    /// <summary>
    /// 游戏服务是否已启动
    ///
    /// 特性：
    /// - 线程安全的读写操作
    /// - 默认值为false（未启动）
    ///
    /// 用法：
    /// - 在启动/停止游戏服务时更新状态
    /// - 在各个组件中检查游戏服务状态
    /// - 用于控制相关功能的启用/禁用
    /// </summary>
    public static bool IsGameServiceStarted
    {
        get
        {
            lock (Lock)
            {
                return _isGameServiceStarted;
            }
        }
        set
        {
            lock (Lock)
            {
                _isGameServiceStarted = value;
            }
        }
    }

    /// <summary>
    /// 飞单功能是否已开启
    ///
    /// 特性：
    /// - 线程安全的读写操作
    /// - 默认值为false（庄家模式）
    ///
    /// 用法：
    /// - 在启用/禁用飞单功能时更新状态
    /// - 在投注处理逻辑中判断运行模式
    /// - 用于UI界面状态显示和控制
    ///
    /// 业务含义：
    /// - true：中介收单模式（飞单模式）
    /// - false：庄家模式（自营模式）
    /// </summary>
    public static bool IsFlightOrderEnabled
    {
        get
        {
            lock (Lock)
            {
                return _isFlightOrderEnabled;
            }
        }
        set
        {
            lock (Lock)
            {
                _isFlightOrderEnabled = value;
            }
        }
    }

    /// <summary>
    /// 当前是否可以投注 - 全局投注状态控制
    ///
    /// 特性：
    /// - 线程安全的读写操作
    /// - 默认值为false（不可投注）
    /// - 综合考虑时间、开奖状态、游戏服务状态
    ///
    /// 设置时机：
    /// - 游戏服务启动时：设置为false（初始状态）
    /// - 开盘且上期已开奖时：设置为true（可以投注）
    /// - 上期未开奖时：设置为false（等待开奖）
    /// - 封盘时：设置为false（不可投注）
    /// - 游戏服务停止时：设置为false（重置状态）
    ///
    /// 使用场景：
    /// - CommandService中投注处理前检查此状态
    /// - FormMain中更新收单状态显示
    /// - 开盘通知逻辑中的状态判断
    ///
    /// 业务含义：
    /// - true：当前可以接受投注（开盘+上期已开奖+游戏服务运行）
    /// - false：当前不可投注（封盘/等待上期开奖/游戏服务未启动）
    /// </summary>
    public static bool CanBet
    {
        get
        {
            lock (Lock)
            {
                return _canBet;
            }
        }
        set
        {
            lock (Lock)
            {
                _canBet = value;
            }
        }
    }

    #endregion

    #region 机器人信息操作方法

    /// <summary>
    /// 线程安全地获取机器人账号
    /// </summary>
    /// <returns>机器人账号</returns>
    public static string GetRobotAccount()
    {
        lock (Lock)
        {
            return _robotInfo.Account;
        }
    }

    /// <summary>
    /// 线程安全地获取机器人昵称
    /// </summary>
    /// <returns>机器人昵称</returns>
    public static string GetRobotNickName()
    {
        lock (Lock)
        {
            return _robotInfo.NickName;
        }
    }

    /// <summary>
    /// 线程安全地获取工作群组ID
    /// </summary>
    /// <returns>工作群组ID</returns>
    public static string GetWorkGroupId()
    {
        lock (Lock)
        {
            return _robotInfo.WorkGroupId;
        }
    }

    /// <summary>
    /// 线程安全地更新机器人账号
    /// </summary>
    /// <param name="account">新的机器人账号</param>
    public static void UpdateRobotAccount(string account)
    {
        lock (Lock)
        {
            _robotInfo.Account = account ?? string.Empty;
        }
    }

    /// <summary>
    /// 线程安全地更新机器人昵称
    /// </summary>
    /// <param name="nickName">新的机器人昵称</param>
    public static void UpdateRobotNickName(string nickName)
    {
        lock (Lock)
        {
            _robotInfo.NickName = nickName ?? string.Empty;
        }
    }

    /// <summary>
    /// 线程安全地更新工作群组ID
    /// </summary>
    /// <param name="workGroupId">新的工作群组ID</param>
    public static void UpdateWorkGroupId(string workGroupId)
    {
        lock (Lock)
        {
            _robotInfo.WorkGroupId = workGroupId ?? string.Empty;
        }
    }

    /// <summary>
    /// 线程安全地添加或更新群组信息
    /// </summary>
    /// <param name="groupId">群组ID</param>
    /// <param name="groupName">群组名称</param>
    public static void AddOrUpdateGroup(string groupId, string groupName)
    {
        if (string.IsNullOrWhiteSpace(groupId)) return;

        lock (Lock)
        {
            _robotInfo.GroupDic.AddOrUpdate(groupId, groupName ?? string.Empty, (_, _) => groupName ?? string.Empty);
        }
    }

    /// <summary>
    /// 线程安全地移除群组信息
    /// </summary>
    /// <param name="groupId">要移除的群组ID</param>
    /// <returns>是否成功移除</returns>
    public static bool RemoveGroup(string groupId)
    {
        if (string.IsNullOrWhiteSpace(groupId)) return false;

        lock (Lock)
        {
            return _robotInfo.GroupDic.TryRemove(groupId, out _);
        }
    }

    /// <summary>
    /// 线程安全地获取群组名称
    /// </summary>
    /// <param name="groupId">群组ID</param>
    /// <returns>群组名称，如果不存在则返回空字符串</returns>
    public static string GetGroupName(string groupId)
    {
        if (string.IsNullOrWhiteSpace(groupId)) return string.Empty;

        lock (Lock)
        {
            return _robotInfo.GroupDic.TryGetValue(groupId, out var groupName) ? groupName : string.Empty;
        }
    }

    /// <summary>
    /// 线程安全地检查群组信息是否为空
    /// </summary>
    /// <returns>如果群组字典为空则返回true，否则返回false</returns>
    public static bool IsGroupDicEmpty()
    {
        lock (Lock)
        {
            return _robotInfo.GroupDic.IsEmpty;
        }
    }

    /// <summary>
    /// 线程安全地获取群组数量
    /// </summary>
    /// <returns>群组数量</returns>
    public static int GetGroupCount()
    {
        lock (Lock)
        {
            return _robotInfo.GroupDic.Count;
        }
    }

    /// <summary>
    /// 线程安全地获取机器人信息的副本
    /// </summary>
    /// <returns>机器人信息的副本</returns>
    public static RobotInfo GetRobotInfo()
    {
        lock (Lock)
        {
            return new RobotInfo
            {
                Account = _robotInfo.Account,
                NickName = _robotInfo.NickName,
                WorkGroupId = _robotInfo.WorkGroupId,
                GroupDic = new ConcurrentDictionary<string, string>(_robotInfo.GroupDic)
            };
        }
    }

    /// <summary>
    /// 线程安全地清空所有群组信息
    /// </summary>
    public static void ClearGroups()
    {
        lock (Lock)
        {
            _robotInfo.GroupDic.Clear();
        }
    }

    /// <summary>
    /// 线程安全地重置机器人信息为默认值
    /// </summary>
    public static void ResetRobotInfo()
    {
        lock (Lock)
        {
            _robotInfo = new RobotInfo();
        }
    }

    #endregion
}