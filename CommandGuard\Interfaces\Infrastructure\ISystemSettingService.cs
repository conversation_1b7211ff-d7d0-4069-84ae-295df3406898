using CommandGuard.Models;

namespace CommandGuard.Interfaces.Infrastructure;

/// <summary>
/// 系统设置数据库服务接口
/// </summary>
public interface ISystemSettingService
{
    /// <summary>
    /// 获取所有系统设置
    /// </summary>
    /// <returns>系统设置列表</returns>
    Task<List<SystemSetting>> GetAllSettingsAsync();
    
    /// <summary>
    /// 根据设置键获取系统设置
    /// </summary>
    /// <param name="settingKey">设置键名</param>
    /// <returns>系统设置</returns>
    Task<SystemSetting?> GetSettingByKeyAsync(string settingKey);

    /// <summary>
    /// 获取设置值（泛型方法）
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="settingKey">设置键名</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>设置值</returns>
    Task<T> GetSettingValueAsync<T>(string settingKey, T defaultValue = default!);

    /// <summary>
    /// 设置值（泛型方法）
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="settingKey">设置键名</param>
    /// <param name="value">设置值</param>
    /// <returns>是否设置成功</returns>
    Task<bool> SetSettingValueAsync<T>(string settingKey, T value);

    /// <summary>
    /// 创建系统设置
    /// </summary>
    /// <param name="systemSetting">系统设置</param>
    /// <returns>是否创建成功</returns>
    Task<bool> CreateSettingAsync(SystemSetting systemSetting);

    /// <summary>
    /// 更新系统设置
    /// </summary>
    /// <param name="systemSetting">系统设置</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateSettingAsync(SystemSetting systemSetting);

    /// <summary>
    /// 删除系统设置
    /// </summary>
    /// <param name="id">设置ID</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteSettingAsync(int id);

    /// <summary>
    /// 批量更新系统设置
    /// </summary>
    /// <param name="systemSettings">系统设置列表</param>
    /// <returns>是否更新成功</returns>
    Task<bool> BatchUpdateSettingsAsync(List<SystemSetting> systemSettings);

    /// <summary>
    /// 初始化默认系统设置
    /// </summary>
    /// <returns>是否初始化成功</returns>
    Task<bool> InitializeDefaultSettingsAsync();

    /// <summary>
    /// 检查设置是否存在
    /// </summary>
    /// <param name="settingKey">设置键名</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(string settingKey);
    
    #region 返点配置便捷方法

    /// <summary>
    /// 获取会员默认返点比例
    /// </summary>
    /// <returns>会员默认返点比例</returns>
    Task<decimal> GetDefaultMemberRebatePercentAsync();

    /// <summary>
    /// 更新会员返点比例
    /// </summary>
    /// <param name="rebatePercent">返点比例</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateMemberRebatePercentAsync(decimal rebatePercent);

    #endregion
}
