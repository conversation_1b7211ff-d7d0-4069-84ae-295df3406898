using CommandGuard.Enums;
using CommandGuard.Interfaces.Business;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Business;

/// <summary>
/// 财务服务实现 - 企业级财务管理服务
/// 提供完整的财务操作功能：余额管理、财务记录、上分下分处
/// 包含完整的参数验证、异常处理、事务管理、审计日
/// 支持异步操作、并发安全、数据一致性保障
/// </summary>
public class FinancialService(IFreeSql fSql, ILogger<FinancialService> logger, IMemberService memberService) : IFinancialService
{
    #region 余额操作

    /// <summary>
    /// 增加用户余额 - 充值、中奖等增加余额操作
    ///
    /// 功能：
    /// - 验证参数有效性
    /// - 增加用户余额
    /// - 创建财务记录
    /// - 支持事务回滚
    ///
    /// 业务场景：
    /// - 用户充值（上分）
    /// - 投注中奖
    /// - 管理员调整余额
    /// - 返水奖励
    /// </summary>
    public async Task<bool> IncreaseBalanceAsync(string account, decimal amount, string referenceType, long referenceId,
        string description, string @operator, string note = "")
    {
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException(@"用户账号不能为空", nameof(account));

        if (amount <= 0)
            throw new ArgumentException(@"增加金额必须大于0", nameof(amount));

        try
        {
            // 获取当前余额
            var currentBalance = await GetCurrentBalanceAsync(account).ConfigureAwait(false);
            var newBalance = currentBalance + amount;

            // 更新用户余额
            var updateResult = await fSql.Update<Member>()
                .Set(m => m.Balance, newBalance)
                .Where(m => m.Account == account)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            if (updateResult == 0)
            {
                logger.LogWarning(@"用户不存在，无法增加余额，账�? {Account}", account);
                return false;
            }

            // 创建财务记录
            var record = new FinancialRecord
            {
                Account = account,
                Type = EnumFinancialType.Deposit,
                Amount = amount,
                BalanceBefore = currentBalance,
                BalanceAfter = newBalance,
                ReferenceType = referenceType,
                ReferenceId = referenceId,
                Description = description,
                Operator = @operator,
                Note = note,
                CreatedTime = DateTime.Now
            };

            await CreateFinancialRecordAsync(record).ConfigureAwait(false);

            logger.LogInformation(@"增加用户余额成功，账�? {Account}, 金额: {Amount}, 余额: {Before} -> {After}",
                account, amount, currentBalance, newBalance);

            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"增加用户余额失败，账�? {Account}, 金额: {Amount}", account, amount);
            return false;
        }
    }

    /// <summary>
    /// 减少用户余额
    /// </summary>
    public async Task<bool> DecreaseBalanceAsync(string account, decimal amount, string referenceType, long referenceId,
        string description, string @operator, string note = "")
    {
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException(@"用户账号不能为空", nameof(account));

        if (amount <= 0)
            throw new ArgumentException(@"减少金额必须大于0", nameof(amount));

        try
        {
            // 获取当前余额
            var currentBalance = await GetCurrentBalanceAsync(account).ConfigureAwait(false);

            if (currentBalance < amount)
            {
                logger.LogWarning(@"用户余额不足，无法减少，账号: {Account}, 当前余额: {Balance}, 需要金�? {Amount}",
                    account, currentBalance, amount);
                return false;
            }

            var newBalance = currentBalance - amount;

            // 更新用户余额
            var updateResult = await fSql.Update<Member>()
                .Set(m => m.Balance, newBalance)
                .Where(m => m.Account == account)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            if (updateResult == 0)
            {
                logger.LogWarning(@"用户不存在，无法减少余额，账�? {Account}", account);
                return false;
            }

            // 创建财务记录
            var record = new FinancialRecord
            {
                Account = account,
                Type = EnumFinancialType.Withdraw,
                Amount = amount,
                BalanceBefore = currentBalance,
                BalanceAfter = newBalance,
                ReferenceType = referenceType,
                ReferenceId = referenceId,
                Description = description,
                Operator = @operator,
                Note = note,
                CreatedTime = DateTime.Now
            };

            await CreateFinancialRecordAsync(record).ConfigureAwait(false);

            logger.LogInformation(@"减少用户余额成功，账�? {Account}, 金额: {Amount}, 余额: {Before} -> {After}",
                account, amount, currentBalance, newBalance);

            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"减少用户余额失败，账�? {Account}, 金额: {Amount}", account, amount);
            return false;
        }
    }

    /// <summary>
    /// 检查用户余额是否充足 - 投注前余额验证
    ///
    /// 功能：
    /// - 验证参数有效性
    /// - 查询用户当前余额
    /// - 比较余额与所需金额
    ///
    /// 用途：
    /// - 投注前余额检查
    /// - 提现前余额验证
    /// - 其他扣费操作前的验证
    /// </summary>
    public async Task<bool> CheckBalanceSufficientAsync(string account, decimal amount)
    {
        if (string.IsNullOrWhiteSpace(account) || amount <= 0)
            return false;

        try
        {
            var currentBalance = await GetCurrentBalanceAsync(account).ConfigureAwait(false);
            return currentBalance >= amount;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"检查用户余额失败，账号: {Account}", account);
            return false;
        }
    }

    /// <summary>
    /// 获取用户当前余额
    /// </summary>
    public async Task<decimal> GetCurrentBalanceAsync(string account)
    {
        if (string.IsNullOrWhiteSpace(account))
            return 0;

        try
        {
            var member = await memberService.GetMemberAsync(account).ConfigureAwait(false);
            return member?.Balance ?? 0;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取用户余额失败，账�? {Account}", account);
            return 0;
        }
    }

    #endregion

    #region 财务记录

    /// <summary>
    /// 创建财务记录
    /// </summary>
    public async Task<long> CreateFinancialRecordAsync(FinancialRecord record)
    {
        if (record == null)
            throw new ArgumentNullException(nameof(record));

        try
        {
            var recordId = await fSql.Insert<FinancialRecord>()
                .AppendData(record)
                .ExecuteIdentityAsync()
                .ConfigureAwait(false);

            logger.LogDebug(@"创建财务记录成功，ID: {RecordId}, 账号: {Account}, 类型: {Type}, 金额: {Amount}",
                recordId, record.Account, record.Type, record.Amount);

            return recordId;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"创建财务记录失败，账�? {Account}", record.Account);
            throw;
        }
    }

    /// <summary>
    /// 获取用户财务记录
    /// </summary>
    public async Task<List<FinancialRecord>> GetFinancialRecordsAsync(string account, DateTime? startTime = null,
        DateTime? endTime = null, EnumFinancialType? type = null)
    {
        if (string.IsNullOrWhiteSpace(account))
            return [];

        try
        {
            var query = fSql.Select<FinancialRecord>()
                .Where(r => r.Account == account);

            if (startTime.HasValue)
                query = query.Where(r => r.CreatedTime >= startTime.Value);

            if (endTime.HasValue)
                query = query.Where(r => r.CreatedTime <= endTime.Value);

            if (type.HasValue)
                query = query.Where(r => r.Type == type.Value);

            return await query
                .OrderByDescending(r => r.CreatedTime)
                .ToListAsync()
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取用户财务记录失败，账�? {Account}", account);
            return [];
        }
    }

    /// <summary>
    /// 获取财务统计信息
    /// </summary>
    public async Task<FinancialStatistics> GetFinancialStatisticsAsync(DateTime? startTime = null, DateTime? endTime = null)
    {
        try
        {
            var query = fSql.Select<FinancialRecord>();

            if (startTime.HasValue)
                query = query.Where(r => r.CreatedTime >= startTime.Value);

            if (endTime.HasValue)
                query = query.Where(r => r.CreatedTime <= endTime.Value);

            var records = await query.ToListAsync().ConfigureAwait(false);

            var depositAmount = records.Where(r => r.Type == EnumFinancialType.Deposit).Sum(r => r.Amount);
            var withdrawAmount = Math.Abs(records.Where(r => r.Type == EnumFinancialType.Withdraw).Sum(r => r.Amount));
            var userCount = records.Select(r => r.Account).Distinct().Count();

            return new FinancialStatistics
            {
                TotalDepositAmount = depositAmount,
                TotalWithdrawAmount = withdrawAmount,
                NetInflowAmount = depositAmount - withdrawAmount,
                TransactionCount = records.Count,
                UserCount = userCount
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取财务统计信息失败");
            return new FinancialStatistics();
        }
    }

    #endregion

    #region 事务操作

    /// <summary>
    /// 执行上分操作 - 用户充值处理
    ///
    /// 功能：
    /// - 增加用户余额
    /// - 记录上分财务流水
    /// - 关联上分申请记录
    ///
    /// 业务流程：
    /// - 调用IncreaseBalanceAsync增加余额
    /// - 自动生成财务记录
    /// - 记录操作人和备注信息
    /// </summary>
    public async Task<bool> ProcessDepositAsync(string account, decimal amount, long depositRequestId, string @operator, string note = "")
    {
        return await IncreaseBalanceAsync(account, amount, @"上分申请", depositRequestId,
            $@"上分操作，金�? {amount}", @operator, note).ConfigureAwait(false);
    }

    /// <summary>
    /// 执行下分操作 - 用户提现处理
    ///
    /// 功能：
    /// - 减少用户余额
    /// - 记录下分财务流水
    /// - 关联下分申请记录
    ///
    /// 业务流程：
    /// - 调用DecreaseBalanceAsync减少余额
    /// - 自动生成财务记录
    /// - 记录操作人和备注信息
    /// </summary>
    public async Task<bool> ProcessWithdrawAsync(string account, decimal amount, long withdrawRequestId, string @operator, string note = "")
    {
        return await DecreaseBalanceAsync(account, amount, @"下分申请", withdrawRequestId,
            $@"下分操作，金�? {amount}", @operator, note).ConfigureAwait(false);
    }

    #endregion
}