using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using CommandGuard.Enums;

namespace CommandGuard.Models.External;

/// <summary>
/// MyQQ平台消息模型
/// 对应MyQQ平台传输过来的消息格式
/// 注意：当前使用占位符字段，需要根据实际MyQQ API文档进行调整
/// </summary>
public class MyQqMessage
{
    /// <summary>
    /// 消息ID
    /// MyQQ平台的消息唯一标识
    /// </summary>
    [JsonPropertyName("messageId")]
    public long MessageId { get; set; }

    /// <summary>
    /// 用户QQ号
    /// MyQQ平台的用户QQ号码
    /// </summary>
    [JsonPropertyName("qq")]
    [Required(ErrorMessage = @"用户QQ号不能为空")]
    [StringLength(20, ErrorMessage = @"用户QQ号长度不能超过20个字符")]
    public string Qq { get; set; } = string.Empty;

    /// <summary>
    /// 用户昵称
    /// MyQQ平台的用户显示名称
    /// </summary>
    [JsonPropertyName("nickname")]
    [StringLength(100, ErrorMessage = @"用户昵称长度不能超过100个字符")]
    public string Nickname { get; set; } = string.Empty;

    /// <summary>
    /// 消息内容
    /// 用户发送的消息文本内容
    /// </summary>
    [JsonPropertyName("message")]
    [Required(ErrorMessage = @"消息内容不能为空")]
    [StringLength(1000, ErrorMessage = @"消息内容长度不能超过1000个字符")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 消息类型
    /// MyQQ平台的消息类型标识
    /// 1: 文本消息, 2: 图片消息, 3: 语音消息等（占位符值）
    /// </summary>
    [JsonPropertyName("msgType")]
    public int MsgType { get; set; }

    /// <summary>
    /// 群号
    /// 如果是群消息，则包含群号；私聊消息则为空
    /// </summary>
    [JsonPropertyName("groupId")]
    [StringLength(20, ErrorMessage = @"群号长度不能超过20个字符")]
    public string GroupId { get; set; } = string.Empty;

    /// <summary>
    /// 消息时间戳
    /// MyQQ平台的消息发送时间戳（Unix时间戳）
    /// </summary>
    [JsonPropertyName("timestamp")]
    public long Timestamp { get; set; }

    /// <summary>
    /// 机器人QQ号
    /// 接收消息的机器人QQ号
    /// </summary>
    [JsonPropertyName("robotQq")]
    [StringLength(20, ErrorMessage = @"机器人QQ号长度不能超过20个字符")]
    public string RobotQq { get; set; } = string.Empty;

    /// <summary>
    /// 验证消息数据的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public (bool IsValid, string ErrorMessage) Validate()
    {
        if (string.IsNullOrWhiteSpace(Qq))
        {
            return (false, @"用户QQ号不能为空");
        }

        if (string.IsNullOrWhiteSpace(Message))
        {
            return (false, @"消息内容不能为空");
        }

        if (Qq.Length > 20)
        {
            return (false, @"用户QQ号长度不能超过20个字符");
        }

        if (!string.IsNullOrEmpty(Nickname) && Nickname.Length > 100)
        {
            return (false, @"用户昵称长度不能超过100个字符");
        }

        if (Message.Length > 1000)
        {
            return (false, @"消息内容长度不能超过1000个字符");
        }

        if (Timestamp <= 0)
        {
            return (false, @"消息时间戳无效");
        }

        return (true, string.Empty);
    }

    /// <summary>
    /// 转换为内部消息模型
    /// </summary>
    /// <returns>内部消息模型</returns>
    public InternalMessage ToInternalMessage()
    {
        return new InternalMessage
        {
            Id = Guid.NewGuid().ToString(),
            Account = Qq,
            NickName = Nickname,
            Content = Message,
            Status = EnumMessageStatus.Pending,
            CreatedTime = DateTime.Now,
            ExternalMessageId = MessageId.ToString(),
            ExternalPlatform = @"MyQQ",
            ExternalCreatedTime = DateTimeOffset.FromUnixTimeSeconds(Timestamp).DateTime
        };
    }

    /// <summary>
    /// 获取消息类型描述
    /// </summary>
    /// <returns>消息类型描述</returns>
    public string GetTypeDescription()
    {
        return MsgType switch
        {
            1 => @"文本消息",
            2 => @"图片消息", 
            3 => @"语音消息",
            4 => @"视频消息",
            5 => @"文件消息",
            6 => @"表情消息",
            _ => @"未知类型"
        };
    }

    /// <summary>
    /// 判断是否为文本消息
    /// </summary>
    /// <returns>是否为文本消息</returns>
    public bool IsTextMessage()
    {
        // MyQQ平台：MsgType = 1 表示文本消息（占位符值）
        return MsgType == 1;
    }

    /// <summary>
    /// 判断是否为群消息
    /// </summary>
    /// <returns>是否为群消息</returns>
    public bool IsGroupMessage()
    {
        return !string.IsNullOrWhiteSpace(GroupId);
    }

    /// <summary>
    /// 获取用户显示名称
    /// 优先使用昵称，如果昵称为空则使用QQ号
    /// </summary>
    /// <returns>用户显示名称</returns>
    public string GetDisplayName()
    {
        return string.IsNullOrWhiteSpace(Nickname) ? Qq : Nickname;
    }

    /// <summary>
    /// 获取消息来源描述
    /// </summary>
    /// <returns>消息来源描述</returns>
    public string GetSourceDescription()
    {
        if (IsGroupMessage())
        {
            return $@"群聊({GroupId})";
        }
        return @"私聊";
    }

    /// <summary>
    /// 重写ToString方法，便于调试和日志记录
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        var source = IsGroupMessage() ? $"Group:{GroupId}" : "Private";
        return $@"MyQqMessage[Id={MessageId}, Qq={Qq}, Nickname={Nickname}, Type={MsgType}, Source={source}, Message={Message?.Substring(0, Math.Min(Message?.Length ?? 0, 50))}...]";
    }
}
