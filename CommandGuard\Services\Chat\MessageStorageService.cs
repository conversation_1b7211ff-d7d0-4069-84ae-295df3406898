using CommandGuard.Enums;
using CommandGuard.Interfaces.Chat;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Chat;

/// <summary>
/// 消息存储服务实现类
/// 负责将转换后的内部消息存储到数据库
/// 提供完整的消息数据管理功能
/// </summary>
public class MessageStorageService(ILogger<MessageStorageService> logger, IFreeSql fSql) : IMessageStorageService
{
    /// <summary>
    /// 保存单条内部消息到数据库 - 消息持久化存储
    ///
    /// 功能：
    /// - 将内部消息对象保存到数据库
    /// - 自动生成消息ID
    /// - 设置创建时间和初始状态
    /// - 支持事务操作
    ///
    /// 业务流程：
    /// 1. 验证消息对象有效性
    /// 2. 设置消息的创建时间
    /// 3. 插入到数据库
    /// 4. 返回生成的消息ID
    ///
    /// 用途：
    /// - 聊天消息的持久化存储
    /// - 消息处理状态跟踪
    /// - 消息历史记录保存
    /// </summary>
    /// <param name="message">要保存的内部消息对象</param>
    /// <returns>保存后的消息ID</returns>
    /// <exception cref="ArgumentNullException">当消息为null时抛出</exception>
    /// <exception cref="ArgumentException">当消息数据无效时抛出</exception>
    public async Task<long> SaveMessageAsync(InternalMessage message)
    {
        // 参数验证
        if (message == null)
        {
            logger.LogError(@"要保存的消息对象为null");
            throw new ArgumentNullException(nameof(message), @"消息不能为空");
        }

        try
        {
            // 设置创建时间
            message.CreatedTime = DateTime.Now;
            message.UpdatedTime = null;

            // 保存到数据库
            var result = await fSql.Insert<InternalMessage>()
                .AppendData(message)
                .ExecuteIdentityAsync()
                .ConfigureAwait(false);

            logger.LogInformation($@"成功保存消息，ID: {result}",
                result);

            return result;
        }
        catch (Exception ex) when (!(ex is InvalidOperationException))
        {
            logger.LogError(ex, @"保存消息时发生异常");
            throw new InvalidOperationException(@"保存消息失败", ex);
        }
    }

    /// <summary>
    /// 获取指定状态的消息列表 - 按状态查询消息
    ///
    /// 功能：
    /// - 根据消息状态筛选消息
    /// - 按创建时间升序排列
    /// - 支持异步查询操作
    ///
    /// 消息状态类型：
    /// - Pending: 待处理的消息
    /// - Processing: 正在处理的消息
    /// - Processed: 已处理完成的消息
    /// - Failed: 处理失败的消息
    ///
    /// 用途：
    /// - 消息队列处理
    /// - 消息状态监控
    /// - 失败消息重试
    /// </summary>
    /// <param name="status">要查询的消息状态</param>
    /// <returns>符合条件的消息列表</returns>
    public async Task<List<InternalMessage>> GetMessagesByStatusAsync(EnumMessageStatus status)
    {
        try
        {
            return await fSql.Select<InternalMessage>()
                .Where(m => m.Status == status)
                .OrderByDescending(m => m.CreatedTime)
                .ToListAsync()
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"根据状态获取消息列表时发生异常，状态: {Status}", status);
            return [];
        }
    }

    /// <summary>
    /// 更新消息状态 - 消息处理状态跟踪
    ///
    /// 功能：
    /// - 更新指定消息的处理状态
    /// - 记录状态变更时间
    /// - 支持状态流转跟踪
    ///
    /// 状态流转：
    /// Pending → Processing → Processed/Failed
    ///
    /// 使用场景：
    /// - 消息开始处理时设为Processing
    /// - 消息处理完成时设为Processed
    /// - 消息处理失败时设为Failed
    ///
    /// 返回值：true表示更新成功，false表示更新失败
    /// </summary>
    /// <param name="messageId">要更新的消息ID</param>
    /// <param name="status">新的消息状态</param>
    /// <returns>是否更新成功</returns>
    public async Task<bool> UpdateMessageStatusAsync(string messageId, EnumMessageStatus status)
    {
        if (string.IsNullOrWhiteSpace(messageId))
        {
            logger.LogWarning(@"无效的消息ID: {MessageId}", messageId);
            return false;
        }

        try
        {
            var result = await fSql.Update<InternalMessage>()
                .Set(m => m.Status, status)
                .Set(m => m.UpdatedTime, DateTime.Now)
                .Where(m => m.Id == messageId)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            var success = result > 0;
            if (success)
            {
                logger.LogInformation(@"成功更新消息状态，ID: {MessageId}, 新状态: {Status}", messageId, status);
            }
            else
            {
                logger.LogWarning(@"更新消息状态失败，消息可能不存在，ID: {MessageId}", messageId);
            }

            return success;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"更新消息状态时发生异常，ID: {MessageId}", messageId);
            return false;
        }
    }
}