using CommandGuard.Models;

namespace CommandGuard.Interfaces.Business;

/// <summary>
/// 订单服务接口
/// 处理上分和下分申请的业务逻辑
/// </summary>
public interface ICommandService
{
    #region 识别指令

    bool IsDepositRequest(string command);
    bool IsWithdrawRequest(string command);

    #endregion

    #region 处理指令

    Task ProcessMessageCommandAsync(InternalMessage message);
    Task ProcessDepositRequestAsync(InternalMessage message);
    Task ProcessWithdrawRequestAsync(InternalMessage message);
    Task ProcessBetRequestAsync(InternalMessage message);

    #endregion


    #region 统计信息

    /// <summary>
    /// 获取订单统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    Task<OrderStatistics> GetOrderStatisticsAsync();

    #endregion
}