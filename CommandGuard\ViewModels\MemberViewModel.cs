using System.ComponentModel;
using CommandGuard.Models;

namespace CommandGuard.ViewModels;

/// <summary>
/// 会员视图模型
/// 用于在会员管理界面显示会员信息
/// </summary>
public class MemberViewModel : INotifyPropertyChanged
{
    private string _account = string.Empty;
    private string _nickName = string.Empty;

    private decimal _balance;
    private decimal _unsettledAmount;
    private decimal _lastIssueProfitLoss;
    private decimal _rebateRate;
    private string? _agentName;
    private DateTime _registerTime;
    private bool _isFakeUser;
    private decimal _totalDeposit;
    private decimal _totalWithdraw;
    private int _id;
    private bool _isEnabled = true;
    private decimal _agentRebatePercent;

    /// <summary>
    /// 主键ID
    /// </summary>
    public int Id
    {
        get => _id;
        set
        {
            _id = value;
            OnPropertyChanged(nameof(Id));
        }
    }

    /// <summary>
    /// 会员账号
    /// </summary>
    public string Account
    {
        get => _account;
        set
        {
            _account = value ?? string.Empty;
            OnPropertyChanged(nameof(Account));
        }
    }

    /// <summary>
    /// 会员昵称
    /// </summary>
    public string NickName
    {
        get => _nickName;
        set
        {
            _nickName = value ?? string.Empty;
            OnPropertyChanged(nameof(NickName));
        }
    }



    /// <summary>
    /// 账户余额
    /// </summary>
    public decimal Balance
    {
        get => _balance;
        set
        {
            _balance = value;
            OnPropertyChanged(nameof(Balance));
        }
    }

    /// <summary>
    /// 未结算积分
    /// </summary>
    public decimal UnsettledAmount
    {
        get => _unsettledAmount;
        set
        {
            _unsettledAmount = value;
            OnPropertyChanged(nameof(UnsettledAmount));
        }
    }

    /// <summary>
    /// 上期盈亏
    /// </summary>
    public decimal LastIssueProfitLoss
    {
        get => _lastIssueProfitLoss;
        set
        {
            _lastIssueProfitLoss = value;
            OnPropertyChanged(nameof(LastIssueProfitLoss));
        }
    }

    /// <summary>
    /// 回水比例
    /// </summary>
    public decimal RebateRate
    {
        get => _rebateRate;
        set
        {
            _rebateRate = value;
            OnPropertyChanged(nameof(RebateRate));
        }
    }

    /// <summary>
    /// 拉手名称
    /// </summary>
    public string? AgentName
    {
        get => _agentName;
        set
        {
            _agentName = value;
            OnPropertyChanged(nameof(AgentName));
        }
    }

    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime RegisterTime
    {
        get => _registerTime;
        set
        {
            _registerTime = value;
            OnPropertyChanged(nameof(RegisterTime));
        }
    }

    /// <summary>
    /// 是否为假人
    /// </summary>
    public bool IsFakeUser
    {
        get => _isFakeUser;
        set
        {
            _isFakeUser = value;
            OnPropertyChanged(nameof(IsFakeUser));
            OnPropertyChanged(nameof(UserTypeText));
        }
    }

    /// <summary>
    /// 总上分金额
    /// </summary>
    public decimal TotalDeposit
    {
        get => _totalDeposit;
        set
        {
            _totalDeposit = value;
            OnPropertyChanged(nameof(TotalDeposit));
            OnPropertyChanged(nameof(NetDeposit));
        }
    }

    /// <summary>
    /// 总下分金额
    /// </summary>
    public decimal TotalWithdraw
    {
        get => _totalWithdraw;
        set
        {
            _totalWithdraw = value;
            OnPropertyChanged(nameof(TotalWithdraw));
            OnPropertyChanged(nameof(NetDeposit));
        }
    }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled
    {
        get => _isEnabled;
        set
        {
            _isEnabled = value;
            OnPropertyChanged(nameof(IsEnabled));
        }
    }

    /// <summary>
    /// 回水百分比（兼容旧属性名）
    /// </summary>
    public decimal RebatePercent
    {
        get => _rebateRate;
        set
        {
            _rebateRate = value;
            OnPropertyChanged(nameof(RebatePercent));
            OnPropertyChanged(nameof(RebateRate));
        }
    }

    /// <summary>
    /// 给上级拉手返点比例
    /// </summary>
    public decimal AgentRebatePercent
    {
        get => _agentRebatePercent;
        set
        {
            _agentRebatePercent = value;
            OnPropertyChanged(nameof(AgentRebatePercent));
        }
    }

    /// <summary>
    /// 用户类型（兼容旧属性名）
    /// </summary>
    public string UserType
    {
        get => IsFakeUser ? @"假人" : @"真人";
        set
        {
            IsFakeUser = value == @"假人";
            OnPropertyChanged(nameof(UserType));
        }
    }

    /// <summary>
    /// 创建时间（兼容旧属性名）
    /// </summary>
    public DateTime CreatedTime
    {
        get => _registerTime;
        set
        {
            _registerTime = value;
            OnPropertyChanged(nameof(CreatedTime));
            OnPropertyChanged(nameof(RegisterTime));
        }
    }

    /// <summary>
    /// 用户类型文本
    /// </summary>
    public string UserTypeText => IsFakeUser ? @"假人" : @"真人";

    /// <summary>
    /// 净上分金额（总上分 - 总下分）
    /// </summary>
    public decimal NetDeposit => TotalDeposit - TotalWithdraw;

    /// <summary>
    /// 总资产（余额 + 未结算积分）
    /// </summary>
    public decimal TotalAssets => Balance + UnsettledAmount;

    /// <summary>
    /// 是否为活跃用户（有余额或未结算积分）
    /// </summary>
    public bool IsActive => Balance > 0 || UnsettledAmount > 0;

    /// <summary>
    /// 是否为VIP用户（总上分超过10万）
    /// </summary>
    public bool IsVip => TotalDeposit >= 100000;

    /// <summary>
    /// 用户等级描述
    /// </summary>
    public string UserLevel
    {
        get
        {
            if (TotalDeposit >= 1000000) return @"钻石";
            if (TotalDeposit >= 500000) return @"白金";
            if (TotalDeposit >= 100000) return @"黄金";
            if (TotalDeposit >= 50000) return @"白银";
            if (TotalDeposit >= 10000) return @"青铜";
            return @"普通";
        }
    }

    /// <summary>
    /// 从Member实体创建MemberViewModel
    /// </summary>
    /// <param name="member">Member实体</param>
    /// <param name="totalDeposit">总上分金额</param>
    /// <param name="totalWithdraw">总下分金额</param>
    /// <returns>MemberViewModel实例</returns>
    public static MemberViewModel FromMember(Member member, decimal totalDeposit = 0, decimal totalWithdraw = 0)
    {
        return new MemberViewModel
        {
            Id = member.Id,
            Account = member.Account,
            NickName = member.NickName,

            Balance = member.Balance,
            UnsettledAmount = member.UnsettledAmount,
            LastIssueProfitLoss = member.LastIssueProfitLoss,
            RebateRate = member.RebatePercent,
            AgentRebatePercent = member.AgentRebatePercent,
            AgentName = string.IsNullOrEmpty(member.AgentName) ? null : member.AgentName,
            RegisterTime = member.CreateTime,
            IsFakeUser = member.UserType == @"假人",
            TotalDeposit = totalDeposit,
            TotalWithdraw = totalWithdraw,
            IsEnabled = !member.Deleted
        };
    }

    /// <summary>
    /// 获取会员信息摘要
    /// </summary>
    /// <returns>会员信息摘要字符串</returns>
    public string GetSummary()
    {
        return $@"会员[{Account}({NickName})] - 余额:{Balance:F2}, 等级:{UserLevel}, 类型:{UserTypeText}";
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
