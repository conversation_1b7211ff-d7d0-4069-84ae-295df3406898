F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\CommandGuard.exe
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\CommandGuard.deps.json
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\CommandGuard.runtimeconfig.json
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\CommandGuard.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\CommandGuard.pdb
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Flurl.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Flurl.Http.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\FreeSql.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\FreeSql.Provider.Sqlite.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Binder.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.FileExtensions.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Json.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyModel.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Microsoft.Extensions.FileProviders.Abstractions.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Microsoft.Extensions.FileProviders.Physical.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Microsoft.Extensions.FileSystemGlobbing.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Microsoft.Extensions.Options.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Microsoft.Extensions.Primitives.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Newtonsoft.Json.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Serilog.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Serilog.Extensions.Logging.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Serilog.Settings.Configuration.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Serilog.Sinks.Console.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Serilog.Sinks.File.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\System.Data.SQLite.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\System.Diagnostics.DiagnosticSource.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\System.IO.Pipelines.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\System.Text.Encodings.Web.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\System.Text.Json.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\linux-x64\native\SQLite.Interop.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\osx-x64\native\SQLite.Interop.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\win-x64\native\SQLite.Interop.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\win-x86\native\SQLite.Interop.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\AiHelper.dll
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\AiHelper.pdb
F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\AiHelper.dll.config
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.csproj.AssemblyReference.cache
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.Forms.FormMain.resources
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.Forms.FormStartup.resources
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.Properties.Resources.resources
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.csproj.GenerateResource.cache
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.GeneratedMSBuildEditorConfig.editorconfig
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.AssemblyInfoInputs.cache
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.AssemblyInfo.cs
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.csproj.CoreCompileInputs.cache
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandG.D8D6B833.Up2Date
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.dll
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\refint\CommandGuard.dll
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.pdb
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\CommandGuard.genruntimeconfig.cache
F:\SolutionCommandGuard\CommandGuard\obj\Debug\net8.0-windows\ref\CommandGuard.dll
