﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net8.0-windows</TargetFramework>
        <Nullable>enable</Nullable>
        <UseWindowsForms>true</UseWindowsForms>
        <ImplicitUsings>enable</ImplicitUsings>
        <ApplicationIcon>Twitter.ico</ApplicationIcon>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Flurl.Http" Version="4.0.2" />
      <PackageReference Include="FreeSql" Version="3.5.212" />
      <PackageReference Include="FreeSql.Provider.Sqlite" Version="3.5.212" />
      <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.8" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
      <PackageReference Include="Serilog" Version="4.3.0" />
      <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
      <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
      <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
      <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    </ItemGroup>

    <ItemGroup>
      <Reference Include="AiHelper">
        <HintPath>D:\TreeDLL\AiHelper.dll</HintPath>
      </Reference>
    </ItemGroup>

    <ItemGroup>
      <Compile Update="Forms\FormMain.InitUi.cs">
        <DependentUpon>FormMain.cs</DependentUpon>
      </Compile>
      <Compile Update="Properties\Resources.Designer.cs">
        <DesignTime>True</DesignTime>
        <AutoGen>True</AutoGen>
        <DependentUpon>Resources.resx</DependentUpon>
      </Compile>
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Update="Properties\Resources.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      </EmbeddedResource>
    </ItemGroup>

</Project>