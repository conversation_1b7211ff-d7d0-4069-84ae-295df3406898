using CommandGuard.Interfaces.Business;
using CommandGuard.ViewModels;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Business;

/// <summary>
/// 输赢流水回水记录服务实现
/// 提供输赢流水回水记录的查询和统计功能
/// </summary>
public class WinLossRebateRecordService : IWinLossRebateRecordService
{
    private readonly ILogger<WinLossRebateRecordService> _logger;
    private readonly IFreeSql _fSql;
    private readonly IMemberService _memberService;
    private readonly IBetRecordService _betRecordService;

    public WinLossRebateRecordService(ILogger<WinLossRebateRecordService> logger, IFreeSql fSql, IMemberService memberService, IBetRecordService betRecordService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _fSql = fSql ?? throw new ArgumentNullException(nameof(fSql));
        _memberService = memberService ?? throw new ArgumentNullException(nameof(memberService));
        _betRecordService = betRecordService ?? throw new ArgumentNullException(nameof(betRecordService));
    }

    /// <summary>
    /// 根据条件查询输赢流水回水记录
    /// </summary>
    public async Task<List<WinLossRebateRecordViewModel>> QueryWinLossRebateRecordsAsync(
        DateTime startTime,
        DateTime endTime,
        string? account = null,
        string? issue = null,
        bool includeFakeUsers = false)
    {
        try
        {
            _logger.LogInformation(@"开始查询输赢流水回水记录，时间范围: {StartTime} - {EndTime}, 账号: {Account}, 期号: {Issue}, 包含假人: {IncludeFakeUsers}",
                startTime, endTime, account ?? "全部", issue ?? "全部", includeFakeUsers);

            // 从数据库查询真实的输赢流水回水记录数据
            var realData = await QueryRealWinLossRebateRecordsFromDatabaseAsync(startTime, endTime, account, issue, includeFakeUsers);

            _logger.LogInformation(@"查询输赢流水回水记录完成，共找到 {Count} 条记录", realData.Count);
            return realData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"查询输赢流水回水记录时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 计算总真盈亏
    /// </summary>
    public decimal CalculateTotalRealProfitLoss(List<WinLossRebateRecordViewModel> records)
    {
        return records?.Sum(r => r.RealProfitLoss) ?? 0;
    }

    /// <summary>
    /// 计算总假盈亏
    /// </summary>
    public decimal CalculateTotalFakeProfitLoss(List<WinLossRebateRecordViewModel> records)
    {
        return records?.Sum(r => r.FakeProfitLoss) ?? 0;
    }

    /// <summary>
    /// 计算总盈亏
    /// </summary>
    public decimal CalculateTotalProfitLoss(List<WinLossRebateRecordViewModel> records)
    {
        return records?.Sum(r => r.TotalProfitLoss) ?? 0;
    }

    /// <summary>
    /// 计算总真流水
    /// </summary>
    public decimal CalculateTotalRealTurnover(List<WinLossRebateRecordViewModel> records)
    {
        return records?.Sum(r => r.RealTurnover) ?? 0;
    }

    /// <summary>
    /// 计算总假流水
    /// </summary>
    public decimal CalculateTotalFakeTurnover(List<WinLossRebateRecordViewModel> records)
    {
        return records?.Sum(r => r.FakeTurnover) ?? 0;
    }

    /// <summary>
    /// 计算总流水
    /// </summary>
    public decimal CalculateTotalTurnover(List<WinLossRebateRecordViewModel> records)
    {
        return records?.Sum(r => r.TotalTurnover) ?? 0;
    }

    /// <summary>
    /// 计算总未回水
    /// </summary>
    public decimal CalculateTotalPendingRebate(List<WinLossRebateRecordViewModel> records)
    {
        return records?.Sum(r => r.PendingRebate) ?? 0;
    }

    /// <summary>
    /// 计算总已回水
    /// </summary>
    public decimal CalculateTotalPaidRebate(List<WinLossRebateRecordViewModel> records)
    {
        return records?.Sum(r => r.PaidRebate) ?? 0;
    }

    /// <summary>
    /// 计算总回水
    /// </summary>
    public decimal CalculateTotalRebate(List<WinLossRebateRecordViewModel> records)
    {
        return records?.Sum(r => r.TotalRebate) ?? 0;
    }

    /// <summary>
    /// 获取输赢流水回水记录统计信息
    /// </summary>
    public WinLossRebateStatistics GetWinLossRebateStatistics(List<WinLossRebateRecordViewModel> records)
    {
        if (records.Count == 0)
        {
            return new WinLossRebateStatistics();
        }

        var statistics = new WinLossRebateStatistics
        {
            TotalRealProfitLoss = CalculateTotalRealProfitLoss(records),
            TotalFakeProfitLoss = CalculateTotalFakeProfitLoss(records),
            TotalProfitLoss = CalculateTotalProfitLoss(records),
            TotalRealTurnover = CalculateTotalRealTurnover(records),
            TotalFakeTurnover = CalculateTotalFakeTurnover(records),
            TotalTurnover = CalculateTotalTurnover(records),
            TotalPendingRebate = CalculateTotalPendingRebate(records),
            TotalPaidRebate = CalculateTotalPaidRebate(records),
            TotalRebate = CalculateTotalRebate(records),
            RecordCount = records.Count,
            AverageRebatePercent = records.Count > 0 ? records.Average(r => r.RebatePercent) : 0,
            RebateCompletionRate = CalculateTotalRebate(records) > 0 
                ? Math.Round((CalculateTotalPaidRebate(records) / CalculateTotalRebate(records)) * 100, 2) 
                : 0
        };

        _logger.LogDebug(@"输赢流水回水记录统计 - 总盈亏: {TotalProfitLoss:F2}, 总流水: {TotalTurnover:F2}, 总回水: {TotalRebate:F2}",
            statistics.TotalProfitLoss, statistics.TotalTurnover, statistics.TotalRebate);

        return statistics;
    }

    /// <summary>
    /// 从数据库查询真实的输赢流水回水记录数据
    /// </summary>
    private async Task<List<WinLossRebateRecordViewModel>> QueryRealWinLossRebateRecordsFromDatabaseAsync(
        DateTime startTime,
        DateTime endTime,
        string? account,
        string? issue,
        bool includeFakeUsers)
    {
        try
        {
            // 获取所有会员
            var members = await _memberService.GetMembersAsync();
            var memberDict = members.ToDictionary(m => m.Account, m => m);

            // 获取指定时间范围内的投注记录
            var betRecords = await _betRecordService.QueryBetRecordsAsync(startTime, endTime, issue, account, includeFakeUsers);

            // 按会员分组计算输赢流水回水数据
            var memberGroups = betRecords.GroupBy(b => b.Account);
            var result = new List<WinLossRebateRecordViewModel>();

            foreach (var group in memberGroups)
            {
                var memberAccount = group.Key;
                var member = memberDict.GetValueOrDefault(memberAccount);

                if (member == null) continue;

                // 根据includeFakeUsers参数过滤假人
                if (!includeFakeUsers && member.UserType == @"假人")
                    continue;

                // 计算真实盈亏和流水
                var memberBets = group.ToList();
                var realTurnover = memberBets.Sum(b => b.Amount);
                var realProfitLoss = memberBets.Sum(b => b.ProfitLoss);

                // 假人数据设为0（真实系统中假人数据应该从其他表获取）
                var fakeProfitLoss = 0m;
                var fakeTurnover = 0m;

                // 计算回水
                var rebatePercent = member.RebatePercent;
                var totalTurnover = realTurnover + fakeTurnover;
                var totalRebate = totalTurnover * (rebatePercent / 100);

                // 查询已发放的回水（这里简化处理，实际应该从回水记录表查询）
                var paidRebate = 0m; // TODO: 从回水记录表查询已发放的回水
                var pendingRebate = totalRebate - paidRebate;

                result.Add(new WinLossRebateRecordViewModel
                {
                    Account = memberAccount,
                    NickName = member.NickName,
                    RealProfitLoss = Math.Round(realProfitLoss, 2),
                    FakeProfitLoss = Math.Round(fakeProfitLoss, 2),
                    RealTurnover = Math.Round(realTurnover, 2),
                    FakeTurnover = Math.Round(fakeTurnover, 2),
                    RebatePercent = Math.Round(rebatePercent, 1),
                    PaidRebate = Math.Round(paidRebate, 2),
                    PendingRebate = Math.Round(pendingRebate, 2)
                });
            }

            return result.OrderByDescending(x => x.TotalTurnover).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"从数据库查询输赢流水回水记录失败");
            return [];
        }
    }
}
