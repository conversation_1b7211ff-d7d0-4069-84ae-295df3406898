using CommandGuard.Enums;
using CommandGuard.Helpers;
using CommandGuard.ViewModels;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

/// <summary>
/// 主窗体的UI初始化逻辑
/// </summary>
public partial class FormMain
{
    /// <summary>
    /// 初始化所有DataGridView控件
    /// </summary>
    private async Task InitializeDataGridViewsAsync()
    {
        try
        {
            _logger.LogDebug(@"开始初始化DataGridView控件");

            // 暂停布局以提高性能
            SuspendLayout();
            try
            {
                // 批量初始化DataGridView控件
                var initTasks = new List<Action>
                {
                    InitializeMemberDataGridView,
                    InitializeDepositRequestDataGridView,
                    InitializeWithdrawRequestDataGridView,
                    InitializeCurrentIssueBetDataGridView,
                    InitializeBetRecordDataGridView,
                    InitializeDepositWithdrawRecordDataGridView,
                    InitializeWinLossRebateRecordDataGridView,
                    InitializeAgentRebateRecordDataGridView,
                    InitializeSystemSettingsControls
                };

                // 分批执行初始化，避免一次性阻塞太久
                const int batchSize = 3;
                for (int i = 0; i < initTasks.Count; i += batchSize)
                {
                    var batch = initTasks.Skip(i).Take(batchSize);
                    foreach (var task in batch)
                    {
                        task();
                    }

                    // 每批次之间让出控制权，保持UI响应
                    if (i + batchSize < initTasks.Count)
                    {
                        await Task.Delay(1);
                    }
                }

                // 初始化BindingSource并绑定到DataGridView
                InitializeDgvDataBinding();
            }
            finally
            {
                ResumeLayout(false);
            }

            _logger.LogInformation(@"DataGridView初始化完成");
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("Win32"))
        {
            _logger.LogError(ex, @"Win32父窗口设置失败，可能是线程问题");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"DataGridView初始化失败");
            await Task.Delay(0);
            throw;
        }
    }

    #region 设置Dgv样式和绑定事件

    /// <summary>
    /// 初始化会员数据表格
    /// </summary>
    private void InitializeMemberDataGridView()
    {
        // 使用帮助类配置DataGridView，大幅简化代码
        DataGridViewHelper.ConfigureMemberDataGridView(dgvMembers);

        // 启用双缓冲以减少闪烁
        EnableDataGridViewDoubleBuffering(dgvMembers);

        // 绑定事件
        dgvMembers.CellClick += DgvMembers_CellClick;
        dgvMembers.CellDoubleClick += DgvMembers_CellDoubleClick;
        dgvMembers.CellFormatting += DgvMembers_CellFormatting;
    }

    /// <summary>
    /// 初始化上分申请表格
    /// </summary>
    private void InitializeDepositRequestDataGridView()
    {
        // 使用帮助类配置DataGridView，大幅简化代码
        DataGridViewHelper.ConfigureDepositRequestDataGridView(dgvDepositRequests);

        // 绑定事件
        dgvDepositRequests.CellClick += DgvDepositRequests_CellClick;
        dgvDepositRequests.CellPainting += DgvDepositRequests_CellPainting;
        dgvDepositRequests.CellFormatting += DgvDepositRequests_CellFormatting;
    }

    /// <summary>
    /// 初始化下分申请表格
    /// </summary>
    private void InitializeWithdrawRequestDataGridView()
    {
        // 使用帮助类配置DataGridView，大幅简化代码
        DataGridViewHelper.ConfigureWithdrawRequestDataGridView(dgvWithdrawRequests);

        // 绑定事件
        dgvWithdrawRequests.CellClick += DgvWithdrawRequests_CellClick;
        dgvWithdrawRequests.CellPainting += DgvWithdrawRequests_CellPainting;
        dgvWithdrawRequests.CellFormatting += DgvWithdrawRequests_CellFormatting;
    }

    /// <summary>
    /// 初始化当前期投注数据表格
    /// </summary>
    private void InitializeCurrentIssueBetDataGridView()
    {
        // 使用帮助类配置DataGridView，大幅简化代码
        DataGridViewHelper.ConfigureCurrentIssueBetDataGridView(dgvCurrentIssueBets);

        // 启用双缓冲以减少闪烁
        EnableDataGridViewDoubleBuffering(dgvCurrentIssueBets);

        // 添加格式化事件处理
        dgvCurrentIssueBets.CellFormatting += DgvCurrentIssueBets_CellFormatting;
    }

    /// <summary>
    /// 初始化投注记录表格（延迟初始化）
    /// </summary>
    private void InitializeBetRecordDataGridView()
    {
        DataGridViewHelper.ConfigureBetRecordDataGridView(dataGridView_投注记录);
        EnableDataGridViewDoubleBuffering(dataGridView_投注记录);
        dataGridView_投注记录.CellFormatting += DgvBetRecord_CellFormatting;
        _logger.LogDebug(@"投注记录表格标记为延迟初始化");
    }

    /// <summary>
    /// 初始化上下分记录表格（延迟初始化）
    /// </summary>
    private void InitializeDepositWithdrawRecordDataGridView()
    {
        DataGridViewHelper.ConfigureDepositWithdrawRecordDataGridView(dataGridView_上下分记录);
        EnableDataGridViewDoubleBuffering(dataGridView_上下分记录);
        dataGridView_上下分记录.CellFormatting += DgvDepositWithdrawRecord_CellFormatting;
        _logger.LogDebug(@"上下分记录表格标记为延迟初始化");
    }

    /// <summary>
    /// 初始化输赢流水回水记录表格（延迟初始化）
    /// </summary>
    private void InitializeWinLossRebateRecordDataGridView()
    {
        DataGridViewHelper.ConfigureWinLossRebateRecordDataGridView(dataGridView_输赢流水回水记录);
        EnableDataGridViewDoubleBuffering(dataGridView_输赢流水回水记录);
        dataGridView_输赢流水回水记录.CellFormatting += DgvWinLossRebateRecord_CellFormatting;
        _logger.LogDebug(@"输赢流水回水记录表格标记为延迟初始化");
    }

    /// <summary>
    /// 初始化拉手返点记录表格（延迟初始化）
    /// </summary>
    private void InitializeAgentRebateRecordDataGridView()
    {
        DataGridViewHelper.ConfigureAgentRebateRecordDataGridView(dataGridView_拉手返点);
        EnableDataGridViewDoubleBuffering(dataGridView_拉手返点);
        dataGridView_拉手返点.CellFormatting += DgvAgentRebateRecord_CellFormatting;
        _logger.LogDebug(@"拉手返点记录表格标记为延迟初始化");
    }

    /// <summary>
    /// 初始化系统设置控件（已移除DataGridView，使用专用控件）
    /// </summary>
    private void InitializeSystemSettingsControls()
    {
        try
        {
            // 系统设置现在使用专用的CheckBox和NumericUpDown控件
            // 不再需要DataGridView的初始化
            _logger.LogDebug(@"系统设置控件初始化完成");

            _logger.LogDebug(@"系统设置DataGridView初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"初始化系统设置DataGridView失败");
        }
    }

    #endregion

    private async Task InitializeQueryInterfaceAsync()
    {
        try
        {
            InitializeBetRecordQueryInterface();
            InitializeDepositWithdrawQueryInterface();
            InitializeWinLossRebateQueryInterface();
            InitializeAgentRebateQueryInterface();
            InitializeWorkGroupComboBox();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"初始化查询界面失败");
            await Task.Delay(0);
            throw;
        }
    }

    #region 设置界面元素

    /// <summary>
    /// 初始化投注记录查询界面
    /// </summary>
    private void InitializeBetRecordQueryInterface()
    {
        try
        {
            // 设置默认查询时间为今天
            var today = DateTime.Today;

            // 开始时间：今天00:00
            dateTimePicker_查询投注记录开始日期.Value = today;
            numericUpDown_查询投注记录开始小时.Value = 0;
            numericUpDown_查询投注记录开始分钟.Value = 0;

            // 结束时间：今天23:59
            dateTimePicker_查询投注记录结束日期.Value = today;
            numericUpDown_查询投注记录结束小时.Value = 23;
            numericUpDown_查询投注记录结束分钟.Value = 59;

            // 设置数值控件的范围
            numericUpDown_查询投注记录开始小时.Minimum = 0;
            numericUpDown_查询投注记录开始小时.Maximum = 23;
            numericUpDown_查询投注记录开始分钟.Minimum = 0;
            numericUpDown_查询投注记录开始分钟.Maximum = 59;

            numericUpDown_查询投注记录结束小时.Minimum = 0;
            numericUpDown_查询投注记录结束小时.Maximum = 23;
            numericUpDown_查询投注记录结束分钟.Minimum = 0;
            numericUpDown_查询投注记录结束分钟.Maximum = 59;

            // 清空文本框
            textBox_根据期号查询投注记录.Text = string.Empty;
            textBox_根据账号查询投注记录.Text = string.Empty;

            // 默认包含假人
            checkBox_查询投注记录包含假人.Checked = true;

            // 移除提示文本设置
            // textBox_根据期号查询投注记录.PlaceholderText = @"输入期号，如：20241201001";
            // textBox_根据账号查询投注记录.PlaceholderText = @"输入账号，如：user001";

            // 绑定键盘事件，支持Enter键快捷查询
            textBox_根据期号查询投注记录.KeyDown += BetRecordQueryTextBox_KeyDown;
            textBox_根据账号查询投注记录.KeyDown += BetRecordQueryTextBox_KeyDown;

            _logger.LogDebug(@"投注记录查询界面初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"初始化投注记录查询界面失败");
        }
    }

    /// <summary>
    /// 初始化上下分记录查询界面
    /// </summary>
    private void InitializeDepositWithdrawQueryInterface()
    {
        try
        {
            // 设置默认查询时间为今天
            var today = DateTime.Today;

            // 开始时间：今天00:00
            dateTimePicker_查询上下分记录开始日期.Value = today;
            numericUpDown_查询上下分记录开始小时.Value = 0;
            numericUpDown_查询上下分记录开始分钟.Value = 0;

            // 结束时间：今天23:59
            dateTimePicker_查询上下分记录结束日期.Value = today;
            numericUpDown_查询上下分记录结束小时.Value = 23;
            numericUpDown_查询上下分记录结束分钟.Value = 59;

            // 设置数值控件的范围
            numericUpDown_查询上下分记录开始小时.Minimum = 0;
            numericUpDown_查询上下分记录开始小时.Maximum = 23;
            numericUpDown_查询上下分记录开始分钟.Minimum = 0;
            numericUpDown_查询上下分记录开始分钟.Maximum = 59;

            numericUpDown_查询上下分记录结束小时.Minimum = 0;
            numericUpDown_查询上下分记录结束小时.Maximum = 23;
            numericUpDown_查询上下分记录结束分钟.Minimum = 0;
            numericUpDown_查询上下分记录结束分钟.Maximum = 59;

            // 清空文本框
            textBox_查询上下分记录账号.Text = string.Empty;

            // 初始化ComboBox
            InitializeDepositWithdrawTypeComboBox();

            // 默认包含假人
            checkBox_查询上下分记录包含假人.Checked = true;

            // 绑定键盘事件，支持Enter键快捷查询
            textBox_查询上下分记录账号.KeyDown += DepositWithdrawQueryTextBox_KeyDown;
            comboBox__查询上下分记录类型.KeyDown += DepositWithdrawQueryTextBox_KeyDown;

            _logger.LogDebug(@"上下分记录查询界面初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"初始化上下分记录查询界面失败");
        }
    }

    /// <summary>
    /// 初始化上下分记录类型ComboBox
    /// </summary>
    private void InitializeDepositWithdrawTypeComboBox()
    {
        try
        {
            // 清空现有项目
            comboBox__查询上下分记录类型.Items.Clear();

            // 添加选项
            comboBox__查询上下分记录类型.Items.Add(@"全部");
            comboBox__查询上下分记录类型.Items.Add(@"上分");
            comboBox__查询上下分记录类型.Items.Add(@"下分");
            comboBox__查询上下分记录类型.Items.Add(@"回水");

            // 设置默认选择为"全部"
            comboBox__查询上下分记录类型.SelectedIndex = 0;

            // 设置为只读下拉列表
            comboBox__查询上下分记录类型.DropDownStyle = ComboBoxStyle.DropDownList;

            _logger.LogDebug(@"上下分记录类型ComboBox初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"初始化上下分记录类型ComboBox失败");
        }
    }

    /// <summary>
    /// 初始化输赢流水回水记录查询界面
    /// </summary>
    private void InitializeWinLossRebateQueryInterface()
    {
        try
        {
            // 设置默认查询时间为今天
            var today = DateTime.Today;

            // 开始时间：今天00:00
            dateTimePicker_查询输赢流水回水记录开始日期.Value = today;
            numericUpDown_查询输赢流水回水记录开始小时.Value = 0;
            numericUpDown_查询输赢流水回水记录开始分钟.Value = 0;

            // 结束时间：今天23:59
            dateTimePicker_查询输赢流水回水记录结束日期.Value = today;
            numericUpDown_查询输赢流水回水记录结束小时.Value = 23;
            numericUpDown_查询输赢流水回水记录结束分钟.Value = 59;

            // 设置数值控件的范围
            numericUpDown_查询输赢流水回水记录开始小时.Minimum = 0;
            numericUpDown_查询输赢流水回水记录开始小时.Maximum = 23;
            numericUpDown_查询输赢流水回水记录开始分钟.Minimum = 0;
            numericUpDown_查询输赢流水回水记录开始分钟.Maximum = 59;

            numericUpDown_查询输赢流水回水记录结束小时.Minimum = 0;
            numericUpDown_查询输赢流水回水记录结束小时.Maximum = 23;
            numericUpDown_查询输赢流水回水记录结束分钟.Minimum = 0;
            numericUpDown_查询输赢流水回水记录结束分钟.Maximum = 59;

            // 清空文本框
            textBox_查询输赢流水回水记录账号.Text = string.Empty;
            textBox_查询输赢流水回水记录期号.Text = string.Empty;

            // 默认包含假人
            checkBox_查询输赢流水回水记录包含假人.Checked = true;

            // 移除提示文本设置
            // textBox_查询输赢流水回水记录账号.PlaceholderText = @"输入账号，如：user001";
            // textBox_查询输赢流水回水记录期号.PlaceholderText = @"输入期号，如：20241201001";

            // 绑定键盘事件，支持Enter键快捷查询
            textBox_查询输赢流水回水记录账号.KeyDown += WinLossRebateQueryTextBox_KeyDown;
            textBox_查询输赢流水回水记录期号.KeyDown += WinLossRebateQueryTextBox_KeyDown;

            _logger.LogDebug(@"输赢流水回水记录查询界面初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"初始化输赢流水回水记录查询界面失败");
        }
    }

    /// <summary>
    /// 初始化拉手返点查询界面
    /// </summary>
    private void InitializeAgentRebateQueryInterface()
    {
        try
        {
            // 设置默认查询时间为今天
            var today = DateTime.Today;

            // 开始时间：今天00:00
            dateTimePicker_查询拉手返点开始日期.Value = today;
            numericUpDown_查询拉手返点开始小时.Value = 0;
            numericUpDown_查询拉手返点开始分钟.Value = 0;

            // 结束时间：今天23:59
            dateTimePicker_查询拉手返点结束日期.Value = today;
            numericUpDown_查询拉手返点结束小时.Value = 23;
            numericUpDown_查询拉手返点结束分钟.Value = 59;

            // 设置数值控件的范围
            numericUpDown_查询拉手返点开始小时.Minimum = 0;
            numericUpDown_查询拉手返点开始小时.Maximum = 23;
            numericUpDown_查询拉手返点开始分钟.Minimum = 0;
            numericUpDown_查询拉手返点开始分钟.Maximum = 59;

            numericUpDown_查询拉手返点结束小时.Minimum = 0;
            numericUpDown_查询拉手返点结束小时.Maximum = 23;
            numericUpDown_查询拉手返点结束分钟.Minimum = 0;
            numericUpDown_查询拉手返点结束分钟.Maximum = 59;

            // 清空文本框
            textBox_拉手名称.Text = string.Empty;

            // 绑定键盘事件，支持Enter键快捷查询
            textBox_拉手名称.KeyDown += AgentRebateQueryTextBox_KeyDown;

            // 绑定查询按钮点击事件
            button_根据条件查询拉手返点.Click += button_根据条件查询拉手返点_Click;

            _logger.LogDebug(@"拉手返点查询界面初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"初始化拉手返点查询界面失败");
        }
    }

    /// <summary>
    /// 初始化工作群组ComboBox
    /// </summary>
    private void InitializeWorkGroupComboBox()
    {
        try
        {
            // 清空现有项目
            comboBox_WorkGroupId.Items.Clear();

            // 设置为只读下拉列表
            comboBox_WorkGroupId.DropDownStyle = ComboBoxStyle.DropDownList;

            _logger.LogDebug(@"工作群组ComboBox初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"初始化工作群组ComboBox失败");
        }
    }

    #endregion

    /// <summary>
    /// 初始化数据绑定
    /// </summary>
    private void InitializeDgvDataBinding()
    {
        try
        {
            // 初始化BindingSource并绑定到DataGridView,使用持久的BindingSource对象，避免重复创建导致状态丢失
            dgvMembers.DataSource = _memberBindingSource;
            dgvDepositRequests.DataSource = _depositBindingSource;
            dgvWithdrawRequests.DataSource = _withdrawBindingSource;
            dgvCurrentIssueBets.DataSource = _currentIssueBetBindingSource;
            dataGridView_投注记录.DataSource = _betRecordBindingSource;
            dataGridView_上下分记录.DataSource = _depositWithdrawRecordBindingSource;
            dataGridView_输赢流水回水记录.DataSource = _winLossRebateRecordBindingSource;
            dataGridView_拉手返点.DataSource = _agentRebateRecordBindingSource;
            _logger.LogInformation(@"数据绑定初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"数据绑定初始化失败");
            throw;
        }
    }

    #region 静态字体对象

    /// <summary>
    /// 预创建的加粗字体，避免在CellFormatting中重复创建
    /// </summary>
    private static readonly Font BoldFont = new("Microsoft Sans Serif", 9, FontStyle.Bold);

    /// <summary>
    /// 预创建的普通字体，避免在CellFormatting中重复创建
    /// </summary>
    private static readonly Font RegularFont = new("Microsoft Sans Serif", 9, FontStyle.Regular);

    #endregion

    #region Dgv格式化事件

    /// <summary>
    /// 会员数据表格格式化事件
    /// </summary>
    private void DgvMembers_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        if (sender is not DataGridView dataGridView || e.RowIndex < 0 || e.ColumnIndex < 0)
            return;

        try
        {
            var columnName = dataGridView.Columns[e.ColumnIndex].Name;

            // 首先检查当前行的会员类型，决定整行颜色
            var currentRow = dataGridView.Rows[e.RowIndex];
            var memberViewModel = currentRow.DataBoundItem as MemberViewModel;
            var isRealUser = memberViewModel?.UserType == @"真人";

            // 格式化上期盈亏列
            if (columnName == @"LastIssueProfitLoss" && e.Value is decimal profitLoss)
            {
                // 只有正数且为小数时才添加+号，整数和负数保持原样
                var formattedValue = profitLoss > 0 && profitLoss % 1 != 0
                    ? $@"+{profitLoss:F2}"
                    : profitLoss.ToString("F2");
                e.Value = formattedValue;
                e.FormattingApplied = true;

                // 设置字体和颜色
                if (e.CellStyle != null)
                {
                    e.CellStyle.Font = BoldFont;

                    if (!isRealUser)
                    {
                        // 假人整行为红色
                        e.CellStyle.ForeColor = Color.Red;
                    }
                    else
                    {
                        // 真人根据盈亏设置颜色：正数红色，负数绿色
                        e.CellStyle.ForeColor = profitLoss switch
                        {
                            > 0 => Color.Red, // 盈利显示红色
                            < 0 => Color.Green, // 亏损显示绿色
                            _ => Color.Black // 无盈亏显示黑色
                        };
                    }
                }
            }
            // 格式化积分列
            else if (columnName == @"Balance" && e.Value is decimal balance)
            {
                e.Value = balance.ToString("F2");
                e.FormattingApplied = true;

                // 积分列固定为黑色加粗字体
                if (e.CellStyle != null)
                {
                    e.CellStyle.Font = BoldFont;

                    if (!isRealUser)
                    {
                        // 假人整行为红色，但仍然加粗
                        e.CellStyle.ForeColor = Color.Red;
                    }
                    else
                    {
                        // 真人积分列固定为黑色
                        e.CellStyle.ForeColor = Color.Black;
                    }
                }
            }
            // 格式化回水比例列
            else if (columnName == @"RebatePercent" && e.Value is decimal rebatePercent)
            {
                e.Value = $@"{rebatePercent:F1}";
                e.FormattingApplied = true;

                // 假人整行为红色
                if (!isRealUser)
                {
                    var cellStyle = new DataGridViewCellStyle(dataGridView.Columns[e.ColumnIndex].DefaultCellStyle)
                    {
                        ForeColor = Color.Red
                    };
                    dataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex].Style = cellStyle;
                }
            }
            // 格式化真假人列（特殊背景色样式）
            else if (columnName == @"UserType" && e.Value is string userType)
            {
                var cellStyle = new DataGridViewCellStyle(dataGridView.Columns[e.ColumnIndex].DefaultCellStyle);

                switch (userType)
                {
                    case @"真人":
                        cellStyle.BackColor = Color.Blue;      // 蓝色背景
                        cellStyle.ForeColor = Color.White;     // 白色字体
                        cellStyle.SelectionBackColor = Color.DarkBlue;  // 选中时的背景色
                        cellStyle.SelectionForeColor = Color.White;     // 选中时的字体色
                        break;
                    case @"假人":
                        cellStyle.BackColor = Color.Red;       // 红色背景
                        cellStyle.ForeColor = Color.White;     // 白色字体
                        cellStyle.SelectionBackColor = Color.DarkRed;   // 选中时的背景色
                        cellStyle.SelectionForeColor = Color.White;     // 选中时的字体色
                        break;
                    default:
                        cellStyle.BackColor = Color.Gray;      // 未知状态灰色背景
                        cellStyle.ForeColor = Color.White;     // 白色字体
                        cellStyle.SelectionBackColor = Color.DarkGray;  // 选中时的背景色
                        cellStyle.SelectionForeColor = Color.White;     // 选中时的字体色
                        break;
                }

                dataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex].Style = cellStyle;
            }
            // 其他列的格式化（假人整行为红色）
            else if (!isRealUser)
            {
                // 假人会员的其他列都设置为红色
                var cellStyle = new DataGridViewCellStyle(dataGridView.Columns[e.ColumnIndex].DefaultCellStyle)
                {
                    ForeColor = Color.Red
                };
                dataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex].Style = cellStyle;
            }

            // 设置行的背景色（交替行颜色），但真假人列保持原有特殊样式
            if (e.CellStyle != null && columnName != @"UserType")
            {
                if (e.RowIndex % 2 == 0)
                {
                    e.CellStyle.BackColor = Color.White;
                }
                else
                {
                    e.CellStyle.BackColor = Color.AliceBlue;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"格式化会员数据表格单元格失败，行: {Row}, 列: {Column}", e.RowIndex, e.ColumnIndex);
        }
    }

    /// <summary>
    /// 上下分记录表格格式化事件处理
    /// </summary>
    private void DgvDepositWithdrawRecord_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        try
        {
            if (sender is not DataGridView grid || e.RowIndex < 0 || e.ColumnIndex < 0)
                return;

            var columnName = grid.Columns[e.ColumnIndex].Name;

            // 设置行的背景色（交替行颜色）
            if (e.CellStyle != null)
            {
                if (e.RowIndex % 2 == 0)
                {
                    e.CellStyle.BackColor = Color.White;
                }
                else
                {
                    e.CellStyle.BackColor = Color.AliceBlue;
                }
            }

            // 根据处理状态设置状态列颜色
            if (columnName == "StatusText" && e.Value != null)
            {
                var statusText = e.Value.ToString();
                var cell = grid.Rows[e.RowIndex].Cells[e.ColumnIndex];

                switch (statusText)
                {
                    case @"已通过":
                        cell.Style.ForeColor = Color.Green;
                        break;
                    case @"已拒绝":
                        cell.Style.ForeColor = Color.Red;
                        break;
                    case @"待处理":
                        cell.Style.ForeColor = Color.Orange;
                        break;
                    case @"已取消":
                        cell.Style.ForeColor = Color.Gray;
                        break;
                }
            }

            // 假人标识颜色
            if (columnName == "IsFakeUserText" && e.Value != null)
            {
                var isFakeUserText = e.Value.ToString();
                var cell = grid.Rows[e.RowIndex].Cells[e.ColumnIndex];

                if (isFakeUserText == @"是")
                {
                    cell.Style.ForeColor = Color.Purple;
                    cell.Style.Font = BoldFont;
                }
            }

            // 根据上下分类型设置积分列颜色
            if (columnName == "Amount" && e.Value != null)
            {
                if (e.RowIndex < grid.Rows.Count && grid.Rows[e.RowIndex].DataBoundItem is DepositWithdrawRecordViewModel record)
                {
                    var cell = grid.Rows[e.RowIndex].Cells[e.ColumnIndex];

                    switch (record.Type)
                    {
                        case @"上分":
                            // 上分显示红色
                            cell.Style.ForeColor = Color.Red;
                            cell.Style.Font = BoldFont;
                            break;
                        case @"下分":
                            // 下分显示绿色
                            cell.Style.ForeColor = Color.Green;
                            cell.Style.Font = BoldFont;
                            break;
                        default:
                            // 其他类型显示黑色
                            cell.Style.ForeColor = Color.Black;
                            break;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"上下分记录表格格式化失败");
        }
    }

    /// <summary>
    /// 当前期投注数据表格格式化事件
    /// </summary>
    private void DgvCurrentIssueBets_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        if (sender is not DataGridView dataGridView || e.RowIndex < 0 || e.ColumnIndex < 0)
            return;

        try
        {
            var columnName = dataGridView.Columns[e.ColumnIndex].Name;

            // 格式化飞单状态列
            if (columnName == @"FlightStatus" && e.Value is EnumFlightOrderStatus status)
            {
                e.Value = status.GetDescription();
                e.FormattingApplied = true;

                // 根据状态设置颜色
                var cellStyle = new DataGridViewCellStyle(dataGridView.DefaultCellStyle)
                {
                    ForeColor = status switch
                    {
                        EnumFlightOrderStatus.Pending => Color.Orange,
                        EnumFlightOrderStatus.Processing => Color.Blue,
                        EnumFlightOrderStatus.Success => Color.Green,
                        EnumFlightOrderStatus.Failed => Color.Red,
                        _ => Color.Black
                    }
                };

                dataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex].Style = cellStyle;
            }

            // 设置行的背景色（交替行颜色）
            if (e.CellStyle != null)
            {
                if (e.RowIndex % 2 == 0)
                {
                    e.CellStyle.BackColor = Color.White;
                }
                else
                {
                    e.CellStyle.BackColor = Color.AliceBlue;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"格式化当前期投注数据表格单元格失败，行: {Row}, 列: {Column}", e.RowIndex, e.ColumnIndex);
        }
    }

    /// <summary>
    /// 投注记录表格格式化事件处理
    /// </summary>
    private void DgvBetRecord_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        try
        {
            if (sender is not DataGridView grid || e.RowIndex < 0 || e.ColumnIndex < 0)
                return;

            var columnName = grid.Columns[e.ColumnIndex].Name;

            // 设置行的背景色（交替行颜色）
            if (e.CellStyle != null)
            {
                if (e.RowIndex % 2 == 0)
                {
                    e.CellStyle.BackColor = Color.White;
                }
                else
                {
                    e.CellStyle.BackColor = Color.AliceBlue;
                }
            }

            // 格式化结算金额列（盈亏金额）
            if (columnName == "SettlementAmountText" && e.Value != null)
            {
                var amountText = e.Value.ToString();
                if (!string.IsNullOrEmpty(amountText) && decimal.TryParse(amountText, out var amount))
                {
                    var cell = grid.Rows[e.RowIndex].Cells[e.ColumnIndex];
                    if (amount > 0)
                    {
                        // 盈利显示红色
                        cell.Style.ForeColor = Color.Red;
                        cell.Style.Font = BoldFont;
                    }
                    else if (amount < 0)
                    {
                        // 亏损显示绿色
                        cell.Style.ForeColor = Color.Green;
                        cell.Style.Font = BoldFont;
                    }
                    else
                    {
                        // 无盈亏显示黑色
                        cell.Style.ForeColor = Color.Black;
                    }
                }
            }

            // 格式化中奖金额列
            if (columnName == "WinAmountText" && e.Value != null)
            {
                var winAmountText = e.Value.ToString();
                if (!string.IsNullOrEmpty(winAmountText) && decimal.TryParse(winAmountText, out var winAmount))
                {
                    var cell = grid.Rows[e.RowIndex].Cells[e.ColumnIndex];
                    if (winAmount > 0)
                    {
                        // 中奖金额大于0显示红色
                        cell.Style.ForeColor = Color.Red;
                        cell.Style.Font = BoldFont;
                    }
                    else
                    {
                        // 中奖金额为0显示灰色
                        cell.Style.ForeColor = Color.Gray;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"投注记录表格格式化失败");
        }
    }

    /// <summary>
    /// 输赢流水回水记录表格格式化事件处理
    /// </summary>
    private void DgvWinLossRebateRecord_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        try
        {
            if (sender is not DataGridView grid || e.RowIndex < 0 || e.ColumnIndex < 0)
                return;

            var columnName = grid.Columns[e.ColumnIndex].Name;

            // 根据盈亏设置颜色
            if ((columnName == "RealProfitLoss" || columnName == "FakeProfitLoss" || columnName == "TotalProfitLoss") && e.Value != null)
            {
                if (decimal.TryParse(e.Value.ToString(), out var value))
                {
                    var cell = grid.Rows[e.RowIndex].Cells[e.ColumnIndex];
                    if (value > 0)
                    {
                        cell.Style.ForeColor = Color.Red; // 正数显示红色
                        cell.Style.Font = BoldFont;
                    }
                    else if (value < 0)
                    {
                        cell.Style.ForeColor = Color.Green; // 负数显示绿色
                        cell.Style.Font = BoldFont;
                    }
                    else
                    {
                        cell.Style.ForeColor = Color.Black; // 零值显示黑色
                    }
                }
            }

            // 根据回水状态设置颜色
            if (columnName == "PendingRebate" && e.Value != null)
            {
                if (decimal.TryParse(e.Value.ToString(), out var pendingValue))
                {
                    var cell = grid.Rows[e.RowIndex].Cells[e.ColumnIndex];
                    if (pendingValue > 0)
                    {
                        cell.Style.ForeColor = Color.Orange; // 有未回水显示橙色
                        cell.Style.Font = BoldFont;
                    }
                }
            }

            // 回水比例颜色
            if (columnName == "RebatePercent" && e.Value != null)
            {
                if (decimal.TryParse(e.Value.ToString(), out var percentValue))
                {
                    var cell = grid.Rows[e.RowIndex].Cells[e.ColumnIndex];
                    if (percentValue >= 5)
                    {
                        cell.Style.ForeColor = Color.Blue; // 高回水比例显示蓝色
                    }
                }
            }

            // 设置行的背景色（交替行颜色）
            if (e.CellStyle != null)
            {
                if (e.RowIndex % 2 == 0)
                {
                    e.CellStyle.BackColor = Color.White;
                }
                else
                {
                    e.CellStyle.BackColor = Color.AliceBlue;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"输赢流水回水记录表格格式化失败");
        }
    }

    #endregion

    /// <summary>
    /// 为DataGridView启用双缓冲
    /// </summary>
    /// <param name="dataGridView">要启用双缓冲的DataGridView</param>
    private static void EnableDataGridViewDoubleBuffering(DataGridView dataGridView)
    {
        try
        {
            var dgvType = dataGridView.GetType();
            var pi = dgvType.GetProperty(@"DoubleBuffered",
                System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
            pi?.SetValue(dataGridView, true, null);
        }
        catch
        {
            // 如果反射失败，忽略错误
        }
    }

    /// <summary>
    /// 安全地创建字体，避免NullReferenceException
    /// </summary>
    /// <param name="cell">数据网格单元格</param>
    /// <param name="grid">数据网格视图</param>
    /// <param name="fontStyle">字体样式</param>
    /// <returns>新的字体对象</returns>
    private static Font CreateSafeFont(DataGridViewCell cell, DataGridView grid, FontStyle fontStyle)
    {
        try
        {
            // 尝试使用单元格的字体
            var baseFont = cell.Style.Font;
            if (baseFont != null)
            {
                return new Font(baseFont, fontStyle);
            }

            // 尝试使用网格的默认字体
            baseFont = grid.DefaultCellStyle.Font;
            if (baseFont != null)
            {
                return new Font(baseFont, fontStyle);
            }

            // 使用系统默认字体
            return new Font("Microsoft Sans Serif", 9, fontStyle);
        }
        catch (Exception)
        {
            // 如果所有尝试都失败，返回系统默认字体
            return new Font("Microsoft Sans Serif", 9, fontStyle);
        }
    }

    /// <summary>
    /// 拉手返点记录数据表格格式化事件
    /// </summary>
    private void DgvAgentRebateRecord_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        if (sender is not DataGridView dataGridView || e.RowIndex < 0 || e.ColumnIndex < 0)
            return;

        try
        {
            var columnName = dataGridView.Columns[e.ColumnIndex].Name;
            var currentRow = dataGridView.Rows[e.RowIndex];
            var agentRebateRecord = currentRow.DataBoundItem as AgentRebateRecordViewModel;

            if (agentRebateRecord == null) return;

            // 格式化数值列
            switch (columnName)
            {
                case "ValidTurnover":
                    if (e.Value is decimal turnover)
                    {
                        e.Value = turnover.ToString(@"F2");
                        e.FormattingApplied = true;
                        // 根据流水金额设置颜色
                        if (e.CellStyle != null)
                        {
                            e.CellStyle.ForeColor = turnover > 0 ? Color.Blue : Color.Gray;
                        }
                    }
                    break;

                case "AgentRebatePercent":
                    if (e.Value is decimal percent)
                    {
                        e.Value = percent.ToString(@"F2");
                        e.FormattingApplied = true;
                        if (e.CellStyle != null)
                        {
                            e.CellStyle.ForeColor = Color.Black;
                        }
                    }
                    break;

                case "RebateAmount":
                    if (e.Value is decimal rebateAmount)
                    {
                        e.Value = rebateAmount.ToString(@"F2");
                        e.FormattingApplied = true;
                        // 根据返水金额设置颜色
                        if (e.CellStyle != null)
                        {
                            e.CellStyle.ForeColor = rebateAmount > 0 ? Color.Red : Color.Gray;
                        }
                    }
                    break;
            }

            // 设置行的背景色（交替行颜色）
            if (e.CellStyle != null)
            {
                if (e.RowIndex % 2 == 0)
                {
                    e.CellStyle.BackColor = Color.White;
                }
                else
                {
                    e.CellStyle.BackColor = Color.AliceBlue;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"拉手返点记录表格格式化时发生错误");
        }
    }

    /// <summary>
    /// 上分申请表格格式化事件处理
    /// </summary>
    private void DgvDepositRequests_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        try
        {
            if (sender is not DataGridView grid || e.RowIndex < 0 || e.ColumnIndex < 0)
                return;

            // 设置行的背景色（交替行颜色）
            if (e.CellStyle != null)
            {
                if (e.RowIndex % 2 == 0)
                {
                    e.CellStyle.BackColor = Color.White;
                }
                else
                {
                    e.CellStyle.BackColor = Color.AliceBlue;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"上分申请表格格式化失败");
        }
    }

    /// <summary>
    /// 下分申请表格格式化事件处理
    /// </summary>
    private void DgvWithdrawRequests_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
    {
        try
        {
            if (sender is not DataGridView grid || e.RowIndex < 0 || e.ColumnIndex < 0)
                return;

            // 设置行的背景色（交替行颜色）
            if (e.CellStyle != null)
            {
                if (e.RowIndex % 2 == 0)
                {
                    e.CellStyle.BackColor = Color.White;
                }
                else
                {
                    e.CellStyle.BackColor = Color.AliceBlue;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"下分申请表格格式化失败");
        }
    }
}