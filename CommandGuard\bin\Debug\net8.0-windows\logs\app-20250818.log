[2025-08-18 22:02:35.628 +08:00 INF] 应用程序启动开始
[2025-08-18 22:02:35.937 +08:00 INF] 数据库服务初始化成功
[2025-08-18 22:02:35.945 +08:00 INF] 依赖注入容器配置完成，已注册 39 个服务
[2025-08-18 22:02:36.053 +08:00 INF] 成功加载Logo图片: F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/img/logo.png
[2025-08-18 22:02:36.054 +08:00 INF] 启动配置窗体已创建
[2025-08-18 22:02:36.054 +08:00 INF] 启动配置窗体创建成功，开始运行应用程序
[2025-08-18 22:02:37.493 +08:00 INF] 用户选择聊天平台: 一起聊吧
[2025-08-18 22:02:37.494 +08:00 INF] 运行时配置更新完成，平台: "一起聊吧"
[2025-08-18 22:02:37.612 +08:00 INF] 正在创建主窗体...
[2025-08-18 22:02:37.676 +08:00 INF] 主窗体构造函数执行完成
[2025-08-18 22:02:37.718 +08:00 INF] 开始主窗体初始化流程
[2025-08-18 22:02:37.718 +08:00 INF] 开始更新状态栏
[2025-08-18 22:02:37.718 +08:00 INF] 开始初始化查询界面
[2025-08-18 22:02:37.720 +08:00 INF] 基本UI初始化完成
[2025-08-18 22:02:37.720 +08:00 INF] 主窗体快速初始化完成，后台服务正在启动
[2025-08-18 22:02:37.722 +08:00 INF] 开始初始化核心服务
[2025-08-18 22:02:37.722 +08:00 INF] 清空IssueTime数据
[2025-08-18 22:02:37.749 +08:00 INF] 主窗体已显示
[2025-08-18 22:02:37.752 +08:00 INF] 窗体已显示，开始延迟加载数据
[2025-08-18 22:02:37.802 +08:00 INF] 清空开奖信息
[2025-08-18 22:02:37.808 +08:00 INF] 开始初始化默认系统设置
[2025-08-18 22:02:37.860 +08:00 INF] 开始加载初始数据
[2025-08-18 22:02:37.862 +08:00 INF] 所有系统设置已存在，无需添加新配置项
[2025-08-18 22:02:37.930 +08:00 INF] 数据绑定初始化完成
[2025-08-18 22:02:37.930 +08:00 INF] DataGridView初始化完成
[2025-08-18 22:02:37.933 +08:00 INF] 生成了 1421 条发放时间记录，开始批量插入数据库
[2025-08-18 22:02:37.954 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:38.008 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:38.012 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:38.015 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:38.024 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:38.026 +08:00 WRN] 当前期号为空，无法加载投注数据
[2025-08-18 22:02:38.039 +08:00 INF] 初始数据加载完成
[2025-08-18 22:02:38.092 +08:00 INF] 成功插入 1421 条发放时间记录
[2025-08-18 22:02:38.131 +08:00 INF] 开始监听HTTP请求
[2025-08-18 22:02:38.132 +08:00 INF] HTTP API服务启动成功，监听端口: 5000
[2025-08-18 22:02:38.133 +08:00 INF] IssueTimeService更新循环已启动
[2025-08-18 22:02:38.135 +08:00 INF] RobotService工作循环已启动
[2025-08-18 22:02:38.137 +08:00 INF] 一起聊吧机器人信息获取成功，账号: admin, 昵称: 系统管理员
[2025-08-18 22:02:38.137 +08:00 INF] 机器人群组信息为空，开始获取群组信息
[2025-08-18 22:02:38.138 +08:00 INF] 一起聊吧群组信息获取成功，群号: OneChat, 群名: 一起聊吧
[2025-08-18 22:02:38.138 +08:00 INF] 成功获取机器人群组信息，共 1 个群组
[2025-08-18 22:02:38.275 +08:00 INF] 数据刷新定时器已启动，间隔: 1000ms
[2025-08-18 22:02:38.276 +08:00 INF] 当前期投注数据更新定时器已启动，间隔: 3000ms
[2025-08-18 22:02:38.276 +08:00 INF] 核心服务初始化完成
[2025-08-18 22:02:39.140 +08:00 INF] 找到当前进行中的发放时间段: 114046668 (22:00:00 - 22:04:20)
[2025-08-18 22:02:39.140 +08:00 INF] 缓存已更新: 114046668 (22:00:00 - 22:04:20)
[2025-08-18 22:02:39.280 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:39.283 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:39.285 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:39.288 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:39.289 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:39.289 +08:00 INF] 首次加载当前期投注数据: 114046668
[2025-08-18 22:02:39.302 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:39.311 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:39.313 +08:00 INF] 检测到群组信息变化，从 0 个群组更新为 1 个群组
[2025-08-18 22:02:39.317 +08:00 INF] 工作群组ComboBox已更新，共 1 个群组，已选择第一项
[2025-08-18 22:02:40.320 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:40.327 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:40.329 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:40.331 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:40.332 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:40.333 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:40.342 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:40.378 +08:00 ERR] 获取开奖信息异常
Flurl.Http.FlurlHttpException: Call failed. 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:6000): POST http://127.0.0.1:6000/DrawInfo/
 ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:6000)
 ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at CommandGuard.Services.Lottery.DrawService.GetDrawFromApiAsync(CancellationToken token) in F:\SolutionCommandGuard\CommandGuard\Services\Lottery\DrawService.cs:line 64
[2025-08-18 22:02:41.332 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:41.334 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:41.335 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:41.336 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:41.337 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:41.338 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:41.340 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:42.344 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:42.346 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:42.347 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:42.349 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:42.349 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:42.350 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:42.353 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:42.615 +08:00 ERR] 获取开奖信息异常
Flurl.Http.FlurlHttpException: Call failed. 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:6000): POST http://127.0.0.1:6000/DrawInfo/
 ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:6000)
 ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at CommandGuard.Services.Lottery.DrawService.GetDrawFromApiAsync(CancellationToken token) in F:\SolutionCommandGuard\CommandGuard\Services\Lottery\DrawService.cs:line 64
[2025-08-18 22:02:43.362 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:43.368 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:43.372 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:43.377 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:43.380 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:43.382 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:43.387 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:44.394 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:44.399 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:44.402 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:44.406 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:44.408 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:44.410 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:44.416 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:44.852 +08:00 ERR] 获取开奖信息异常
Flurl.Http.FlurlHttpException: Call failed. 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:6000): POST http://127.0.0.1:6000/DrawInfo/
 ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:6000)
 ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at CommandGuard.Services.Lottery.DrawService.GetDrawFromApiAsync(CancellationToken token) in F:\SolutionCommandGuard\CommandGuard\Services\Lottery\DrawService.cs:line 64
[2025-08-18 22:02:45.425 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:45.427 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:45.428 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:45.429 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:45.430 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:45.431 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:45.434 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:46.446 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:46.455 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:46.462 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:46.472 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:46.475 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:46.477 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:46.484 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:47.081 +08:00 ERR] 获取开奖信息异常
Flurl.Http.FlurlHttpException: Call failed. 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:6000): POST http://127.0.0.1:6000/DrawInfo/
 ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:6000)
 ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at CommandGuard.Services.Lottery.DrawService.GetDrawFromApiAsync(CancellationToken token) in F:\SolutionCommandGuard\CommandGuard\Services\Lottery\DrawService.cs:line 64
[2025-08-18 22:02:47.488 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:47.492 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:47.495 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:47.500 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:47.502 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:47.505 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:47.511 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:48.514 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:48.517 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:48.519 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:48.521 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:48.522 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:48.523 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:48.528 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:49.253 +08:00 ERR] 获取开奖信息异常
Flurl.Http.FlurlHttpException: Call failed. 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:6000): POST http://127.0.0.1:6000/DrawInfo/
 ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:6000)
 ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at CommandGuard.Services.Lottery.DrawService.GetDrawFromApiAsync(CancellationToken token) in F:\SolutionCommandGuard\CommandGuard\Services\Lottery\DrawService.cs:line 64
[2025-08-18 22:02:49.529 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:49.532 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:49.534 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:49.537 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:49.538 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:49.540 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:49.547 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:50.542 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:50.544 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:50.545 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:50.546 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:50.547 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:50.548 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:50.550 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:51.421 +08:00 ERR] 获取开奖信息异常
Flurl.Http.FlurlHttpException: Call failed. 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:6000): POST http://127.0.0.1:6000/DrawInfo/
 ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:6000)
 ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at CommandGuard.Services.Lottery.DrawService.GetDrawFromApiAsync(CancellationToken token) in F:\SolutionCommandGuard\CommandGuard\Services\Lottery\DrawService.cs:line 64
[2025-08-18 22:02:51.557 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:51.559 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:51.561 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:51.564 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:51.565 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:51.567 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:51.571 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:52.570 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:52.572 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:52.573 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:52.575 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:52.575 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:52.576 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:52.579 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:53.589 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:53.589 +08:00 ERR] 获取开奖信息异常
Flurl.Http.FlurlHttpException: Call failed. 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:6000): POST http://127.0.0.1:6000/DrawInfo/
 ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (127.0.0.1:6000)
 ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at CommandGuard.Services.Lottery.DrawService.GetDrawFromApiAsync(CancellationToken token) in F:\SolutionCommandGuard\CommandGuard\Services\Lottery\DrawService.cs:line 64
[2025-08-18 22:02:53.591 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:53.593 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:53.594 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:53.595 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:53.596 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:53.599 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:54.612 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:54.614 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:54.615 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:54.616 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:54.617 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:54.618 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:54.621 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:55.618 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:55.620 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:55.621 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:55.622 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:55.623 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:55.624 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:55.626 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:56.617 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:56.619 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:56.620 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:56.621 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:56.622 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:56.623 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:56.625 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:57.616 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:57.619 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:57.620 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:57.623 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:57.624 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:57.625 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:57.630 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:58.642 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:58.645 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:58.646 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:58.647 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:58.648 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:58.649 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:58.651 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:02:59.648 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:02:59.650 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:02:59.651 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:02:59.652 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:02:59.653 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:02:59.654 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:02:59.656 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:00.662 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:00.668 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:00.672 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:00.677 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:00.679 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:00.682 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:00.691 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:01.712 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:01.722 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:01.728 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:01.737 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:01.741 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:01.744 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:01.752 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:02.762 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:02.764 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:02.765 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:02.767 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:02.767 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:02.768 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:02.771 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:03.783 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:03.785 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:03.786 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:03.787 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:03.788 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:03.789 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:03.791 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:04.798 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:04.801 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:04.803 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:04.807 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:04.808 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:04.810 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:04.815 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:05.826 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:05.830 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:05.833 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:05.838 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:05.840 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:05.842 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:05.849 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:06.873 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:06.883 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:06.889 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:06.898 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:06.901 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:06.904 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:06.912 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:07.920 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:07.922 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:07.923 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:07.925 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:07.925 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:07.926 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:07.929 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:08.928 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:08.931 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:08.932 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:08.934 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:08.934 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:08.935 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:08.937 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:09.941 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:09.943 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:09.944 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:09.945 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:09.946 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:09.947 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:09.949 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:10.960 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:10.968 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:10.974 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:10.984 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:10.986 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:10.988 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:10.994 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:11.438 +08:00 INF] 获取到新的开奖数据，开始处理投注结算和图片生成
[2025-08-18 22:03:11.439 +08:00 INF] === 开始投注结算处理 ===
[2025-08-18 22:03:11.442 +08:00 INF] 共找到 52 个未结算的投注订单
[2025-08-18 22:03:11.442 +08:00 INF] 涉及 1 个期号
[2025-08-18 22:03:11.442 +08:00 INF] 检查期号 114046663 - 有 52 个未结算订单
[2025-08-18 22:03:11.443 +08:00 INF] 期号 114046663 找到开奖数据 - 开奖号码: 02,07,09,12,16,17,29,36,48,49,52,53,54,56,59,60,65,69,77,79,54, 开奖时间: 2025-08-18 21:40:00
[2025-08-18 22:03:11.446 +08:00 INF] 投注结果计算完成 - 订单ID: 183, 结算状态: "Win", 金额: 19.450, 番摊结果: 1
[2025-08-18 22:03:11.478 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 19.450, 余额: 9458.55 -> 9478.000
[2025-08-18 22:03:11.478 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 183, 金额: 19.450, 余额已更新
[2025-08-18 22:03:11.478 +08:00 INF] 结算投注订单成功，订单ID: 183, 账号: FZVH7NYL, 结算结果: "Win", 金额: 19.450
[2025-08-18 22:03:11.478 +08:00 INF] 投注订单结算成功 - 订单ID: 183, 账号: FZVH7NYL, 投注项目: 1正, 结算状态: "Win", 金额: 19.450
[2025-08-18 22:03:11.478 +08:00 INF] 投注结果计算完成 - 订单ID: 184, 结算状态: "Draw", 金额: 10, 番摊结果: 1
[2025-08-18 22:03:11.486 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 10, 余额: 9478 -> 9488
[2025-08-18 22:03:11.486 +08:00 INF] 会员 FZVH7NYL 投注和局退款，订单ID: 184, 金额: 10, 余额已更新
[2025-08-18 22:03:11.486 +08:00 INF] 结算投注订单成功，订单ID: 184, 账号: FZVH7NYL, 结算结果: "Draw", 金额: 10
[2025-08-18 22:03:11.486 +08:00 INF] 投注订单结算成功 - 订单ID: 184, 账号: FZVH7NYL, 投注项目: 2正, 结算状态: "Draw", 金额: 10
[2025-08-18 22:03:11.486 +08:00 INF] 投注结果计算完成 - 订单ID: 185, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.489 +08:00 INF] 结算投注订单成功，订单ID: 185, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.489 +08:00 INF] 投注订单结算成功 - 订单ID: 185, 账号: FZVH7NYL, 投注项目: 3正, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.489 +08:00 INF] 投注结果计算完成 - 订单ID: 186, 结算状态: "Draw", 金额: 10, 番摊结果: 1
[2025-08-18 22:03:11.496 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 10, 余额: 9488 -> 9498
[2025-08-18 22:03:11.496 +08:00 INF] 会员 FZVH7NYL 投注和局退款，订单ID: 186, 金额: 10, 余额已更新
[2025-08-18 22:03:11.496 +08:00 INF] 结算投注订单成功，订单ID: 186, 账号: FZVH7NYL, 结算结果: "Draw", 金额: 10
[2025-08-18 22:03:11.496 +08:00 INF] 投注订单结算成功 - 订单ID: 186, 账号: FZVH7NYL, 投注项目: 4正, 结算状态: "Draw", 金额: 10
[2025-08-18 22:03:11.496 +08:00 INF] 投注结果计算完成 - 订单ID: 187, 结算状态: "Win", 金额: 38.350, 番摊结果: 1
[2025-08-18 22:03:11.503 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 38.350, 余额: 9498 -> 9536.350
[2025-08-18 22:03:11.503 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 187, 金额: 38.350, 余额已更新
[2025-08-18 22:03:11.503 +08:00 INF] 结算投注订单成功，订单ID: 187, 账号: FZVH7NYL, 结算结果: "Win", 金额: 38.350
[2025-08-18 22:03:11.503 +08:00 INF] 投注订单结算成功 - 订单ID: 187, 账号: FZVH7NYL, 投注项目: 1番, 结算状态: "Win", 金额: 38.350
[2025-08-18 22:03:11.503 +08:00 INF] 投注结果计算完成 - 订单ID: 188, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.506 +08:00 INF] 结算投注订单成功，订单ID: 188, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.506 +08:00 INF] 投注订单结算成功 - 订单ID: 188, 账号: FZVH7NYL, 投注项目: 2番, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.506 +08:00 INF] 投注结果计算完成 - 订单ID: 189, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.508 +08:00 INF] 结算投注订单成功，订单ID: 189, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.508 +08:00 INF] 投注订单结算成功 - 订单ID: 189, 账号: FZVH7NYL, 投注项目: 3番, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.508 +08:00 INF] 投注结果计算完成 - 订单ID: 190, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.510 +08:00 INF] 结算投注订单成功，订单ID: 190, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.510 +08:00 INF] 投注订单结算成功 - 订单ID: 190, 账号: FZVH7NYL, 投注项目: 4番, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.511 +08:00 INF] 投注结果计算完成 - 订单ID: 191, 结算状态: "Win", 金额: 19.450, 番摊结果: 1
[2025-08-18 22:03:11.518 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 19.450, 余额: 9536.35 -> 9555.800
[2025-08-18 22:03:11.518 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 191, 金额: 19.450, 余额已更新
[2025-08-18 22:03:11.518 +08:00 INF] 结算投注订单成功，订单ID: 191, 账号: FZVH7NYL, 结算结果: "Win", 金额: 19.450
[2025-08-18 22:03:11.518 +08:00 INF] 投注订单结算成功 - 订单ID: 191, 账号: FZVH7NYL, 投注项目: 12角, 结算状态: "Win", 金额: 19.450
[2025-08-18 22:03:11.518 +08:00 INF] 投注结果计算完成 - 订单ID: 192, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.520 +08:00 INF] 结算投注订单成功，订单ID: 192, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.520 +08:00 INF] 投注订单结算成功 - 订单ID: 192, 账号: FZVH7NYL, 投注项目: 23角, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.520 +08:00 INF] 投注结果计算完成 - 订单ID: 193, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.523 +08:00 INF] 结算投注订单成功，订单ID: 193, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.523 +08:00 INF] 投注订单结算成功 - 订单ID: 193, 账号: FZVH7NYL, 投注项目: 34角, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.523 +08:00 INF] 投注结果计算完成 - 订单ID: 194, 结算状态: "Win", 金额: 19.450, 番摊结果: 1
[2025-08-18 22:03:11.529 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 19.450, 余额: 9555.8 -> 9575.250
[2025-08-18 22:03:11.529 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 194, 金额: 19.450, 余额已更新
[2025-08-18 22:03:11.529 +08:00 INF] 结算投注订单成功，订单ID: 194, 账号: FZVH7NYL, 结算结果: "Win", 金额: 19.450
[2025-08-18 22:03:11.529 +08:00 INF] 投注订单结算成功 - 订单ID: 194, 账号: FZVH7NYL, 投注项目: 14角, 结算状态: "Win", 金额: 19.450
[2025-08-18 22:03:11.530 +08:00 INF] 投注结果计算完成 - 订单ID: 195, 结算状态: "Win", 金额: 28.90, 番摊结果: 1
[2025-08-18 22:03:11.537 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 28.90, 余额: 9575.25 -> 9604.15
[2025-08-18 22:03:11.537 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 195, 金额: 28.90, 余额已更新
[2025-08-18 22:03:11.537 +08:00 INF] 结算投注订单成功，订单ID: 195, 账号: FZVH7NYL, 结算结果: "Win", 金额: 28.90
[2025-08-18 22:03:11.537 +08:00 INF] 投注订单结算成功 - 订单ID: 195, 账号: FZVH7NYL, 投注项目: 1念2, 结算状态: "Win", 金额: 28.90
[2025-08-18 22:03:11.537 +08:00 INF] 投注结果计算完成 - 订单ID: 196, 结算状态: "Win", 金额: 28.90, 番摊结果: 1
[2025-08-18 22:03:11.544 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 28.90, 余额: 9604.15 -> 9633.05
[2025-08-18 22:03:11.544 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 196, 金额: 28.90, 余额已更新
[2025-08-18 22:03:11.544 +08:00 INF] 结算投注订单成功，订单ID: 196, 账号: FZVH7NYL, 结算结果: "Win", 金额: 28.90
[2025-08-18 22:03:11.544 +08:00 INF] 投注订单结算成功 - 订单ID: 196, 账号: FZVH7NYL, 投注项目: 1念3, 结算状态: "Win", 金额: 28.90
[2025-08-18 22:03:11.544 +08:00 INF] 投注结果计算完成 - 订单ID: 197, 结算状态: "Win", 金额: 28.90, 番摊结果: 1
[2025-08-18 22:03:11.552 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 28.90, 余额: 9633.05 -> 9661.95
[2025-08-18 22:03:11.552 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 197, 金额: 28.90, 余额已更新
[2025-08-18 22:03:11.552 +08:00 INF] 结算投注订单成功，订单ID: 197, 账号: FZVH7NYL, 结算结果: "Win", 金额: 28.90
[2025-08-18 22:03:11.552 +08:00 INF] 投注订单结算成功 - 订单ID: 197, 账号: FZVH7NYL, 投注项目: 1念4, 结算状态: "Win", 金额: 28.90
[2025-08-18 22:03:11.552 +08:00 INF] 投注结果计算完成 - 订单ID: 198, 结算状态: "Draw", 金额: 10, 番摊结果: 1
[2025-08-18 22:03:11.559 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 10, 余额: 9661.95 -> 9671.95
[2025-08-18 22:03:11.559 +08:00 INF] 会员 FZVH7NYL 投注和局退款，订单ID: 198, 金额: 10, 余额已更新
[2025-08-18 22:03:11.559 +08:00 INF] 结算投注订单成功，订单ID: 198, 账号: FZVH7NYL, 结算结果: "Draw", 金额: 10
[2025-08-18 22:03:11.559 +08:00 INF] 投注订单结算成功 - 订单ID: 198, 账号: FZVH7NYL, 投注项目: 2念1, 结算状态: "Draw", 金额: 10
[2025-08-18 22:03:11.559 +08:00 INF] 投注结果计算完成 - 订单ID: 199, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.562 +08:00 INF] 结算投注订单成功，订单ID: 199, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.562 +08:00 INF] 投注订单结算成功 - 订单ID: 199, 账号: FZVH7NYL, 投注项目: 2念3, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.562 +08:00 INF] 投注结果计算完成 - 订单ID: 200, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.564 +08:00 INF] 结算投注订单成功，订单ID: 200, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.565 +08:00 INF] 投注订单结算成功 - 订单ID: 200, 账号: FZVH7NYL, 投注项目: 2念4, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.565 +08:00 INF] 投注结果计算完成 - 订单ID: 201, 结算状态: "Draw", 金额: 10, 番摊结果: 1
[2025-08-18 22:03:11.571 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 10, 余额: 9671.95 -> 9681.95
[2025-08-18 22:03:11.571 +08:00 INF] 会员 FZVH7NYL 投注和局退款，订单ID: 201, 金额: 10, 余额已更新
[2025-08-18 22:03:11.571 +08:00 INF] 结算投注订单成功，订单ID: 201, 账号: FZVH7NYL, 结算结果: "Draw", 金额: 10
[2025-08-18 22:03:11.572 +08:00 INF] 投注订单结算成功 - 订单ID: 201, 账号: FZVH7NYL, 投注项目: 3念1, 结算状态: "Draw", 金额: 10
[2025-08-18 22:03:11.572 +08:00 INF] 投注结果计算完成 - 订单ID: 202, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.574 +08:00 INF] 结算投注订单成功，订单ID: 202, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.574 +08:00 INF] 投注订单结算成功 - 订单ID: 202, 账号: FZVH7NYL, 投注项目: 3念2, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.574 +08:00 INF] 投注结果计算完成 - 订单ID: 203, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.577 +08:00 INF] 结算投注订单成功，订单ID: 203, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.577 +08:00 INF] 投注订单结算成功 - 订单ID: 203, 账号: FZVH7NYL, 投注项目: 3念4, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.577 +08:00 INF] 投注结果计算完成 - 订单ID: 204, 结算状态: "Draw", 金额: 10, 番摊结果: 1
[2025-08-18 22:03:11.584 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 10, 余额: 9681.95 -> 9691.95
[2025-08-18 22:03:11.584 +08:00 INF] 会员 FZVH7NYL 投注和局退款，订单ID: 204, 金额: 10, 余额已更新
[2025-08-18 22:03:11.584 +08:00 INF] 结算投注订单成功，订单ID: 204, 账号: FZVH7NYL, 结算结果: "Draw", 金额: 10
[2025-08-18 22:03:11.584 +08:00 INF] 投注订单结算成功 - 订单ID: 204, 账号: FZVH7NYL, 投注项目: 4念1, 结算状态: "Draw", 金额: 10
[2025-08-18 22:03:11.584 +08:00 INF] 投注结果计算完成 - 订单ID: 205, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.587 +08:00 INF] 结算投注订单成功，订单ID: 205, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.587 +08:00 INF] 投注订单结算成功 - 订单ID: 205, 账号: FZVH7NYL, 投注项目: 4念2, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.587 +08:00 INF] 投注结果计算完成 - 订单ID: 206, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.589 +08:00 INF] 结算投注订单成功，订单ID: 206, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.589 +08:00 INF] 投注订单结算成功 - 订单ID: 206, 账号: FZVH7NYL, 投注项目: 4念3, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.589 +08:00 INF] 投注结果计算完成 - 订单ID: 207, 结算状态: "Win", 金额: 19.450, 番摊结果: 1
[2025-08-18 22:03:11.597 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 19.450, 余额: 9691.95 -> 9711.400
[2025-08-18 22:03:11.597 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 207, 金额: 19.450, 余额已更新
[2025-08-18 22:03:11.597 +08:00 INF] 结算投注订单成功，订单ID: 207, 账号: FZVH7NYL, 结算结果: "Win", 金额: 19.450
[2025-08-18 22:03:11.597 +08:00 INF] 投注订单结算成功 - 订单ID: 207, 账号: FZVH7NYL, 投注项目: 单, 结算状态: "Win", 金额: 19.450
[2025-08-18 22:03:11.597 +08:00 INF] 投注结果计算完成 - 订单ID: 208, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.601 +08:00 INF] 结算投注订单成功，订单ID: 208, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.601 +08:00 INF] 投注订单结算成功 - 订单ID: 208, 账号: FZVH7NYL, 投注项目: 双, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.601 +08:00 INF] 投注结果计算完成 - 订单ID: 209, 结算状态: "Win", 金额: 19.450, 番摊结果: 1
[2025-08-18 22:03:11.608 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 19.450, 余额: 9711.4 -> 9730.850
[2025-08-18 22:03:11.608 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 209, 金额: 19.450, 余额已更新
[2025-08-18 22:03:11.608 +08:00 INF] 结算投注订单成功，订单ID: 209, 账号: FZVH7NYL, 结算结果: "Win", 金额: 19.450
[2025-08-18 22:03:11.608 +08:00 INF] 投注订单结算成功 - 订单ID: 209, 账号: FZVH7NYL, 投注项目: 1正, 结算状态: "Win", 金额: 19.450
[2025-08-18 22:03:11.608 +08:00 INF] 投注结果计算完成 - 订单ID: 210, 结算状态: "Draw", 金额: 10, 番摊结果: 1
[2025-08-18 22:03:11.615 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 10, 余额: 9730.85 -> 9740.85
[2025-08-18 22:03:11.615 +08:00 INF] 会员 FZVH7NYL 投注和局退款，订单ID: 210, 金额: 10, 余额已更新
[2025-08-18 22:03:11.615 +08:00 INF] 结算投注订单成功，订单ID: 210, 账号: FZVH7NYL, 结算结果: "Draw", 金额: 10
[2025-08-18 22:03:11.615 +08:00 INF] 投注订单结算成功 - 订单ID: 210, 账号: FZVH7NYL, 投注项目: 2正, 结算状态: "Draw", 金额: 10
[2025-08-18 22:03:11.615 +08:00 INF] 投注结果计算完成 - 订单ID: 211, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.617 +08:00 INF] 结算投注订单成功，订单ID: 211, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.617 +08:00 INF] 投注订单结算成功 - 订单ID: 211, 账号: FZVH7NYL, 投注项目: 3正, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.617 +08:00 INF] 投注结果计算完成 - 订单ID: 212, 结算状态: "Draw", 金额: 10, 番摊结果: 1
[2025-08-18 22:03:11.624 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 10, 余额: 9740.85 -> 9750.85
[2025-08-18 22:03:11.624 +08:00 INF] 会员 FZVH7NYL 投注和局退款，订单ID: 212, 金额: 10, 余额已更新
[2025-08-18 22:03:11.624 +08:00 INF] 结算投注订单成功，订单ID: 212, 账号: FZVH7NYL, 结算结果: "Draw", 金额: 10
[2025-08-18 22:03:11.624 +08:00 INF] 投注订单结算成功 - 订单ID: 212, 账号: FZVH7NYL, 投注项目: 4正, 结算状态: "Draw", 金额: 10
[2025-08-18 22:03:11.625 +08:00 INF] 投注结果计算完成 - 订单ID: 213, 结算状态: "Win", 金额: 38.350, 番摊结果: 1
[2025-08-18 22:03:11.632 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 38.350, 余额: 9750.85 -> 9789.200
[2025-08-18 22:03:11.632 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 213, 金额: 38.350, 余额已更新
[2025-08-18 22:03:11.632 +08:00 INF] 结算投注订单成功，订单ID: 213, 账号: FZVH7NYL, 结算结果: "Win", 金额: 38.350
[2025-08-18 22:03:11.632 +08:00 INF] 投注订单结算成功 - 订单ID: 213, 账号: FZVH7NYL, 投注项目: 1番, 结算状态: "Win", 金额: 38.350
[2025-08-18 22:03:11.632 +08:00 INF] 投注结果计算完成 - 订单ID: 214, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.635 +08:00 INF] 结算投注订单成功，订单ID: 214, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.635 +08:00 INF] 投注订单结算成功 - 订单ID: 214, 账号: FZVH7NYL, 投注项目: 2番, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.635 +08:00 INF] 投注结果计算完成 - 订单ID: 215, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.638 +08:00 INF] 结算投注订单成功，订单ID: 215, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.638 +08:00 INF] 投注订单结算成功 - 订单ID: 215, 账号: FZVH7NYL, 投注项目: 3番, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.638 +08:00 INF] 投注结果计算完成 - 订单ID: 216, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.640 +08:00 INF] 结算投注订单成功，订单ID: 216, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.640 +08:00 INF] 投注订单结算成功 - 订单ID: 216, 账号: FZVH7NYL, 投注项目: 4番, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.640 +08:00 INF] 投注结果计算完成 - 订单ID: 217, 结算状态: "Win", 金额: 19.450, 番摊结果: 1
[2025-08-18 22:03:11.647 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 19.450, 余额: 9789.2 -> 9808.650
[2025-08-18 22:03:11.647 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 217, 金额: 19.450, 余额已更新
[2025-08-18 22:03:11.647 +08:00 INF] 结算投注订单成功，订单ID: 217, 账号: FZVH7NYL, 结算结果: "Win", 金额: 19.450
[2025-08-18 22:03:11.647 +08:00 INF] 投注订单结算成功 - 订单ID: 217, 账号: FZVH7NYL, 投注项目: 12角, 结算状态: "Win", 金额: 19.450
[2025-08-18 22:03:11.647 +08:00 INF] 投注结果计算完成 - 订单ID: 218, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.650 +08:00 INF] 结算投注订单成功，订单ID: 218, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.650 +08:00 INF] 投注订单结算成功 - 订单ID: 218, 账号: FZVH7NYL, 投注项目: 23角, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.650 +08:00 INF] 投注结果计算完成 - 订单ID: 219, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.652 +08:00 INF] 结算投注订单成功，订单ID: 219, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.652 +08:00 INF] 投注订单结算成功 - 订单ID: 219, 账号: FZVH7NYL, 投注项目: 34角, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.652 +08:00 INF] 投注结果计算完成 - 订单ID: 220, 结算状态: "Win", 金额: 19.450, 番摊结果: 1
[2025-08-18 22:03:11.659 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 19.450, 余额: 9808.65 -> 9828.100
[2025-08-18 22:03:11.659 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 220, 金额: 19.450, 余额已更新
[2025-08-18 22:03:11.659 +08:00 INF] 结算投注订单成功，订单ID: 220, 账号: FZVH7NYL, 结算结果: "Win", 金额: 19.450
[2025-08-18 22:03:11.659 +08:00 INF] 投注订单结算成功 - 订单ID: 220, 账号: FZVH7NYL, 投注项目: 14角, 结算状态: "Win", 金额: 19.450
[2025-08-18 22:03:11.659 +08:00 INF] 投注结果计算完成 - 订单ID: 221, 结算状态: "Win", 金额: 28.90, 番摊结果: 1
[2025-08-18 22:03:11.666 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 28.90, 余额: 9828.1 -> 9857.00
[2025-08-18 22:03:11.666 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 221, 金额: 28.90, 余额已更新
[2025-08-18 22:03:11.666 +08:00 INF] 结算投注订单成功，订单ID: 221, 账号: FZVH7NYL, 结算结果: "Win", 金额: 28.90
[2025-08-18 22:03:11.666 +08:00 INF] 投注订单结算成功 - 订单ID: 221, 账号: FZVH7NYL, 投注项目: 1念2, 结算状态: "Win", 金额: 28.90
[2025-08-18 22:03:11.666 +08:00 INF] 投注结果计算完成 - 订单ID: 222, 结算状态: "Win", 金额: 28.90, 番摊结果: 1
[2025-08-18 22:03:11.672 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 28.90, 余额: 9857 -> 9885.90
[2025-08-18 22:03:11.672 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 222, 金额: 28.90, 余额已更新
[2025-08-18 22:03:11.672 +08:00 INF] 结算投注订单成功，订单ID: 222, 账号: FZVH7NYL, 结算结果: "Win", 金额: 28.90
[2025-08-18 22:03:11.673 +08:00 INF] 投注订单结算成功 - 订单ID: 222, 账号: FZVH7NYL, 投注项目: 1念3, 结算状态: "Win", 金额: 28.90
[2025-08-18 22:03:11.673 +08:00 INF] 投注结果计算完成 - 订单ID: 223, 结算状态: "Win", 金额: 28.90, 番摊结果: 1
[2025-08-18 22:03:11.679 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 28.90, 余额: 9885.9 -> 9914.80
[2025-08-18 22:03:11.679 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 223, 金额: 28.90, 余额已更新
[2025-08-18 22:03:11.679 +08:00 INF] 结算投注订单成功，订单ID: 223, 账号: FZVH7NYL, 结算结果: "Win", 金额: 28.90
[2025-08-18 22:03:11.679 +08:00 INF] 投注订单结算成功 - 订单ID: 223, 账号: FZVH7NYL, 投注项目: 1念4, 结算状态: "Win", 金额: 28.90
[2025-08-18 22:03:11.679 +08:00 INF] 投注结果计算完成 - 订单ID: 224, 结算状态: "Draw", 金额: 10, 番摊结果: 1
[2025-08-18 22:03:11.686 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 10, 余额: 9914.8 -> 9924.8
[2025-08-18 22:03:11.686 +08:00 INF] 会员 FZVH7NYL 投注和局退款，订单ID: 224, 金额: 10, 余额已更新
[2025-08-18 22:03:11.686 +08:00 INF] 结算投注订单成功，订单ID: 224, 账号: FZVH7NYL, 结算结果: "Draw", 金额: 10
[2025-08-18 22:03:11.686 +08:00 INF] 投注订单结算成功 - 订单ID: 224, 账号: FZVH7NYL, 投注项目: 2念1, 结算状态: "Draw", 金额: 10
[2025-08-18 22:03:11.686 +08:00 INF] 投注结果计算完成 - 订单ID: 225, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.688 +08:00 INF] 结算投注订单成功，订单ID: 225, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.688 +08:00 INF] 投注订单结算成功 - 订单ID: 225, 账号: FZVH7NYL, 投注项目: 2念3, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.688 +08:00 INF] 投注结果计算完成 - 订单ID: 226, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.690 +08:00 INF] 结算投注订单成功，订单ID: 226, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.690 +08:00 INF] 投注订单结算成功 - 订单ID: 226, 账号: FZVH7NYL, 投注项目: 2念4, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.690 +08:00 INF] 投注结果计算完成 - 订单ID: 227, 结算状态: "Draw", 金额: 10, 番摊结果: 1
[2025-08-18 22:03:11.697 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 10, 余额: 9924.8 -> 9934.8
[2025-08-18 22:03:11.697 +08:00 INF] 会员 FZVH7NYL 投注和局退款，订单ID: 227, 金额: 10, 余额已更新
[2025-08-18 22:03:11.697 +08:00 INF] 结算投注订单成功，订单ID: 227, 账号: FZVH7NYL, 结算结果: "Draw", 金额: 10
[2025-08-18 22:03:11.697 +08:00 INF] 投注订单结算成功 - 订单ID: 227, 账号: FZVH7NYL, 投注项目: 3念1, 结算状态: "Draw", 金额: 10
[2025-08-18 22:03:11.697 +08:00 INF] 投注结果计算完成 - 订单ID: 228, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.699 +08:00 INF] 结算投注订单成功，订单ID: 228, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.699 +08:00 INF] 投注订单结算成功 - 订单ID: 228, 账号: FZVH7NYL, 投注项目: 3念2, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.699 +08:00 INF] 投注结果计算完成 - 订单ID: 229, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.702 +08:00 INF] 结算投注订单成功，订单ID: 229, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.702 +08:00 INF] 投注订单结算成功 - 订单ID: 229, 账号: FZVH7NYL, 投注项目: 3念4, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.702 +08:00 INF] 投注结果计算完成 - 订单ID: 230, 结算状态: "Draw", 金额: 10, 番摊结果: 1
[2025-08-18 22:03:11.708 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 10, 余额: 9934.8 -> 9944.8
[2025-08-18 22:03:11.708 +08:00 INF] 会员 FZVH7NYL 投注和局退款，订单ID: 230, 金额: 10, 余额已更新
[2025-08-18 22:03:11.708 +08:00 INF] 结算投注订单成功，订单ID: 230, 账号: FZVH7NYL, 结算结果: "Draw", 金额: 10
[2025-08-18 22:03:11.708 +08:00 INF] 投注订单结算成功 - 订单ID: 230, 账号: FZVH7NYL, 投注项目: 4念1, 结算状态: "Draw", 金额: 10
[2025-08-18 22:03:11.708 +08:00 INF] 投注结果计算完成 - 订单ID: 231, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.711 +08:00 INF] 结算投注订单成功，订单ID: 231, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.711 +08:00 INF] 投注订单结算成功 - 订单ID: 231, 账号: FZVH7NYL, 投注项目: 4念2, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.711 +08:00 INF] 投注结果计算完成 - 订单ID: 232, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.713 +08:00 INF] 结算投注订单成功，订单ID: 232, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.713 +08:00 INF] 投注订单结算成功 - 订单ID: 232, 账号: FZVH7NYL, 投注项目: 4念3, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.713 +08:00 INF] 投注结果计算完成 - 订单ID: 233, 结算状态: "Win", 金额: 19.450, 番摊结果: 1
[2025-08-18 22:03:11.720 +08:00 INF] 增加用户余额成功，账�? FZVH7NYL, 金额: 19.450, 余额: 9944.8 -> 9964.250
[2025-08-18 22:03:11.720 +08:00 INF] 会员 FZVH7NYL 投注中奖，订单ID: 233, 金额: 19.450, 余额已更新
[2025-08-18 22:03:11.720 +08:00 INF] 结算投注订单成功，订单ID: 233, 账号: FZVH7NYL, 结算结果: "Win", 金额: 19.450
[2025-08-18 22:03:11.720 +08:00 INF] 投注订单结算成功 - 订单ID: 233, 账号: FZVH7NYL, 投注项目: 单, 结算状态: "Win", 金额: 19.450
[2025-08-18 22:03:11.720 +08:00 INF] 投注结果计算完成 - 订单ID: 234, 结算状态: "Lose", 金额: 0, 番摊结果: 1
[2025-08-18 22:03:11.722 +08:00 INF] 结算投注订单成功，订单ID: 234, 账号: FZVH7NYL, 结算结果: "Lose", 金额: 0
[2025-08-18 22:03:11.722 +08:00 INF] 投注订单结算成功 - 订单ID: 234, 账号: FZVH7NYL, 投注项目: 双, 结算状态: "Lose", 金额: 0
[2025-08-18 22:03:11.722 +08:00 INF] 期号 114046663 结算完成 - 成功: 52/52
[2025-08-18 22:03:11.722 +08:00 INF] 投注结算处理完成 - 处理期号: 1, 处理订单: 52, 成功结算: 52
[2025-08-18 22:03:11.722 +08:00 INF] 检查是否需要发送结算结果 - 成功结算订单数: 52
[2025-08-18 22:03:11.722 +08:00 INF] 开始发送结算结果到聊天平台
[2025-08-18 22:03:11.723 +08:00 INF] === 开始发送结算结果到聊天平台 ===
[2025-08-18 22:03:11.723 +08:00 INF] 获取最新开奖信息 - 期号: 114046667, 开奖号码: 06,07,08,18,19,21,23,27,29,30,38,41,44,58,63,68,69,70,75,79,18
[2025-08-18 22:03:11.723 +08:00 INF] 开始查询期号 114046667 的结算数据
[2025-08-18 22:03:11.724 +08:00 INF] 开始获取期号 114046667 的结算数据
[2025-08-18 22:03:11.724 +08:00 INF] 查询到期号 114046667 的投注记录数: 0
[2025-08-18 22:03:11.724 +08:00 INF] 期号 114046667 没有投注记录
[2025-08-18 22:03:11.725 +08:00 INF] 查询到结算数据 - 参与会员数: 0, 总积分: 0
[2025-08-18 22:03:11.725 +08:00 INF] 本期无投注记录，跳过发送结算结果
[2025-08-18 22:03:11.725 +08:00 INF] === 投注结算处理结束 ===
[2025-08-18 22:03:11.725 +08:00 INF] 开始生成开奖数据图片
[2025-08-18 22:03:11.909 +08:00 INF] 开奖数据图片生成完成
[2025-08-18 22:03:11.999 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:12.000 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:12.002 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:12.003 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:12.004 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:12.007 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:12.010 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:12.010 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:12.011 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:12.011 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:12.078 +08:00 INF] 7行路子图生成完成
[2025-08-18 22:03:12.222 +08:00 INF] 6行路子图生成完成
[2025-08-18 22:03:12.264 +08:00 INF] 7行完整路子图生成完成
[2025-08-18 22:03:12.308 +08:00 INF] 6行完整路子图生成完成
[2025-08-18 22:03:12.308 +08:00 INF] 所有开奖图片生成完成
[2025-08-18 22:03:12.309 +08:00 INF] 开始发送开奖数据和摊路图到聊天平台
[2025-08-18 22:03:12.309 +08:00 INF] 游戏服务未启动，跳过发送开奖图片
[2025-08-18 22:03:12.739 +08:00 INF] 获取到新的开奖数据，开始处理投注结算和图片生成
[2025-08-18 22:03:12.739 +08:00 INF] === 开始投注结算处理 ===
[2025-08-18 22:03:12.739 +08:00 INF] 没有找到未结算的投注订单
[2025-08-18 22:03:12.739 +08:00 INF] 开始生成开奖数据图片
[2025-08-18 22:03:12.939 +08:00 INF] 开奖数据图片生成完成
[2025-08-18 22:03:13.014 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:13.015 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:13.018 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:13.020 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:13.021 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:13.021 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:13.024 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:13.024 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:13.025 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:13.025 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:13.097 +08:00 INF] 7行路子图生成完成
[2025-08-18 22:03:13.233 +08:00 INF] 6行路子图生成完成
[2025-08-18 22:03:13.498 +08:00 INF] 7行完整路子图生成完成
[2025-08-18 22:03:13.771 +08:00 INF] 6行完整路子图生成完成
[2025-08-18 22:03:13.771 +08:00 INF] 所有开奖图片生成完成
[2025-08-18 22:03:13.771 +08:00 INF] 开始发送开奖数据和摊路图到聊天平台
[2025-08-18 22:03:13.771 +08:00 INF] 游戏服务未启动，跳过发送开奖图片
[2025-08-18 22:03:14.026 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:14.028 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:14.030 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:14.033 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:14.034 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:14.035 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:14.039 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:14.040 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:14.041 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:14.041 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:15.039 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:15.042 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:15.044 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:15.046 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:15.047 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:15.048 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:15.052 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:15.052 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:15.052 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:15.052 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:16.051 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:16.052 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:16.054 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:16.055 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:16.056 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:16.056 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:16.059 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:16.059 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:16.061 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:16.061 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:17.063 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:17.065 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:17.067 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:17.078 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:17.078 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:17.079 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:17.082 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:17.082 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:17.082 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:17.082 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:18.094 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:18.096 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:18.097 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:18.099 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:18.099 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:18.100 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:18.103 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:18.103 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:18.104 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:18.104 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:19.109 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:19.110 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:19.111 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:19.113 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:19.114 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:19.115 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:19.118 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:19.118 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:19.119 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:19.119 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:20.128 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:20.137 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:20.144 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:20.153 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:20.157 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:20.161 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:20.172 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:20.173 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:20.174 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:20.174 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:21.185 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:21.186 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:21.188 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:21.189 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:21.190 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:21.190 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:21.193 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:21.193 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:21.194 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:21.194 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:22.207 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:22.217 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:22.224 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:22.233 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:22.237 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:22.242 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:22.259 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:22.263 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:22.265 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:22.265 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:23.259 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:23.261 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:23.262 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:23.263 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:23.264 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:23.265 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:23.267 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:23.267 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:23.268 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:23.268 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:24.272 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:24.274 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:24.275 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:24.277 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:24.277 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:24.278 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:24.281 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:24.281 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:24.282 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:24.282 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:25.285 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:25.286 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:25.288 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:25.289 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:25.290 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:25.291 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:25.293 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:25.293 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:25.294 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:25.294 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:26.297 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:26.299 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:26.300 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:26.301 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:26.302 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:26.302 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:26.305 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:26.305 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:26.306 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:26.306 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:27.302 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:27.303 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:27.305 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:27.306 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:27.307 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:27.307 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:27.310 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:27.310 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:27.310 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:27.310 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:28.310 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:28.312 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:28.313 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:28.314 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:28.315 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:28.316 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:28.318 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:28.318 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:28.319 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:28.319 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:29.323 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:29.325 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:29.328 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:29.330 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:29.330 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:29.332 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:29.335 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:29.335 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:29.335 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:29.335 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:30.343 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:30.345 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:30.347 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:30.349 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:30.349 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:30.350 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:30.353 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:30.353 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:30.353 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:30.353 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:31.358 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:31.361 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:31.362 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:31.365 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:31.366 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:31.367 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:31.371 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:31.371 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:31.372 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:31.372 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:32.052 +08:00 INF] 用户点击开始飞单按钮
[2025-08-18 22:03:32.052 +08:00 INF] 开始游戏按钮未被按下，先模拟点击开始游戏按钮
[2025-08-18 22:03:32.066 +08:00 ERR] 获取选择的工作群组ID失败
System.InvalidOperationException: 线程间操作无效: 从不是创建控件“comboBox_WorkGroupId”的线程访问它。
   at System.Windows.Forms.Control.get_Handle()
   at Windows.Win32.PInvoke.SendMessage[T](T hWnd, MessageId Msg, WPARAM wParam, LPARAM lParam)
   at System.Windows.Forms.ComboBox.get_SelectedIndex()
   at System.Windows.Forms.ComboBox.get_SelectedItem()
   at CommandGuard.Forms.FormMain.GetSelectedWorkGroupId() in F:\SolutionCommandGuard\CommandGuard\Forms\FormMain.cs:line 4658
[2025-08-18 22:03:32.370 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:32.372 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:32.376 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:32.385 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:32.387 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:32.389 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:32.393 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:32.393 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:32.394 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:32.394 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:33.401 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:33.402 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:33.403 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:33.406 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:33.406 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:33.407 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:33.410 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:33.410 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:33.411 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:33.411 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:34.414 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:34.416 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:34.417 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:34.429 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:34.430 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:34.431 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:34.434 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:34.434 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:34.435 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:34.435 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:35.438 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:35.441 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:35.445 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:35.448 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:35.449 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:35.451 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:35.456 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:35.457 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:35.458 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:35.458 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:36.473 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:36.475 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:36.477 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:36.479 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:36.480 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:36.482 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:36.494 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:36.495 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:36.498 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:36.498 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:37.514 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:37.515 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:37.517 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:37.518 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:37.518 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:37.519 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:37.522 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:37.522 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:37.522 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:37.522 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:38.532 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:38.534 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:38.536 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:38.538 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:38.539 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:38.540 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:38.544 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:38.545 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:38.546 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:38.546 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:39.550 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:39.558 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:39.564 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:39.577 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:39.579 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:39.581 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:39.587 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:39.587 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:39.588 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:39.589 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:40.584 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:40.585 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:40.586 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:40.588 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:40.588 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:40.589 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:40.592 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:40.592 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:40.592 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:40.592 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:41.594 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:41.596 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:41.597 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:41.598 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:41.599 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:41.600 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:41.602 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:41.602 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:41.603 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:41.603 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:42.612 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:42.614 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:42.615 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:42.616 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:42.617 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:42.618 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:42.620 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:42.620 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:42.621 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:42.621 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:43.625 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:43.626 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:43.627 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:43.629 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:43.629 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:43.632 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:43.634 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:43.634 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:43.635 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:43.635 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:44.636 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:44.638 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:44.639 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:44.641 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:44.641 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:44.642 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:44.651 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:44.652 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:44.655 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:44.655 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:45.659 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:45.667 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:45.671 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:45.676 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:45.678 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:45.681 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:45.688 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:45.689 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:45.691 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:45.691 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:46.693 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:46.695 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:46.696 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:46.698 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:46.698 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:46.699 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:46.702 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:46.702 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:46.702 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:46.702 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:47.707 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:47.708 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:47.709 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:47.713 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:47.713 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:47.714 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:47.717 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:47.717 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:47.718 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:47.718 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:48.714 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:48.720 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:48.724 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:48.729 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:48.731 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:48.734 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:48.744 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:48.745 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:48.747 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:48.747 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:49.739 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:49.741 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:49.742 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:49.743 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:49.744 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:49.744 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:49.747 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:49.747 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:49.748 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:49.748 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:50.761 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:50.773 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:50.779 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:50.788 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:50.794 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:50.799 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:50.807 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:50.808 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:50.810 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:50.810 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:51.809 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:51.811 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:51.812 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:51.814 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:51.814 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:51.815 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:51.818 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:51.818 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:51.819 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:51.819 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:52.830 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:52.841 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:52.848 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:52.859 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:52.862 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:52.866 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:52.873 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:52.875 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:52.877 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:52.877 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:53.881 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:53.883 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:53.884 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:53.885 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:53.886 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:53.887 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:53.889 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:53.889 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:53.890 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:53.890 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:54.885 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:54.887 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:54.890 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:54.894 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:54.895 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:54.896 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:54.901 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:54.902 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:54.902 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:54.902 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:55.905 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:55.907 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:55.908 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:55.909 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:55.910 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:55.911 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:55.913 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:55.913 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:55.914 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:55.914 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:56.927 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:56.935 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:56.941 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:56.947 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:56.949 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:56.951 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:56.958 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:56.959 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:56.960 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:56.960 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:57.970 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:57.973 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:57.976 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:57.980 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:57.981 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:57.983 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:57.989 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:57.989 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:57.990 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:57.990 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:03:59.000 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:03:59.003 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:03:59.005 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:03:59.007 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:03:59.008 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:03:59.009 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:03:59.018 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:03:59.018 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:03:59.019 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:03:59.019 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:00.026 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:00.028 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:00.029 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:00.031 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:00.032 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:00.033 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:00.036 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:00.036 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:00.037 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:00.037 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:01.049 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:01.055 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:01.058 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:01.061 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:01.063 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:01.065 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:01.071 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:01.071 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:01.073 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:01.073 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:02.074 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:02.076 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:02.078 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:02.079 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:02.080 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:02.081 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:02.084 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:02.084 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:02.085 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:02.085 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:03.085 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:03.087 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:03.088 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:03.090 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:03.090 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:03.093 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:03.099 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:03.099 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:03.100 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:03.100 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:04.113 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:04.116 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:04.118 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:04.120 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:04.121 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:04.122 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:04.128 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:04.129 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:04.129 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:04.129 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:05.138 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:05.140 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:05.141 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:05.143 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:05.144 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:05.145 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:05.149 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:05.149 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:05.149 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:05.149 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:06.150 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:06.151 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:06.153 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:06.154 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:06.155 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:06.156 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:06.160 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:06.160 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:06.161 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:06.161 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:07.169 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:07.170 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:07.172 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:07.173 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:07.175 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:07.176 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:07.180 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:07.180 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:07.181 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:07.181 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:08.194 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:08.198 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:08.200 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:08.204 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:08.205 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:08.208 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:08.213 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:08.213 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:08.215 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:08.215 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:09.219 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:09.221 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:09.222 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:09.224 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:09.225 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:09.225 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:09.229 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:09.229 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:09.229 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:09.229 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:10.234 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:10.246 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:10.248 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:10.250 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:10.251 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:10.252 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:10.255 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:10.256 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:10.256 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:10.256 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:11.254 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:11.256 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:11.258 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:11.259 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:11.260 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:11.260 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:11.264 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:11.265 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:11.265 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:11.265 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:12.265 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:12.269 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:12.273 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:12.276 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:12.278 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:12.279 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:12.286 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:12.287 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:12.287 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:12.287 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:13.299 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:13.301 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:13.303 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:13.304 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:13.306 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:13.307 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:13.309 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:13.310 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:13.310 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:13.310 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:14.303 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:14.304 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:14.306 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:14.307 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:14.308 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:14.309 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:14.312 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:14.312 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:14.313 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:14.313 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:14.401 +08:00 ERR] 模拟点击开始游戏按钮失败
System.InvalidOperationException: 游戏服务启动失败
   at CommandGuard.Forms.FormMain.button_StartBet_Click(Object sender, EventArgs e) in F:\SolutionCommandGuard\CommandGuard\Forms\FormMain.cs:line 691
[2025-08-18 22:04:15.315 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:15.317 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:15.318 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:15.319 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:15.320 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:15.321 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:15.324 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:15.324 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:15.325 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:15.325 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:16.330 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:16.332 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:16.334 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:16.335 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:16.336 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:16.337 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:16.340 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:16.340 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:16.341 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:16.341 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:17.334 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:17.336 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:17.337 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:17.338 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:17.339 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:17.340 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:17.343 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:17.343 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:17.344 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:17.344 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:18.339 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:18.341 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:18.354 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:18.355 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:18.356 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:18.356 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:18.361 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:18.362 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:18.362 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:18.362 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:19.368 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:19.370 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:19.374 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:19.376 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:19.377 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:19.378 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:19.382 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:19.382 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:19.382 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:19.382 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:19.590 +08:00 INF] 用户点击开始飞单按钮
[2025-08-18 22:04:19.590 +08:00 INF] 开始游戏按钮未被按下，先模拟点击开始游戏按钮
[2025-08-18 22:04:19.597 +08:00 ERR] 获取选择的工作群组ID失败
System.InvalidOperationException: 线程间操作无效: 从不是创建控件“comboBox_WorkGroupId”的线程访问它。
   at System.Windows.Forms.Control.get_Handle()
   at Windows.Win32.PInvoke.SendMessage[T](T hWnd, MessageId Msg, WPARAM wParam, LPARAM lParam)
   at System.Windows.Forms.ComboBox.get_SelectedIndex()
   at System.Windows.Forms.ComboBox.get_SelectedItem()
   at CommandGuard.Forms.FormMain.GetSelectedWorkGroupId() in F:\SolutionCommandGuard\CommandGuard\Forms\FormMain.cs:line 4658
[2025-08-18 22:04:20.380 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:20.382 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:20.383 +08:00 INF] 找到下一个发放时间段: 114046669 (22:05:00 - 22:09:20)
[2025-08-18 22:04:20.383 +08:00 INF] 缓存已更新: 114046669 (22:05:00 - 22:09:20)
[2025-08-18 22:04:20.383 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:20.400 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:20.400 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:20.400 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:20.401 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:20.405 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:20.405 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:20.405 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:20.405 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:21.398 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:21.400 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:21.401 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:21.403 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:21.403 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:21.403 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:21.404 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:21.407 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:21.407 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:21.408 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:21.408 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:22.414 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:22.424 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:22.432 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:22.441 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:22.444 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:22.445 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:22.447 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:22.458 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:22.459 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:22.462 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:22.462 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:23.316 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:23.467 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:23.473 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:23.478 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:23.484 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:23.486 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:23.486 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:23.490 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:23.497 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:23.498 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:23.499 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:23.499 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:24.489 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:24.491 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:24.493 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:24.494 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:24.495 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:24.495 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:24.496 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:24.499 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:24.499 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:24.500 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:24.500 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:25.502 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:25.504 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:25.505 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:25.507 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:25.507 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:25.507 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:25.508 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:25.511 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:25.512 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:25.512 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:25.512 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:26.315 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:26.518 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:26.520 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:26.522 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:26.524 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:26.526 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:26.526 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:26.528 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:26.539 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:26.540 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:26.542 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:26.542 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:27.552 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:27.554 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:27.555 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:27.557 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:27.557 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:27.557 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:27.558 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:27.562 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:27.562 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:27.562 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:27.562 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:28.554 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:28.556 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:28.557 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:28.559 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:28.560 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:28.560 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:28.560 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:28.564 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:28.564 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:28.565 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:28.565 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:29.323 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:29.581 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:29.585 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:29.586 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:29.588 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:29.589 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:29.589 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:29.590 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:29.594 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:29.594 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:29.595 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:29.595 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:30.590 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:30.591 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:30.593 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:30.596 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:30.597 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:30.597 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:30.597 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:30.601 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:30.601 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:30.602 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:30.602 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:31.600 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:31.601 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:31.603 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:31.604 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:31.605 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:31.605 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:31.605 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:31.609 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:31.609 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:31.610 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:31.610 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:32.159 +08:00 ERR] 模拟点击开始游戏按钮失败
System.InvalidOperationException: 游戏服务启动失败
   at CommandGuard.Forms.FormMain.button_StartBet_Click(Object sender, EventArgs e) in F:\SolutionCommandGuard\CommandGuard\Forms\FormMain.cs:line 691
[2025-08-18 22:04:32.319 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:32.606 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:32.608 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:32.611 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:32.613 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:32.615 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:32.615 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:32.616 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:32.621 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:32.622 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:32.623 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:32.623 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:33.625 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:33.627 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:33.630 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:33.632 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:33.633 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:33.633 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:33.634 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:33.638 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:33.638 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:33.638 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:33.638 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:34.344 +08:00 INF] 游戏开始，工作群组已设置，群组ID: OneChat, 群组名称: 一起聊吧
[2025-08-18 22:04:34.350 +08:00 INF] 开始发送游戏开始前的图片
[2025-08-18 22:04:34.389 +08:00 INF] 一起聊吧群组图片发送成功: F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Images\draw.jpeg
[2025-08-18 22:04:34.389 +08:00 INF] 成功发送图片: draw.jpeg
[2025-08-18 22:04:34.397 +08:00 INF] 收到HTTP请求: POST "http://127.0.0.1:5000/api" 来自 127.0.0.1:54016
[2025-08-18 22:04:34.408 +08:00 WRN] OneChat消息处理失败: 不支持的消息类型: 1
[2025-08-18 22:04:34.638 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:34.640 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:34.641 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:34.643 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:34.643 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:34.643 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:34.644 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:34.648 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:34.648 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:34.648 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:34.648 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:35.320 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:35.418 +08:00 INF] 一起聊吧群组图片发送成功: F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Images\tan6.jpeg
[2025-08-18 22:04:35.418 +08:00 INF] 成功发送图片: tan6.jpeg
[2025-08-18 22:04:35.418 +08:00 INF] 收到HTTP请求: POST "http://127.0.0.1:5000/api" 来自 127.0.0.1:54021
[2025-08-18 22:04:35.418 +08:00 WRN] OneChat消息处理失败: 不支持的消息类型: 1
[2025-08-18 22:04:35.657 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:35.667 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:35.674 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:35.679 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:35.681 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:35.681 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:35.683 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:35.692 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:35.693 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:35.695 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:35.695 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:36.444 +08:00 INF] 一起聊吧群组图片发送成功: F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\Images\tan7.jpeg
[2025-08-18 22:04:36.444 +08:00 INF] 成功发送图片: tan7.jpeg
[2025-08-18 22:04:36.444 +08:00 INF] 收到HTTP请求: POST "http://127.0.0.1:5000/api" 来自 127.0.0.1:54022
[2025-08-18 22:04:36.444 +08:00 WRN] OneChat消息处理失败: 不支持的消息类型: 1
[2025-08-18 22:04:36.709 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:36.712 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:36.718 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:36.721 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:36.723 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:36.723 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:36.724 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:36.728 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:36.729 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:36.729 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:36.729 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:37.455 +08:00 INF] 游戏开始前的图片发送完成
[2025-08-18 22:04:37.466 +08:00 INF] 一起聊吧群组消息发送成功: 大家好
[2025-08-18 22:04:37.466 +08:00 INF] 收到HTTP请求: POST "http://127.0.0.1:5000/api" 来自 127.0.0.1:54023
[2025-08-18 22:04:37.467 +08:00 WRN] OneChat消息处理失败: 忽略机器人自身消息，避免消息回环
[2025-08-18 22:04:37.468 +08:00 INF] 一起聊吧群组消息发送成功: ★开始游戏★
[2025-08-18 22:04:37.468 +08:00 INF] 游戏开始通知已发送到聊天平台，群组: 一起聊吧
[2025-08-18 22:04:37.468 +08:00 INF] 游戏服务已启动，工作群组ComboBox已禁用
[2025-08-18 22:04:37.468 +08:00 INF] 收到HTTP请求: POST "http://127.0.0.1:5000/api" 来自 127.0.0.1:54024
[2025-08-18 22:04:37.468 +08:00 WRN] OneChat消息处理失败: 忽略机器人自身消息，避免消息回环
[2025-08-18 22:04:37.737 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:37.740 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:37.741 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:37.745 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:37.745 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:37.745 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:37.746 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:37.750 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:37.750 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:37.751 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:37.751 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:38.329 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:38.756 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:38.763 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:38.765 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:38.769 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:38.770 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:38.770 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:38.770 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:38.775 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:38.775 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:38.776 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:38.776 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:39.784 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:39.789 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:39.794 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:39.800 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:39.802 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:39.802 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:39.804 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:39.815 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:39.815 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:39.816 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:39.816 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:40.829 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:40.832 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:40.834 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:40.835 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:40.836 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:40.836 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:40.836 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:40.840 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:40.840 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:40.840 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:40.840 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:41.345 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:41.843 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:41.845 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:41.848 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:41.850 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:41.851 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:41.851 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:41.851 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:41.855 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:41.855 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:41.856 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:41.856 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:42.855 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:42.857 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:42.858 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:42.860 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:42.860 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:42.860 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:42.861 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:42.864 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:42.865 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:42.865 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:42.865 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:43.869 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:43.871 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:43.872 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:43.874 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:43.874 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:43.874 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:43.875 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:43.878 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:43.878 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:43.879 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:43.879 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:44.353 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:44.890 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:44.894 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:44.899 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:44.904 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:44.906 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:44.906 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:44.908 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:44.916 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:44.916 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:44.917 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:44.917 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:45.913 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:45.914 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:45.916 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:45.917 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:45.918 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:45.918 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:45.919 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:45.922 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:45.922 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:45.923 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:45.923 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:46.916 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:46.919 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:46.922 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:46.925 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:46.926 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:46.926 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:46.927 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:46.934 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:46.934 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:46.935 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:46.935 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:47.357 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:47.940 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:47.943 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:47.951 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:47.961 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:47.965 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:47.965 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:47.969 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:47.992 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:47.993 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:48.001 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:48.001 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:04:49.013 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:04:49.015 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:04:49.016 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:04:49.018 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:04:49.019 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:04:49.019 +08:00 INF] 新期号未开盘，继续显示上期数据: 114046668, 新期号: 114046669, 开盘时间: 22:05:00
[2025-08-18 22:04:49.019 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:04:49.023 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:04:49.023 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:04:49.025 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:04:49.025 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:05:04.454 +08:00 INF] 应用程序启动开始
[2025-08-18 22:05:04.768 +08:00 INF] 数据库服务初始化成功
[2025-08-18 22:05:04.778 +08:00 INF] 依赖注入容器配置完成，已注册 39 个服务
[2025-08-18 22:05:04.885 +08:00 INF] 成功加载Logo图片: F:\SolutionCommandGuard\CommandGuard\bin\Debug\net8.0-windows\/img/logo.png
[2025-08-18 22:05:04.886 +08:00 INF] 启动配置窗体已创建
[2025-08-18 22:05:04.886 +08:00 INF] 启动配置窗体创建成功，开始运行应用程序
[2025-08-18 22:05:13.436 +08:00 INF] 用户选择聊天平台: 一起聊吧
[2025-08-18 22:05:13.437 +08:00 INF] 运行时配置更新完成，平台: "一起聊吧"
[2025-08-18 22:05:13.542 +08:00 INF] 正在创建主窗体...
[2025-08-18 22:05:13.597 +08:00 INF] 主窗体构造函数执行完成
[2025-08-18 22:05:13.638 +08:00 INF] 开始主窗体初始化流程
[2025-08-18 22:05:13.638 +08:00 INF] 开始更新状态栏
[2025-08-18 22:05:13.638 +08:00 INF] 开始初始化查询界面
[2025-08-18 22:05:13.639 +08:00 INF] 基本UI初始化完成
[2025-08-18 22:05:13.640 +08:00 INF] 主窗体快速初始化完成，后台服务正在启动
[2025-08-18 22:05:13.641 +08:00 INF] 开始初始化核心服务
[2025-08-18 22:05:13.641 +08:00 INF] 清空IssueTime数据
[2025-08-18 22:05:13.664 +08:00 INF] 主窗体已显示
[2025-08-18 22:05:13.666 +08:00 INF] 窗体已显示，开始延迟加载数据
[2025-08-18 22:05:13.716 +08:00 INF] 清空开奖信息
[2025-08-18 22:05:13.722 +08:00 INF] 开始初始化默认系统设置
[2025-08-18 22:05:13.771 +08:00 INF] 所有系统设置已存在，无需添加新配置项
[2025-08-18 22:05:13.776 +08:00 INF] 开始加载初始数据
[2025-08-18 22:05:13.825 +08:00 INF] 数据绑定初始化完成
[2025-08-18 22:05:13.826 +08:00 INF] DataGridView初始化完成
[2025-08-18 22:05:13.827 +08:00 INF] 生成了 1421 条发放时间记录，开始批量插入数据库
[2025-08-18 22:05:13.869 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:05:13.916 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:05:13.920 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:05:13.922 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:05:13.931 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:05:13.933 +08:00 WRN] 当前期号为空，无法加载投注数据
[2025-08-18 22:05:13.944 +08:00 INF] 初始数据加载完成
[2025-08-18 22:05:13.975 +08:00 INF] 成功插入 1421 条发放时间记录
[2025-08-18 22:05:14.006 +08:00 INF] 开始监听HTTP请求
[2025-08-18 22:05:14.007 +08:00 INF] HTTP API服务启动成功，监听端口: 5000
[2025-08-18 22:05:14.008 +08:00 INF] IssueTimeService更新循环已启动
[2025-08-18 22:05:14.011 +08:00 INF] RobotService工作循环已启动
[2025-08-18 22:05:14.012 +08:00 INF] 一起聊吧机器人信息获取成功，账号: admin, 昵称: 系统管理员
[2025-08-18 22:05:14.012 +08:00 INF] 机器人群组信息为空，开始获取群组信息
[2025-08-18 22:05:14.013 +08:00 INF] 一起聊吧群组信息获取成功，群号: OneChat, 群名: 一起聊吧
[2025-08-18 22:05:14.013 +08:00 INF] 成功获取机器人群组信息，共 1 个群组
[2025-08-18 22:05:14.130 +08:00 INF] 数据刷新定时器已启动，间隔: 1000ms
[2025-08-18 22:05:14.131 +08:00 INF] 当前期投注数据更新定时器已启动，间隔: 3000ms
[2025-08-18 22:05:14.131 +08:00 INF] 核心服务初始化完成
[2025-08-18 22:05:14.571 +08:00 INF] 获取到新的开奖数据，开始处理投注结算和图片生成
[2025-08-18 22:05:14.572 +08:00 INF] === 开始投注结算处理 ===
[2025-08-18 22:05:14.574 +08:00 INF] 没有找到未结算的投注订单
[2025-08-18 22:05:14.575 +08:00 INF] 开始生成开奖数据图片
[2025-08-18 22:05:14.761 +08:00 INF] 开奖数据图片生成完成
[2025-08-18 22:05:14.920 +08:00 INF] 7行路子图生成完成
[2025-08-18 22:05:15.026 +08:00 INF] 找到当前进行中的发放时间段: 114046669 (22:05:00 - 22:09:20)
[2025-08-18 22:05:15.026 +08:00 INF] 缓存已更新: 114046669 (22:05:00 - 22:09:20)
[2025-08-18 22:05:15.056 +08:00 INF] 6行路子图生成完成
[2025-08-18 22:05:15.136 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:05:15.139 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:05:15.140 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:05:15.142 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:05:15.143 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:05:15.143 +08:00 INF] 首次加载当前期投注数据: 114046669
[2025-08-18 22:05:15.152 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:05:15.159 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:05:15.161 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:05:15.162 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:05:15.162 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:05:15.162 +08:00 INF] 检测到群组信息变化，从 0 个群组更新为 1 个群组
[2025-08-18 22:05:15.166 +08:00 INF] 工作群组ComboBox已更新，共 1 个群组，已选择第一项
[2025-08-18 22:05:15.334 +08:00 INF] 7行完整路子图生成完成
[2025-08-18 22:05:15.611 +08:00 INF] 6行完整路子图生成完成
[2025-08-18 22:05:15.611 +08:00 INF] 所有开奖图片生成完成
[2025-08-18 22:05:15.612 +08:00 INF] 开始发送开奖数据和摊路图到聊天平台
[2025-08-18 22:05:15.612 +08:00 INF] 游戏服务未启动，跳过发送开奖图片
[2025-08-18 22:05:16.168 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:05:16.174 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:05:16.177 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:05:16.181 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:05:16.182 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:05:16.184 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:05:16.192 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:05:16.192 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:05:16.193 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:05:16.193 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:05:16.937 +08:00 INF] 用户点击开始飞单按钮
[2025-08-18 22:05:16.937 +08:00 INF] 开始游戏按钮未被按下，先模拟点击开始游戏按钮
[2025-08-18 22:05:16.950 +08:00 ERR] 获取选择的工作群组ID失败
System.InvalidOperationException: 线程间操作无效: 从不是创建控件“comboBox_WorkGroupId”的线程访问它。
   at System.Windows.Forms.Control.get_Handle()
   at System.Windows.Forms.Control.IHandle<Windows.Win32.Foundation.HWND>.get_Handle()
   at Windows.Win32.PInvoke.SendMessage[T](T hWnd, MessageId Msg, WPARAM wParam, LPARAM lParam)
   at System.Windows.Forms.ComboBox.get_SelectedIndex()
   at System.Windows.Forms.ComboBox.get_SelectedItem()
   at CommandGuard.Forms.FormMain.GetSelectedWorkGroupId() in F:\SolutionCommandGuard\CommandGuard\Forms\FormMain.cs:line 4658
[2025-08-18 22:05:17.194 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:05:17.196 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:05:17.198 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:05:17.200 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:05:17.201 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:05:17.202 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:05:17.208 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:05:17.208 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:05:17.209 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:05:17.209 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:05:18.209 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:05:18.211 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:05:18.214 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:05:18.216 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:05:18.217 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:05:18.218 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:05:18.223 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:05:18.224 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:05:18.224 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:05:18.224 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:05:19.224 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:05:19.226 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:05:19.228 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:05:19.229 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:05:19.230 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:05:19.231 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:05:19.234 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:05:19.234 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:05:19.235 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:05:19.235 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:05:20.224 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:05:20.226 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:05:20.227 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:05:20.229 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:05:20.229 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:05:20.230 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:05:20.233 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:05:20.233 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:05:20.234 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:05:20.234 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:05:21.224 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:05:21.226 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:05:21.227 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:05:21.229 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:05:21.229 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:05:21.230 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:05:21.233 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:05:21.233 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:05:21.233 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:05:21.233 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:05:21.791 +08:00 ERR] 模拟点击开始游戏按钮失败
System.InvalidOperationException: 游戏服务启动失败
   at CommandGuard.Forms.FormMain.button_StartBet_Click(Object sender, EventArgs e) in F:\SolutionCommandGuard\CommandGuard\Forms\FormMain.cs:line 691
[2025-08-18 22:05:22.231 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:05:22.233 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:05:22.234 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:05:22.236 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:05:22.237 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:05:22.238 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:05:22.241 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:05:22.241 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:05:22.242 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:05:22.242 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
[2025-08-18 22:05:23.240 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: UZ8H37F9, 类型: 全部, 包含假人: true
[2025-08-18 22:05:23.242 +08:00 INF] 查询上下分记录完成，共找到 3 条记录
[2025-08-18 22:05:23.243 +08:00 INF] 开始查询上下分记录，时间范围: "0001-01-01T00:00:00.0000000" - "9999-12-31T23:59:59.9999999", 账号: FZVH7NYL, 类型: 全部, 包含假人: true
[2025-08-18 22:05:23.245 +08:00 INF] 查询上下分记录完成，共找到 1 条记录
[2025-08-18 22:05:23.245 +08:00 INF] 已加载 8 个系统设置项到控件
[2025-08-18 22:05:23.246 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-18T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 全部, 账号: 全部, 包含假人: true
[2025-08-18 22:05:23.249 +08:00 INF] 查询投注记录完成，共找到 234 条记录
[2025-08-18 22:05:23.249 +08:00 INF] 开始查询投注记录，时间范围: "2025-08-15T00:00:00.0000000+08:00" - "2025-08-19T00:00:00.0000000+08:00", 期号: 114046667, 账号: 全部, 包含假人: true
[2025-08-18 22:05:23.249 +08:00 INF] 查询投注记录完成，共找到 0 条记录
[2025-08-18 22:05:23.250 +08:00 INF] 上期总输赢计算完成，期号: 114046667, 无已结算记录，总盈亏: 0
