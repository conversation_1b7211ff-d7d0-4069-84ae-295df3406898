using System.Net;
using System.Text;
using System.Text.Json;
using CommandGuard.Configuration;
using CommandGuard.Enums;
using CommandGuard.Interfaces.Chat;
using CommandGuard.Interfaces.Infrastructure;
using CommandGuard.Models;
using CommandGuard.Models.External;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Infrastructure;

/// <summary>
/// HTTP API服务实现类 - 简化版本，内联消息转换逻辑
/// 使用HttpListener提供HTTP监听服务
/// 接收外部POST消息并处理
/// 实现IDisposable确保资源正确释放
/// </summary>
public sealed class HttpApiService(
    ILogger<HttpApiService> logger,
    IMessageStorageService storageService) : IHttpApiService
{
    private HttpListener? _httpListener;
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _listenerTask;

    /// <summary>
    /// 启动HTTP监听服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            const int port = 5000; // 固定使用端口5000

            _httpListener = new HttpListener();
            _httpListener.Prefixes.Add($@"http://localhost:{port}/");
            _httpListener.Prefixes.Add($@"http://127.0.0.1:{port}/");
            _httpListener.Prefixes.Add($@"http://+:{port}/"); // 监听所有IP

            _httpListener.Start();

            _cancellationTokenSource = new CancellationTokenSource();
            var combinedToken = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, _cancellationTokenSource.Token).Token;

            // 初始化统计信息
            _listenerTask = ListenAsync(combinedToken);

            logger.LogInformation(@"HTTP API服务启动成功，监听端口: {Port}", port);
            await Task.CompletedTask;
        }
        catch (HttpListenerException ex) when (ex.ErrorCode == 183 || ex.ErrorCode == 32) // ERROR_ALREADY_EXISTS 或 ERROR_SHARING_VIOLATION
        {
            logger.LogError(@"端口5000已被其他程序占用，错误码: {ErrorCode}", ex.ErrorCode);
            await StopAsync(CancellationToken.None);

            // 获取占用端口的进程信息
            var occupyingProcess = GetProcessUsingPort(5000);
            var errorMessage = occupyingProcess != null
                ? $@"端口5000已被程序 '{occupyingProcess}' 占用，请关闭该程序后重新启动CommandGuard。"
                : @"端口5000已被其他程序占用，请关闭占用端口5000的程序后重新启动CommandGuard。";

            throw new InvalidOperationException(errorMessage, ex);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"启动HTTP API服务失败");
            await StopAsync(CancellationToken.None);
            throw new InvalidOperationException($@"启动HTTP API服务失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 停止HTTP监听服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation(@"正在停止HTTP API服务...");

            // 1. 首先取消所有操作
            if (_cancellationTokenSource != null)
            {
                await _cancellationTokenSource.CancelAsync();
            }

            // 2. 停止HTTP监听器
            if (_httpListener?.IsListening == true)
            {
                try
                {
                    _httpListener.Stop();
                    logger.LogDebug(@"HTTP监听器已停止");
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, @"停止HTTP监听器时发生异常");
                }
            }

            // 3. 等待监听任务完成，设置超时避免无限等待
            if (_listenerTask != null)
            {
                try
                {
                    using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                    using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                        cancellationToken, timeoutCts.Token);

                    await _listenerTask.WaitAsync(combinedCts.Token).ConfigureAwait(false);
                    logger.LogDebug(@"监听任务已完成");
                }
                catch (OperationCanceledException)
                {
                    logger.LogWarning(@"等待监听任务完成超时，强制继续清理");
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, @"等待监听任务完成时发生异常");
                }
            }

            // 4. 关闭并释放HTTP监听器
            if (_httpListener != null)
            {
                try
                {
                    _httpListener.Close();
                    logger.LogDebug(@"HTTP监听器已关闭");
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, @"关闭HTTP监听器时发生异常");
                }
                finally
                {
                    _httpListener = null;
                }
            }

            // 5. 释放取消令牌源
            if (_cancellationTokenSource != null)
            {
                try
                {
                    _cancellationTokenSource.Dispose();
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, @"释放取消令牌源时发生异常");
                }
                finally
                {
                    _cancellationTokenSource = null;
                }
            }

            // 6. 清理其他资源
            _listenerTask = null;

            // 7. 强制垃圾回收，确保资源被释放
            GC.Collect();
            GC.WaitForPendingFinalizers();

            logger.LogInformation(@"HTTP API服务已完全停止并清理资源");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"停止HTTP API服务时发生异常");

            // 即使出现异常，也要尝试清理资源
            try
            {
                _httpListener?.Close();
                _httpListener = null;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
                _listenerTask = null;
            }
            catch
            {
                // 忽略清理时的异常
            }
        }
    }

    /// <summary>
    /// 监听HTTP请求的主循环
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task ListenAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation(@"开始监听HTTP请求");

        while (!cancellationToken.IsCancellationRequested && _httpListener?.IsListening == true)
        {
            try
            {
                var context = await _httpListener.GetContextAsync().ConfigureAwait(false);

                // 在后台处理请求，不阻塞主循环
                _ = Task.Run(async () => await ProcessRequestAsync(context, cancellationToken)
                    .ConfigureAwait(false), cancellationToken);
            }
            catch (ObjectDisposedException)
            {
                // HttpListener已被释放，正常退出
                break;
            }
            catch (HttpListenerException ex) when (ex.ErrorCode == 995) // ERROR_OPERATION_ABORTED
            {
                // 操作被取消，正常退出
                break;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, @"监听HTTP请求时发生异常");
                await Task.Delay(1000, cancellationToken).ConfigureAwait(false); // 短暂延迟后继续
            }
        }

        logger.LogInformation(@"HTTP请求监听已停止");
    }

    /// <summary>
    /// 处理单个HTTP请求
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task ProcessRequestAsync(HttpListenerContext context, CancellationToken cancellationToken)
    {
        var request = context.Request;
        var response = context.Response;

        try
        {
            logger.LogInformation(@"收到HTTP请求: {Method} {Url} 来自 {RemoteEndPoint}",
                request.HttpMethod, request.Url, request.RemoteEndPoint);

            // 设置CORS头
            response.Headers.Add("Access-Control-Allow-Origin", "*");
            response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");

            // 处理OPTIONS预检请求
            if (request.HttpMethod == "OPTIONS")
            {
                response.StatusCode = 200;
                response.Close();
                return;
            }

            // 检查路径
            if (!request.Url?.AbsolutePath.Equals("/api", StringComparison.OrdinalIgnoreCase) == true)
            {
                await SendErrorResponseAsync(response, 404, @"端点不存在").ConfigureAwait(false);
                return;
            }

            // 只处理POST请求用于消息提交
            if (request.HttpMethod != "POST")
            {
                await SendErrorResponseAsync(response, 405, @"只支持GET和POST方法").ConfigureAwait(false);
                return;
            }

            // 判断是否已经开始了游戏服务,如果还没开始则直接return
            if (!RuntimeConfiguration.IsGameServiceStarted)
            {
                await SendErrorResponseAsync(response, 503, @"游戏服务未启动").ConfigureAwait(false);
                return;
            }

            // 读取请求体
            string requestBody;
            using (var reader = new StreamReader(request.InputStream, Encoding.UTF8))
            {
                requestBody = await reader.ReadToEndAsync(cancellationToken).ConfigureAwait(false);
            }

            if (string.IsNullOrWhiteSpace(requestBody))
            {
                await SendErrorResponseAsync(response, 400, @"请求体不能为空").ConfigureAwait(false);
                return;
            }

            // 根据当前选择的平台处理消息
            await ProcessMessageAsync(requestBody, response).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理HTTP请求时发生异常");
            try
            {
                await SendErrorResponseAsync(response, 500, @"服务器内部错误").ConfigureAwait(false);
            }
            catch
            {
                // 忽略响应发送失败
            }
        }
    }

    /// <summary>
    /// 根据当前选择的平台处理消息转换和存储
    /// </summary>
    /// <param name="requestBody">请求体内容</param>
    /// <param name="response">HTTP响应</param>
    private async Task ProcessMessageAsync(string requestBody, HttpListenerResponse response)
    {
        try
        {
            // 获取当前选择的平台
            var selectedPlatform = RuntimeConfiguration.SelectedChatApp;
            logger.LogDebug(@"当前选择的平台: {Platform}", selectedPlatform);

            InternalMessage? internalMessage;
            string platformName;

            // 根据选择的平台处理消息
            switch (selectedPlatform)
            {
                case EnumChatApp.一起聊吧:
                    var (oneChatSuccess, oneChatError, oneChatMessage) = ProcessOneChatMessage(requestBody);
                    if (!oneChatSuccess)
                    {
                        logger.LogWarning(@"OneChat消息处理失败: {Error}", oneChatError);
                        await SendErrorResponseAsync(response, 400, $@"OneChat消息处理失败: {oneChatError}").ConfigureAwait(false);
                        return;
                    }

                    internalMessage = oneChatMessage;
                    platformName = @"OneChat";
                    break;

                case EnumChatApp.MyQQ:
                    var (myQqSuccess, myQqError, myQqMessage) = ProcessMyQqMessage(requestBody);
                    if (!myQqSuccess)
                    {
                        logger.LogWarning(@"MyQQ消息处理失败: {Error}", myQqError);
                        await SendErrorResponseAsync(response, 400, $@"MyQQ消息处理失败: {myQqError}").ConfigureAwait(false);
                        return;
                    }

                    internalMessage = myQqMessage;
                    platformName = @"MyQQ";
                    break;

                default:
                    logger.LogError(@"不支持的平台: {Platform}", selectedPlatform);
                    await SendErrorResponseAsync(response, 400, $@"不支持的平台: {selectedPlatform}").ConfigureAwait(false);
                    return;
            }

            if (internalMessage == null)
            {
                logger.LogWarning(@"消息转换结果为空，平台: {Platform}", platformName);
                await SendErrorResponseAsync(response, 500, @"消息转换失败").ConfigureAwait(false);
                return;
            }

            // 存储到数据库
            var messageId = await storageService.SaveMessageAsync(internalMessage).ConfigureAwait(false);

            // 返回成功响应
            var successResponse = new
            {
                success = true,
                message = $@"{platformName}消息处理成功",
                data = new
                {
                    messageId,
                    externalId = internalMessage.ExternalMessageId,
                    platform = platformName,
                    account = internalMessage.Account,
                    receivedTime = DateTime.Now
                }
            };

            await SendJsonResponseAsync(response, 200, successResponse).ConfigureAwait(false);

            logger.LogInformation(@"成功处理{Platform}消息，内部ID: {MessageId}, 外部ID: {ExternalId}, 用户: {Account}",
                platformName, messageId, internalMessage.ExternalMessageId, internalMessage.Account);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理消息异常");
            try
            {
                await SendErrorResponseAsync(response, 500, @"服务器内部错误").ConfigureAwait(false);
            }
            catch
            {
                // 忽略响应发送失败的异常
            }
        }
    }

    /// <summary>
    /// 处理OneChat消息
    /// </summary>
    /// <param name="jsonContent">JSON消息内容</param>
    /// <returns>处理结果</returns>
    private (bool Success, string ErrorMessage, InternalMessage? InternalMessage) ProcessOneChatMessage(string jsonContent)
    {
        try
        {
            logger.LogDebug(@"开始处理OneChat消息: {JsonContent}", jsonContent);

            // 解析OneChat消息
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
            var oneChatMessage = JsonSerializer.Deserialize<OneChatMessage>(jsonContent, options);

            if (oneChatMessage == null)
            {
                return (false, @"OneChat消息解析失败", null);
            }

            // 验证消息
            var (isValid, errorMessage) = ValidateOneChatMessage(oneChatMessage);
            if (!isValid)
            {
                return (false, errorMessage, null);
            }

            // 转换为内部消息
            var internalMessage = ConvertOneChatToInternalMessage(oneChatMessage);

            logger.LogInformation(@"OneChat消息处理成功，外部ID: {ExternalId}, 内部ID: {InternalId}",
                oneChatMessage.Id, internalMessage.Id);

            return (true, string.Empty, internalMessage);
        }
        catch (JsonException ex)
        {
            logger.LogWarning(ex, @"OneChat消息JSON解析失败: {JsonContent}", jsonContent);
            return (false, $@"JSON格式错误: {ex.Message}", null);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理OneChat消息异常: {JsonContent}", jsonContent);
            return (false, $@"处理异常: {ex.Message}", null);
        }
    }

    /// <summary>
    /// 处理MyQQ消息
    /// </summary>
    /// <param name="jsonContent">JSON消息内容</param>
    /// <returns>处理结果</returns>
    private (bool Success, string ErrorMessage, InternalMessage? InternalMessage) ProcessMyQqMessage(string jsonContent)
    {
        try
        {
            logger.LogDebug(@"开始处理MyQQ消息: {JsonContent}", jsonContent);

            // 解析MyQQ消息
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
            var myQqMessage = JsonSerializer.Deserialize<MyQqMessage>(jsonContent, options);

            if (myQqMessage == null)
            {
                return (false, @"MyQQ消息解析失败", null);
            }

            // 验证消息
            var (isValid, errorMessage) = ValidateMyQqMessage(myQqMessage);
            if (!isValid)
            {
                return (false, errorMessage, null);
            }

            // 转换为内部消息
            var internalMessage = ConvertMyQqToInternalMessage(myQqMessage);

            logger.LogInformation(@"MyQQ消息处理成功，外部ID: {ExternalId}, 内部ID: {InternalId}, 来源: {Source}",
                myQqMessage.MessageId, internalMessage.Id, myQqMessage.GetSourceDescription());

            return (true, string.Empty, internalMessage);
        }
        catch (JsonException ex)
        {
            logger.LogWarning(ex, @"MyQQ消息JSON解析失败: {JsonContent}", jsonContent);
            return (false, $@"JSON格式错误: {ex.Message}", null);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理MyQQ消息异常: {JsonContent}", jsonContent);
            return (false, $@"处理异常: {ex.Message}", null);
        }
    }

    /// <summary>
    /// 发送JSON响应
    /// </summary>
    /// <param name="response">HTTP响应</param>
    /// <param name="statusCode">状态码</param>
    /// <param name="data">响应数据</param>
    private async Task SendJsonResponseAsync(HttpListenerResponse response, int statusCode, object data)
    {
        try
        {
            response.StatusCode = statusCode;
            response.ContentType = @"application/json; charset=utf-8";

            var jsonResponse = JsonSerializer.Serialize(data, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            });

            var buffer = Encoding.UTF8.GetBytes(jsonResponse);
            response.ContentLength64 = buffer.Length;

            await response.OutputStream.WriteAsync(buffer, 0, buffer.Length).ConfigureAwait(false);
            response.Close();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"发送JSON响应时发生异常");
            try
            {
                response.Close();
            }
            catch
            {
                // 忽略关闭响应时的异常
            }
        }
    }

    /// <summary>
    /// 发送错误响应
    /// </summary>
    /// <param name="response">HTTP响应</param>
    /// <param name="statusCode">状态码</param>
    /// <param name="errorMessage">错误消息</param>
    private async Task SendErrorResponseAsync(HttpListenerResponse response, int statusCode, string errorMessage)
    {
        var errorResponse = new
        {
            success = false,
            message = errorMessage,
            timestamp = DateTime.Now
        };

        await SendJsonResponseAsync(response, statusCode, errorResponse).ConfigureAwait(false);
    }

    #region 端口管理

    /// <summary>
    /// 获取占用指定端口的进程名称
    /// </summary>
    /// <param name="port">端口号</param>
    /// <returns>进程名称，如果无法获取则返回null</returns>
    private string? GetProcessUsingPort(int port)
    {
        try
        {
            using var process = new System.Diagnostics.Process();
            process.StartInfo.FileName = @"netstat";
            process.StartInfo.Arguments = @"-ano";
            process.StartInfo.UseShellExecute = false;
            process.StartInfo.RedirectStandardOutput = true;
            process.StartInfo.CreateNoWindow = true;

            process.Start();
            var output = process.StandardOutput.ReadToEnd();
            process.WaitForExit();

            var lines = output.Split('\n');
            foreach (var line in lines)
            {
                if (line.Contains($@":{port} ") && (line.Contains(@"LISTENING") || line.Contains(@"监听")))
                {
                    var parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length > 0 && int.TryParse(parts[^1], out int pid))
                    {
                        try
                        {
                            var proc = System.Diagnostics.Process.GetProcessById(pid);
                            return $@"{proc.ProcessName} (PID: {pid})";
                        }
                        catch
                        {
                            return $@"PID: {pid}";
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, @"获取端口 {Port} 占用进程信息失败", port);
        }

        return null;
    }

    #endregion

    #region 内联消息转换方法

    /// <summary>
    /// 验证OneChat消息的有效性
    /// </summary>
    private (bool IsValid, string ErrorMessage) ValidateOneChatMessage(OneChatMessage externalMessage)
    {
        try
        {
            // 使用OneChatMessage自带的验证方法
            var (isValid, errorMessage) = externalMessage.Validate();
            if (!isValid)
            {
                logger.LogWarning(@"OneChat消息验证失败，外部ID: {ExternalId}, 错误: {Error}",
                    externalMessage.Id, errorMessage);
                return (false, errorMessage);
            }

            // 额外验证：只处理文本消息
            if (externalMessage.Type != 0) // Type = 0 表示文本消息
            {
                var message = $@"不支持的消息类型: {externalMessage.Type}";
                logger.LogDebug(@"OneChat消息类型不支持，外部ID: {ExternalId}, 类型: {Type}",
                    externalMessage.Id, externalMessage.Type);
                return (false, message);
            }

            // 额外验证：过滤机器人自己发送的消息，避免消息回环
            var robotAccount = RuntimeConfiguration.GetRobotAccount();
            if (!string.IsNullOrEmpty(robotAccount) && externalMessage.Uid == robotAccount)
            {
                var message = @"忽略机器人自身消息，避免消息回环";
                logger.LogDebug(@"OneChat忽略机器人自身消息，外部ID: {ExternalId}, 机器人账号: {RobotAccount}",
                    externalMessage.Id, robotAccount);
                return (false, message);
            }

            logger.LogDebug(@"OneChat消息验证通过，外部ID: {ExternalId}, 用户: {Account}",
                externalMessage.Id, externalMessage.Uid);

            return (true, string.Empty);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"OneChat消息验证异常，外部ID: {ExternalId}", externalMessage.Id);
            return (false, $@"消息验证异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 将OneChat消息转换为内部消息
    /// </summary>
    private InternalMessage ConvertOneChatToInternalMessage(OneChatMessage externalMessage)
    {
        if (externalMessage == null)
        {
            throw new ArgumentNullException(nameof(externalMessage));
        }

        try
        {
            var internalMessage = new InternalMessage
            {
                Id = Guid.NewGuid().ToString(),
                Account = externalMessage.Uid,
                NickName = externalMessage.NickName,
                Content = externalMessage.Content,
                Status = EnumMessageStatus.Pending,
                CreatedTime = DateTime.Now,
                ExternalMessageId = externalMessage.Id.ToString(),
                ExternalPlatform = @"OneChat",
                ExternalCreatedTime = externalMessage.CreatedAt
            };

            logger.LogDebug(@"OneChat消息转换成功，外部ID: {ExternalId}, 内部ID: {InternalId}, 用户: {Account}",
                externalMessage.Id, internalMessage.Id, externalMessage.Uid);

            return internalMessage;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"OneChat消息转换失败，外部ID: {ExternalId}", externalMessage.Id);
            throw;
        }
    }

    /// <summary>
    /// 验证MyQQ消息的有效性
    /// </summary>
    private (bool IsValid, string ErrorMessage) ValidateMyQqMessage(MyQqMessage externalMessage)
    {
        try
        {
            // 使用MyQqMessage自带的验证方法
            var (isValid, errorMessage) = externalMessage.Validate();
            if (!isValid)
            {
                logger.LogWarning(@"MyQQ消息验证失败，外部ID: {ExternalId}, 错误: {Error}",
                    externalMessage.MessageId, errorMessage);
                return (false, errorMessage);
            }

            // 额外验证：过滤机器人自己发送的消息，避免消息回环
            var robotAccount = RuntimeConfiguration.GetRobotAccount();
            if (!string.IsNullOrEmpty(robotAccount) && externalMessage.Qq == robotAccount)
            {
                var message = @"忽略机器人自身消息，避免消息回环";
                logger.LogDebug(@"MyQQ忽略机器人自身消息，外部ID: {ExternalId}, 机器人账号: {RobotAccount}",
                    externalMessage.MessageId, robotAccount);
                return (false, message);
            }

            logger.LogDebug(@"MyQQ消息验证通过，外部ID: {ExternalId}, 用户: {Account}, 来源: {Source}",
                externalMessage.MessageId, externalMessage.Qq, externalMessage.GetSourceDescription());

            return (true, string.Empty);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"MyQQ消息验证异常，外部ID: {ExternalId}", externalMessage.MessageId);
            return (false, $@"消息验证异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 将MyQQ消息转换为内部消息
    /// </summary>
    private InternalMessage ConvertMyQqToInternalMessage(MyQqMessage externalMessage)
    {
        if (externalMessage == null)
        {
            throw new ArgumentNullException(nameof(externalMessage));
        }

        try
        {
            var internalMessage = new InternalMessage
            {
                Id = Guid.NewGuid().ToString(),
                Account = externalMessage.Qq,
                NickName = externalMessage.Nickname,
                Content = externalMessage.Message,
                Status = EnumMessageStatus.Pending,
                CreatedTime = DateTime.Now,
                ExternalMessageId = externalMessage.MessageId.ToString(),
                ExternalPlatform = @"MyQQ",
                ExternalCreatedTime = DateTimeOffset.FromUnixTimeSeconds(externalMessage.Timestamp).DateTime
            };

            logger.LogDebug(@"MyQQ消息转换成功，外部ID: {ExternalId}, 内部ID: {InternalId}, 用户: {Account}, 来源: {Source}",
                externalMessage.MessageId, internalMessage.Id, externalMessage.Qq, externalMessage.GetSourceDescription());

            return internalMessage;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"MyQQ消息转换失败，外部ID: {ExternalId}", externalMessage.MessageId);
            throw;
        }
    }

    #endregion

    #region IDisposable Implementation

    private bool _disposed;

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源的具体实现
    /// </summary>
    /// <param name="disposing">是否正在释放托管资源</param>
    private void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                try
                {
                    // 同步停止服务，确保资源被正确清理
                    StopAsync().GetAwaiter().GetResult();
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, @"在Dispose中停止HTTP API服务时发生异常");
                }
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// 析构函数，确保资源被释放
    /// </summary>
    ~HttpApiService()
    {
        Dispose(false);
    }

    #endregion
}