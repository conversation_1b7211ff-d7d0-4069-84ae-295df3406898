using System.Text;
using System.Text.RegularExpressions;
using AiHelper;
using CommandGuard.Configuration;
using CommandGuard.Constants;
using CommandGuard.Enums;
using CommandGuard.Helpers;
using CommandGuard.Interfaces.Business;
using CommandGuard.Interfaces.Chat;
using CommandGuard.Interfaces.Lottery;
using CommandGuard.Models;
using CommandGuard.ViewModels;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Business;

/// <summary>
/// Command服务实现
/// 提供完整的上分下分申请管理功能：创建、审核、处理、查询
/// 包含完整的参数验证、异常处理、事务管理、日志记录
/// 支持异步操作、状态管理、财务记录集成
/// </summary>
#pragma warning disable CS9113 // 参数未读
public sealed class CommandService(
    ILogger<CommandService> logger,
    IFreeSql fSql,
    IMessageStorageService messageStorageService,
    IDepositWithdrawRecordService depositWithdrawRecordService,
    IFinancialService financialService,
    IIssueTimeService issueTimeService,
    IBetCommandValidatorService betCommandValidatorService,
    IOddsService oddsService,
    IBetRecordService betRecordService,
    IMemberService memberService,
    IChatService chatService) : ICommandService
#pragma warning restore CS9113
{
    #region 识别指令

    public bool IsDepositRequest(string command)
    {
        var patterns = new[] { @"上\s*\d+", @"上分\s*\d+", @"起\s*\d+", @"起分\s*\d+" };
        return patterns.Any(pattern => Regex.IsMatch(command, pattern, RegexOptions.IgnoreCase));
    }

    public bool IsWithdrawRequest(string command)
    {
        var patterns = new[] { @"下\s*\d+", @"下分\s*\d+", @"落\s*\d+", @"落分\s*\d+" };
        return patterns.Any(pattern => Regex.IsMatch(command, pattern, RegexOptions.IgnoreCase));
    }

    #endregion

    /// <summary>
    /// 处理上分申请 - 用户充值请求处理
    ///
    /// 功能：
    /// - 解析上分指令中的金额
    /// - 创建充值申请记录
    /// - 自动审核通过并处理
    /// - 更新用户余额
    /// - 记录财务流水
    ///
    /// 支持的指令格式：
    /// - "上100"、"上分100"、"起100"、"起分100"
    /// </summary>
    /// <param name="message">包含上分指令的内部消息</param>
    public async Task ProcessDepositRequestAsync(InternalMessage message)
    {
        // 使用正则表达式提取消息中的数字金额
        var match = Regex.Match(message.Content, @"(\d+)", RegexOptions.IgnoreCase);
        if (match.Success && decimal.TryParse(match.Groups[1].Value, out var amount))
        {
            try
            {
                // 获取会员信息（此时会员应该已存在）
                var member = await memberService.GetMemberAsync(message.Account);
                logger.LogInformation(@"会员确认存在，账号: {Account}, 昵称: {NickName}", member?.Account, member?.NickName);

                // 创建上分申请记录
                await depositWithdrawRecordService.CreateDepositRequestAsync(message.Account, amount, message.Content, message.Id);

                // 标记消息为已处理
                logger.LogInformation(@"创建上分申请成功，用户: {Account}, 金额: {Amount}", message.Account, amount);
            }
            catch (Exception ex)
            {
                // 处理异常，标记消息为失败
                logger.LogError(ex, @"处理上分申请时发生异常，用户: {Account}, 金额: {Amount}", message.Account, amount);
            }
        }
    }

    /// <summary>
    /// 处理下分申请 - 用户提现请求处理
    ///
    /// 功能：
    /// - 解析下分指令中的金额
    /// - 创建提现申请记录
    /// - 自动审核通过并处理
    /// - 扣减用户余额
    /// - 记录财务流水
    ///
    /// 支持的指令格式：
    /// - "下100"、"下分100"、"落100"、"落分100"
    /// </summary>
    /// <param name="message">包含下分指令的内部消息</param>
    public async Task ProcessWithdrawRequestAsync(InternalMessage message)
    {
        // 使用正则表达式提取消息中的数字金额
        var match = Regex.Match(message.Content, @"(\d+)", RegexOptions.IgnoreCase);
        if (match.Success && decimal.TryParse(match.Groups[1].Value, out var amount))
        {
            try
            {
                // 获取会员信息（此时会员应该已存在）
                var member = await memberService.GetMemberAsync(message.Account);
                logger.LogInformation(@"会员确认存在，账号: {Account}, 昵称: {NickName}", member?.Account, member?.NickName);

                // 创建下分申请记录
                await depositWithdrawRecordService.CreateWithdrawRequestAsync(message.Account, amount, message.Content, message.Id);

                // 标记消息为已处理
                logger.LogInformation(@"创建下分申请成功，用户: {Account}, 金额: {Amount}", message.Account, amount);
            }
            catch (Exception ex)
            {
                // 处理异常，标记消息为失败
                logger.LogError(ex, @"处理下分申请时发生异常，用户: {Account}, 金额: {Amount}", message.Account, amount);
            }
        }
    }

    /// <summary>
    /// 处理消息指令 - 统一的消息处理入口
    ///
    /// 功能：
    /// - 统一处理各种指令类型
    /// - 支持数字指令、中文指令、英文指令
    /// - 正则表达式匹配复杂指令
    /// - 兜底处理未匹配指令为投注
    ///
    /// 支持的指令：
    /// - 0: 指令说明
    /// - 1/查/查分: 查询余额
    /// - 2/图/路: 发送路子图
    /// - 3: 发送开奖图
    /// - 4/回/回水/返水/反水: 回水处理
    /// - 5/水/流水: 流水详情
    /// - 6/时间/封/开: 查询时间
    /// - 7/撤/撤销/取消: 撤销投注
    /// - 8: 发送摊图
    /// - 9: 发送结算报告
    /// - 上分指令: 上100/上分100/起100/起分100
    /// - 下分指令: 下100/下分100/落100/落分100
    /// - 其他: 投注指令
    /// </summary>
    /// <param name="message">包含指令的内部消息</param>
    public async Task ProcessMessageCommandAsync(InternalMessage message)
    {
        // 获取会员信息（此时会员应该已存在）
        var member = await memberService.GetMemberAsync(message.Account);
        if (member == null)
        {
            logger.LogWarning(@"会员不存在，账号: {Account}", message.Account);
            return;
        }

        // 处理换行符 - 统一文本格式
        string msgCon = message.Content
            .Replace(Environment.NewLine, " ")
            .Replace("\\r\\n", " ")
            .Replace("\\r", " ")
            .Replace("\\n", " ")
            .Replace("\r\n", " ")
            .Replace("\r", " ")
            .Replace("\n", " ")
            .Replace("  ", " ")
            .Trim();

        // 判断指令类型
        if (msgCon == "0")
        {
            await 指令说明Handler(member);
        }
        else if (msgCon == "1" || msgCon == "查" || msgCon == "查分")
        {
            await 查分指令Handler(member);
        }
        else if (msgCon == "2" || msgCon == "图" || msgCon == "路")
        {
            await 发送路子图Handler();
        }
        else if (msgCon == "3")
        {
            await 发送开奖图Handler();
        }
        else if (msgCon == "4" || msgCon == "回" || msgCon == "回水" || msgCon == "返水" || msgCon == "反水")
        {
            await RebateHandler(member, message);
        }
        else if (msgCon == "5" || msgCon == "水" || msgCon == "流水")
        {
            await 流水详情Handler(member);
        }
        else if (msgCon == "6" || msgCon == "时间" || msgCon == "封" || msgCon == "开")
        {
            await 查询时间指令Handler(member);
        }
        else if (msgCon == "7" || msgCon == "撤" || msgCon == "撤销" || msgCon == "取消")
        {
            await CancelBetDataHandler(member, message);
        }
        else if (msgCon == "8")
        {
            await SendTanImageFullHandler();
        }
        else if (msgCon == "9")
        {
            await SendSettleReportHandler();
        }
        else if ((Regex.Matches(msgCon, "上[1-9]\\d*$").Count > 0 && Regex.Matches(msgCon, "上[1-9]\\d*$")[0].Value.Length.Equals(msgCon.Length)) ||
                 (Regex.Matches(msgCon, "上分[1-9]\\d*$").Count > 0 && Regex.Matches(msgCon, "上分[1-9]\\d*$")[0].Value.Length.Equals(msgCon.Length)) ||
                 (Regex.Matches(msgCon, "起[1-9]\\d*$").Count > 0 && Regex.Matches(msgCon, "起[1-9]\\d*$")[0].Value.Length.Equals(msgCon.Length)) ||
                 (Regex.Matches(msgCon, "起分[1-9]\\d*$").Count > 0 && Regex.Matches(msgCon, "起分[1-9]\\d*$")[0].Value.Length.Equals(msgCon.Length)))
        {
            await 上分指令Handler(member, message);
        }
        else if ((Regex.Matches(msgCon, "下[0-9]+(\\.[0-9]+)?$").Count > 0 && Regex.Matches(msgCon, "下[0-9]+(\\.[0-9]+)?$")[0].Value.Length.Equals(msgCon.Length)) ||
                 (Regex.Matches(msgCon, "下分[0-9]+(\\.[0-9]+)?$").Count > 0 && Regex.Matches(msgCon, "下分[0-9]+(\\.[0-9]+)?$")[0].Value.Length.Equals(msgCon.Length)) ||
                 (Regex.Matches(msgCon, "落[0-9]+(\\.[0-9]+)?$").Count > 0 && Regex.Matches(msgCon, "落[0-9]+(\\.[0-9]+)?$")[0].Value.Length.Equals(msgCon.Length)) ||
                 (Regex.Matches(msgCon, "落分[0-9]+(\\.[0-9]+)?$").Count > 0 && Regex.Matches(msgCon, "落分[0-9]+(\\.[0-9]+)?$")[0].Value.Length.Equals(msgCon.Length)))
        {
            await 下分指令Handler(member, message);
        }
        else
        {
            await 下注指令Handler(member, message);
        }
    }

    /// <summary>
    /// 处理投注指令 - 用户投注请求处理
    ///
    /// 功能：
    /// - 检查全局投注状态（RuntimeConfiguration.CanBet）
    /// - 解析投注指令（支持多个投注）
    /// - 验证投注格式和金额
    /// - 创建投注记录
    /// - 扣减用户余额
    /// - 发送投注确认消息
    ///
    /// 投注状态检查：
    /// - 综合考虑时间、上期开奖状态、游戏服务状态
    /// - 不可投注时发送友好提醒
    ///
    /// 支持的投注格式：
    /// - 单个投注："大10"、"小20"、"单15"、"双25"
    /// - 多个投注："大10小20"、"单15双25"
    /// </summary>
    /// <param name="message">包含投注指令的内部消息</param>
    public async Task ProcessBetRequestAsync(InternalMessage message)
    {
        // 获取会员信息（此时会员应该已存在）
        var member = await memberService.GetMemberAsync(message.Account);

        // 分割指令
        string[] commands = Ai.Split(message.Content, " ");

        // 从赔率表获取支持的投注项目列表
        var supportedPlayItems = await oddsService.GetSupportedPlayItemsAsync();
        if (!supportedPlayItems.Any())
        {
            logger.LogWarning(@"未找到支持的投注项目配置，跳过投注处理，用户: {Account}", message.Account);

            return;
        }

        // 检测是否包含有效的投注指令
        bool hasValidBetCommand = false;
        var invalidCommands = new List<string>();

        foreach (string command in commands)
        {
            // 修正指令并去除前后空格
            string newCommand = ChangeCommand(command).Trim();

            // 验证投注指令
            var (isValid, _, _, errorMessage) = await betCommandValidatorService.ValidateBetCommandAsync(newCommand);
            if (isValid)
            {
                hasValidBetCommand = true;
            }
            else
            {
                invalidCommands.Add($"{command}({errorMessage})");
            }
        }

        // 如果没有有效的投注指令，记录错误并返回
        if (!hasValidBetCommand)
        {
            logger.LogWarning(@"未检测到有效投注指令，用户: {Account}, 消息: {Content}, 错误: {Errors}",
                message.Account, message.Content, string.Join("; ", invalidCommands));

            return;
        }

        // 获取当前期号
        var currentIssue = issueTimeService.GetCurrentCachedIssueTime();
        if (currentIssue == null)
        {
            logger.LogWarning(@"无法获取当前期号，跳过投注处理，用户: {Account}", message.Account);

            return;
        }

        // 检查全局投注状态 - 统一投注控制入口
        // RuntimeConfiguration.CanBet综合考虑：时间+上期开奖状态+游戏服务状态
        if (!RuntimeConfiguration.CanBet)
        {
            logger.LogWarning(@"当前不可投注（未开始答题或等待上期开奖），用户: {Account}, 期号: {Issue}", message.Account, currentIssue.Issue);

            // 发送用户友好的提醒消息
            var reminderMessage = $"{EmojiHelper.GetCrossMark()}已封卷\n{message.Content}\n无效";
            await chatService.SendGroupMessageAsync(reminderMessage, member!.Account);
            return;
        }

        // 遍历处理投注指令
        bool hasSuccessfulBet = false;
        foreach (string command in commands)
        {
            try
            {
                // 修正指令并去除前后空格
                string newCommand = ChangeCommand(command).Trim();

                // 验证投注指令
                var (isValid, playItem, amount, errorMessage) = await betCommandValidatorService.ValidateBetCommandAsync(newCommand);
                if (!isValid)
                {
                    logger.LogWarning(@"投注指令验证失败，跳过，指令: {Command}, 错误: {Error}", command, errorMessage);
                    continue;
                }

                // 获取赔率配置
                var oddsConfig = await oddsService.GetOddsByPlayItemAsync(playItem);
                if (oddsConfig == null)
                {
                    logger.LogWarning(@"投注项目配置不存在，跳过，项目: {PlayItem}", playItem);
                    continue;
                }

                // 创建投注订单
                var orderId = await betRecordService.CreateBetOrderAsync(
                    message.Account, member?.NickName ?? message.NickName, currentIssue.Issue, playItem, amount, oddsConfig.Odds, message.Content, message.Id);

                if (orderId > 0)
                {
                    hasSuccessfulBet = true;
                    logger.LogInformation(@"创建投注订单成功，用户: {Account}, 期号: {Issue}, 项目: {PlayItem}, 金额: {Amount}, 赔率: {Odds}, 订单ID: {OrderId}",
                        message.Account, currentIssue.Issue, playItem, amount, oddsConfig.Odds, orderId);
                }
                else
                {
                    logger.LogWarning(@"创建投注订单失败，用户: {Account}, 项目: {PlayItem}, 金额: {Amount}",
                        message.Account, playItem, amount);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, @"处理投注指令异常，指令: {Command}", command);
            }
        }

        // 反馈投注结果
        if (hasSuccessfulBet)
        {
            // 获取当前期的有效投注订单
            var validBetOrders = await betRecordService.GetBetOrdersByAccountAndIssueAsync(message.Account, currentIssue.Issue);

            // 如果没有有效的投注订单
            if (!validBetOrders.Any())
            {
                return;
            }

            // 按投注项目分组
            var playItemGroups = validBetOrders.GroupBy(b => b.PlayItem)
                .Select(g => new
                {
                    PlayItem = g.Key,
                    TotalAmount = g.Sum(b => b.Amount)
                })
                .OrderBy(g => g.PlayItem)
                .ToList();

            var betItems = playItemGroups.Select(g => $"{g.PlayItem}/{g.TotalAmount}");
            string msg = Ai.中括号左 + (string.Join(" ", betItems)) + Ai.中括号右;
            await chatService.SendGroupMessageAsync(EmojiHelper.GetCheckMark() + "成功\n" + msg, member!.Account);
            logger.LogInformation(@"投注消息处理完成，用户: {Account}, 消息ID: {MessageId}", message.Account, message.Id);
        }
        else
        {
            logger.LogWarning(@"投注消息处理失败，没有成功创建任何订单，用户: {Account}, 消息ID: {MessageId}", message.Account, message.Id);
        }
    }

    #region 修正答题指令

    /// <summary>
    /// 修正投注指令格式 - 标准化用户输入
    ///
    /// 功能：
    /// - 将各种分隔符统一为"/"
    /// - 支持中英文标点符号
    /// - 标准化投注指令格式
    ///
    /// 转换规则：
    /// - "." → "/"
    /// - "。" → "/"
    /// - "，" → "/"
    /// - "," → "/"
    ///
    /// 示例：
    /// - "大10.小20" → "大10/小20"
    /// - "单15，双25" → "单15/双25"
    /// </summary>
    /// <param name="command">原始投注指令</param>
    /// <returns>标准化后的投注指令</returns>
    private string ChangeCommand(string command)
    {
        command = command.Replace(".", "/");
        command = command.Replace("。", "/");
        command = command.Replace("，", "/");
        command = command.Replace(",", "/");
        command = command.Replace("?", "/");
        command = command.Replace("=", "/");
        command = command.Replace("+", "/");
        command = command.Replace("·", "/");
        command = command.Replace("！", "/");
        command = command.Replace("!", "/");
        command = command.Replace("@", "/");
        command = command.Replace("#", "/");
        command = command.Replace("*", "/");
        command = command.Replace("、", "/");
        command = command.Replace("／", "/");
        command = command.Replace("正", "正/");
        command = command.Replace("堂", "正/");
        command = command.Replace("糖", "正/");
        command = command.Replace("中", "正/");
        command = command.Replace("番", "番/");
        command = command.Replace("高", "番/");
        command = command.Replace("牛", "番/");
        command = command.Replace("风", "番/");
        command = command.Replace("方", "番/");
        command = command.Replace("翻", "番/");
        command = command.Replace(":", "番/");
        command = command.Replace("角", "角/");
        command = command.Replace("粘", "念");
        command = command.Replace("贴", "念");
        command = command.Replace("严", "念");
        command = command.Replace("那", "念");
        command = command.Replace("拿", "念");
        command = command.Replace("拖", "念");
        command = command.Replace("-", "念");
        if (command.Contains("念") && command.Length > 4)
        {
            command = command.Substring(0, 3) + "/" + command.Substring(3, command.Length - 3);
        }

        command = command.Replace("单", "单/");
        command = command.Replace("双", "双/");
        command = command.Replace("大", "大/");
        command = command.Replace("小", "小/");
        command = command.Replace("//", "/");

        if (Ai.GetTextLeft(command, "/") == "1") command = command.Replace("1/", "1正/");
        if (Ai.GetTextLeft(command, "/") == "2") command = command.Replace("2/", "2正/");
        if (Ai.GetTextLeft(command, "/") == "3") command = command.Replace("3/", "3正/");
        if (Ai.GetTextLeft(command, "/") == "4") command = command.Replace("4/", "4正/");
        if (Ai.GetTextLeft(command, "/") == "12") command = command.Replace("12/", "12角/");
        if (Ai.GetTextLeft(command, "/") == "21") command = command.Replace("21/", "12角/");
        if (Ai.GetTextLeft(command, "/") == "23") command = command.Replace("23/", "23角/");
        if (Ai.GetTextLeft(command, "/") == "32") command = command.Replace("32/", "23角/");
        if (Ai.GetTextLeft(command, "/") == "34") command = command.Replace("34/", "34角/");
        if (Ai.GetTextLeft(command, "/") == "43") command = command.Replace("43/", "34角/");
        if (Ai.GetTextLeft(command, "/") == "14") command = command.Replace("14/", "14角/");
        if (Ai.GetTextLeft(command, "/") == "41") command = command.Replace("41/", "14角/");
        if (Ai.GetTextLeft(command, "/") == "31") command = command.Replace("31/", "单/");
        if (Ai.GetTextLeft(command, "/") == "13") command = command.Replace("13/", "单/");
        if (Ai.GetTextLeft(command, "/") == "24") command = command.Replace("24/", "双/");
        if (Ai.GetTextLeft(command, "/") == "42") command = command.Replace("42/", "双/");
        if (Ai.GetTextLeft(command, "/") == "1122") command = command.Replace("1122/", "小/");
        if (Ai.GetTextLeft(command, "/") == "2211") command = command.Replace("2211/", "小/");
        if (Ai.GetTextLeft(command, "/") == "3344") command = command.Replace("3344/", "大/");
        if (Ai.GetTextLeft(command, "/") == "4433") command = command.Replace("4433/", "大/");
        if (Ai.GetTextLeft(command, "/") == "123") command = command.Replace("123/", "三门123/");
        if (Ai.GetTextLeft(command, "/") == "124") command = command.Replace("124/", "三门124/");
        if (Ai.GetTextLeft(command, "/") == "134") command = command.Replace("134/", "三门134/");
        if (Ai.GetTextLeft(command, "/") == "234") command = command.Replace("234/", "三门234/");
        if (Ai.GetTextLeft(command, "/") == "152") command = command.Replace("152/", "1无2/");
        if (Ai.GetTextLeft(command, "/") == "153") command = command.Replace("153/", "1无3/");
        if (Ai.GetTextLeft(command, "/") == "154") command = command.Replace("154/", "1无4/");
        if (Ai.GetTextLeft(command, "/") == "251") command = command.Replace("251/", "2无1/");
        if (Ai.GetTextLeft(command, "/") == "253") command = command.Replace("253/", "2无3/");
        if (Ai.GetTextLeft(command, "/") == "254") command = command.Replace("254/", "2无4/");
        if (Ai.GetTextLeft(command, "/") == "351") command = command.Replace("351/", "3无1/");
        if (Ai.GetTextLeft(command, "/") == "352") command = command.Replace("352/", "3无2/");
        if (Ai.GetTextLeft(command, "/") == "354") command = command.Replace("354/", "3无4/");
        if (Ai.GetTextLeft(command, "/") == "451") command = command.Replace("451/", "4无1/");
        if (Ai.GetTextLeft(command, "/") == "452") command = command.Replace("452/", "4无2/");
        if (Ai.GetTextLeft(command, "/") == "453") command = command.Replace("453/", "4无3/");

        return command;
    }

    #endregion

    #region 统计信息

    /// <summary>
    /// 获取订单统计信息 - 今日投注数据统计
    ///
    /// 功能：
    /// - 统计今日投注订单总数
    /// - 统计今日投注总金额
    /// - 统计今日参与投注的用户数
    /// - 返回完整的统计信息对象
    ///
    /// 统计范围：当日00:00:00到23:59:59的所有投注记录
    /// 用途：用于显示实时的投注统计数据
    /// </summary>
    public async Task<OrderStatistics> GetOrderStatisticsAsync()
    {
        try
        {
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);

            var pendingDepositCount = await fSql.Select<DepositRequest>()
                .Where(r => r.Status == EnumDepositStatus.Pending)
                .CountAsync()
                .ConfigureAwait(false);

            var pendingWithdrawCount = await fSql.Select<WithdrawRequest>()
                .Where(r => r.Status == EnumWithdrawStatus.Pending)
                .CountAsync()
                .ConfigureAwait(false);

            var todayDepositAmount = await fSql.Select<DepositRequest>()
                .Where(r => r.Status == EnumDepositStatus.Approved && r.ReviewTime >= today && r.ReviewTime < tomorrow)
                .SumAsync(r => r.Amount)
                .ConfigureAwait(false);

            var todayWithdrawAmount = await fSql.Select<WithdrawRequest>()
                .Where(r => r.Status == EnumWithdrawStatus.Approved && r.ReviewTime >= today && r.ReviewTime < tomorrow)
                .SumAsync(r => r.Amount)
                .ConfigureAwait(false);

            var todayProcessedCount = await fSql.Select<DepositRequest>()
                                          .Where(r => r.ReviewTime >= today && r.ReviewTime < tomorrow)
                                          .CountAsync()
                                          .ConfigureAwait(false) +
                                      await fSql.Select<WithdrawRequest>()
                                          .Where(r => r.ReviewTime >= today && r.ReviewTime < tomorrow)
                                          .CountAsync()
                                          .ConfigureAwait(false);

            // 投注订单统计
            var pendingBetOrderCount = await fSql.Select<BetOrder>()
                .Where(o => o.Status == EnumBetOrderStatus.Confirmed)
                .CountAsync()
                .ConfigureAwait(false);

            var todayBetAmount = await fSql.Select<BetOrder>()
                .Where(o => o.CreatedTime >= today && o.CreatedTime < tomorrow &&
                            o.Status != EnumBetOrderStatus.Cancelled)
                .SumAsync(o => o.Amount)
                .ConfigureAwait(false);

            var todayWinAmount = await fSql.Select<BetOrder>()
                .Where(o => o.SettledTime >= today && o.SettledTime < tomorrow &&
                            (o.Status == EnumBetOrderStatus.Win || o.Status == EnumBetOrderStatus.Draw) && o.ActualWinAmount > 0)
                .SumAsync(o => o.ActualWinAmount ?? 0)
                .ConfigureAwait(false);

            return new OrderStatistics
            {
                PendingDepositCount = (int)pendingDepositCount,
                PendingWithdrawCount = (int)pendingWithdrawCount,
                TodayDepositAmount = todayDepositAmount,
                TodayWithdrawAmount = todayWithdrawAmount,
                TodayProcessedCount = (int)todayProcessedCount,
                PendingBetOrderCount = (int)pendingBetOrderCount,
                TodayBetAmount = todayBetAmount,
                TodayWinAmount = todayWinAmount
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取订单统计信息失败");
            return new OrderStatistics();
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 计算投注订单的盈亏金额
    /// </summary>
    /// <param name="betOrder">投注订单</param>
    /// <returns>盈亏金额（正数为盈利，负数为亏损）</returns>
    private static decimal CalculateProfitLoss(BetOrder betOrder)
    {
        return betOrder.Status switch
        {
            EnumBetOrderStatus.Win => (betOrder.ActualWinAmount ?? 0) - betOrder.Amount,
            EnumBetOrderStatus.Lose => (betOrder.ActualWinAmount ?? 0) - betOrder.Amount, // 通常为 0 - Amount = -Amount
            EnumBetOrderStatus.Draw => 0, // 和局不计盈亏
            _ => 0 // 未结算的订单不计算盈亏
        };
    }

    #endregion

    #region 指令处理方法

    /// <summary>
    /// 指令说明处理器
    /// </summary>
    private async Task 指令说明Handler(Member member)
    {
        var helpMessage = @"📋 指令说明：
0 - 指令说明
1/查/查分 - 查询余额
2/图/路 - 发送路子图
3 - 发送开奖图
4/回/回水/返水/反水 - 回水处理
5/水/流水 - 流水详情
6/时间/封/开 - 查询时间
7/撤/撤销/取消 - 撤销投注
8 - 发送摊图
9 - 发送结算报告
上100/上分100/起100/起分100 - 上分
下100/下分100/落100/落分100 - 下分
大10/小20/单15/双25 - 投注";

        await chatService.SendGroupMessageAsync(helpMessage, member.Account);
        logger.LogInformation(@"发送指令说明给用户: {Account}", member.Account);
    }

    /// <summary>
    /// 查分指令处理器
    /// </summary>
    private async Task 查分指令Handler(Member member)
    {
        var balance = member.Balance;
        var message = $@"💰 余额查询
当前余额: {balance:F2}";

        await chatService.SendGroupMessageAsync(message, member.Account);
        logger.LogInformation(@"发送余额查询结果给用户: {Account}, 余额: {Balance}", member.Account, balance);
    }

    /// <summary>
    /// 发送路子图处理器
    /// </summary>
    private async Task 发送路子图Handler()
    {
        try
        {
            logger.LogInformation(@"发送路子图指令被调用");

            // 检查路子图文件是否存在
            var tan7Path = ImageConstants.TanRows7ImagePath;
            var tan6Path = ImageConstants.TanRows6ImagePath;

            if (File.Exists(tan7Path))
            {
                await chatService.SendImageAsync(tan7Path);
                logger.LogInformation(@"发送7行路子图成功: {ImagePath}", tan7Path);
            }
            else if (File.Exists(tan6Path))
            {
                await chatService.SendImageAsync(tan6Path);
                logger.LogInformation(@"发送6行路子图成功: {ImagePath}", tan6Path);
            }
            else
            {
                await chatService.SendGroupMessageAsync("📊 路子图暂未生成，请稍后再试");
                logger.LogWarning(@"路子图文件不存在: {Path7}, {Path6}", tan7Path, tan6Path);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"发送路子图时发生异常");
            await chatService.SendGroupMessageAsync("📊 发送路子图失败，请稍后再试");
        }
    }

    /// <summary>
    /// 发送开奖图处理器
    /// </summary>
    private async Task 发送开奖图Handler()
    {
        try
        {
            logger.LogInformation(@"发送开奖图指令被调用");

            // 检查开奖图文件是否存在
            var drawImagePath = ImageConstants.DrawImagePath;

            if (File.Exists(drawImagePath))
            {
                await chatService.SendImageAsync(drawImagePath);
                logger.LogInformation(@"发送开奖图成功: {ImagePath}", drawImagePath);
            }
            else
            {
                await chatService.SendGroupMessageAsync("🎯 开奖图暂未生成，请稍后再试");
                logger.LogWarning(@"开奖图文件不存在: {ImagePath}", drawImagePath);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"发送开奖图时发生异常");
            await chatService.SendGroupMessageAsync("🎯 发送开奖图失败，请稍后再试");
        }
    }

    /// <summary>
    /// 回水处理器
    /// </summary>
    private async Task RebateHandler(Member member, InternalMessage message)
    {
        try
        {
            logger.LogInformation(@"回水指令被调用，用户: {Account}", member.Account);

            // 获取今日的回水记录
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);

            // 查询用户今日的输赢流水回水记录
            var winLossRebateRecords = await fSql.Select<WinLossRebateRecordViewModel>()
                .Where(r => r.Account == member.Account)
                .ToListAsync();

            // 计算今日流水和回水
            var todayBetOrders = await fSql.Select<BetOrder>()
                .Where(o => o.Account == member.Account &&
                           o.CreatedTime >= today &&
                           o.CreatedTime < tomorrow &&
                           o.Status != EnumBetOrderStatus.Cancelled)
                .ToListAsync();

            var todayTurnover = todayBetOrders.Sum(o => o.Amount);
            var todayProfitLoss = todayBetOrders.Sum(o => CalculateProfitLoss(o));

            // 计算应得回水
            var rebatePercent = member.RebatePercent;
            var expectedRebate = todayTurnover * (rebatePercent / 100);

            // 查询已发放的回水（这里简化处理，实际应该从回水记录表查询）
            var paidRebate = 0m; // TODO: 从回水记录表查询已发放的回水
            var pendingRebate = expectedRebate - paidRebate;

            var rebateMessage = $@"💧 回水查询
今日流水: {todayTurnover:F2}
今日盈亏: {todayProfitLoss:F2}
回水比例: {rebatePercent:F1}%
应得回水: {expectedRebate:F2}
已发回水: {paidRebate:F2}
待发回水: {pendingRebate:F2}";

            await chatService.SendGroupMessageAsync(rebateMessage, member.Account);
            logger.LogInformation(@"发送回水查询结果给用户: {Account}, 今日流水: {Turnover}, 应得回水: {Rebate}",
                member.Account, todayTurnover, expectedRebate);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理回水查询时发生异常，用户: {Account}", member.Account);
            await chatService.SendGroupMessageAsync("💧 回水查询失败，请稍后再试", member.Account);
        }
    }

    /// <summary>
    /// 流水详情处理器
    /// </summary>
    private async Task 流水详情Handler(Member member)
    {
        try
        {
            logger.LogInformation(@"流水详情指令被调用，用户: {Account}", member.Account);

            // 获取今日的投注记录
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);

            var todayBetOrders = await fSql.Select<BetOrder>()
                .Where(o => o.Account == member.Account &&
                           o.CreatedTime >= today &&
                           o.CreatedTime < tomorrow)
                .OrderByDescending(o => o.CreatedTime)
                .ToListAsync();

            if (!todayBetOrders.Any())
            {
                await chatService.SendGroupMessageAsync("📈 今日暂无投注记录", member.Account);
                return;
            }

            // 统计数据
            var totalBetAmount = todayBetOrders.Where(o => o.Status != EnumBetOrderStatus.Cancelled).Sum(o => o.Amount);
            var totalWinAmount = todayBetOrders.Where(o => o.Status == EnumBetOrderStatus.Win).Sum(o => o.ActualWinAmount ?? 0);
            var totalProfitLoss = todayBetOrders.Where(o => o.Status != EnumBetOrderStatus.Cancelled).Sum(o => CalculateProfitLoss(o));
            var winCount = todayBetOrders.Count(o => o.Status == EnumBetOrderStatus.Win);
            var loseCount = todayBetOrders.Count(o => o.Status == EnumBetOrderStatus.Lose);
            var pendingCount = todayBetOrders.Count(o => o.Status == EnumBetOrderStatus.Confirmed);
            var cancelledCount = todayBetOrders.Count(o => o.Status == EnumBetOrderStatus.Cancelled);

            var flowMessage = $@"📈 今日流水详情
投注总额: {totalBetAmount:F2}
中奖金额: {totalWinAmount:F2}
盈亏总计: {totalProfitLoss:F2}
中奖次数: {winCount}
失败次数: {loseCount}
待开奖: {pendingCount}
已撤销: {cancelledCount}
总投注: {todayBetOrders.Count}";

            await chatService.SendGroupMessageAsync(flowMessage, member.Account);
            logger.LogInformation(@"发送流水详情给用户: {Account}, 今日投注: {BetCount}, 投注总额: {TotalAmount}",
                member.Account, todayBetOrders.Count, totalBetAmount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理流水详情查询时发生异常，用户: {Account}", member.Account);
            await chatService.SendGroupMessageAsync("📈 流水详情查询失败，请稍后再试", member.Account);
        }
    }

    /// <summary>
    /// 查询时间指令处理器
    /// </summary>
    private async Task 查询时间指令Handler(Member member)
    {
        try
        {
            logger.LogInformation(@"查询时间指令被调用，用户: {Account}", member.Account);

            // 获取当前期号信息
            var currentIssueTime = issueTimeService.GetCurrentCachedIssueTime();
            var currentTime = DateTime.Now;

            if (currentIssueTime == null)
            {
                var message = $@"⏰ 当前时间: {currentTime:yyyy-MM-dd HH:mm:ss}
❌ 期号信息暂未加载";
                await chatService.SendGroupMessageAsync(message, member.Account);
                return;
            }

            // 获取开盘和封盘倒计时
            var openTimeSpan = issueTimeService.GetCurrentCachedOpenTimeSpan();
            var closeTimeSpan = issueTimeService.GetCurrentCachedCloseTimeSpan();

            // 判断当前状态
            string status;
            string timeInfo;

            if (openTimeSpan > 0)
            {
                status = "🔒 封盘中";
                timeInfo = $"开盘倒计时: {TimeHelper.FormatTimeRemaining(openTimeSpan)}";
            }
            else if (closeTimeSpan > 0)
            {
                status = "🟢 开盘中";
                timeInfo = $"封盘倒计时: {TimeHelper.FormatTimeRemaining(closeTimeSpan)}";
            }
            else
            {
                status = "🔒 封盘中";
                timeInfo = "等待下期开盘";
            }

            var timeMessage = $@"⏰ 时间查询
当前时间: {currentTime:yyyy-MM-dd HH:mm:ss}
当前期号: {currentIssueTime.Issue}
当前状态: {status}
{timeInfo}";

            await chatService.SendGroupMessageAsync(timeMessage, member.Account);
            logger.LogInformation(@"发送时间查询结果给用户: {Account}, 期号: {Issue}, 状态: {Status}",
                member.Account, currentIssueTime.Issue, status);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理时间查询时发生异常，用户: {Account}", member.Account);
            await chatService.SendGroupMessageAsync("⏰ 时间查询失败，请稍后再试", member.Account);
        }
    }

    /// <summary>
    /// 撤销投注处理器
    /// </summary>
    private async Task CancelBetDataHandler(Member member, InternalMessage message)
    {
        try
        {
            logger.LogInformation(@"撤销投注指令被调用，用户: {Account}", member.Account);

            // 获取当前期号
            var currentIssueTime = issueTimeService.GetCurrentCachedIssueTime();
            if (currentIssueTime == null)
            {
                await chatService.SendGroupMessageAsync("❌ 当前期号信息不可用，无法撤销投注", member.Account);
                return;
            }

            // 查询当前期该用户的未结算投注订单
            var pendingBetOrders = await fSql.Select<BetOrder>()
                .Where(o => o.Account == member.Account &&
                           o.Issue == currentIssueTime.Issue &&
                           o.Status == EnumBetOrderStatus.Confirmed)
                .ToListAsync();

            if (!pendingBetOrders.Any())
            {
                await chatService.SendGroupMessageAsync("❌ 当前期暂无可撤销的投注", member.Account);
                return;
            }

            // 检查是否还在投注时间内（可以撤销）
            var now = DateTime.Now;
            if (now >= currentIssueTime.CloseTime)
            {
                await chatService.SendGroupMessageAsync("❌ 已封盘，无法撤销投注", member.Account);
                return;
            }

            // 批量撤销所有未结算的投注
            int cancelledCount = 0;
            decimal refundAmount = 0;

            foreach (var betOrder in pendingBetOrders)
            {
                var success = await betRecordService.CancelBetOrderAsync(betOrder.Id, "用户撤销", "用户主动撤销投注");
                if (success)
                {
                    cancelledCount++;
                    refundAmount += betOrder.Amount;
                }
            }

            if (cancelledCount > 0)
            {
                var cancelMessage = $@"✅ 撤销成功
撤销订单: {cancelledCount} 个
退还金额: {refundAmount:F2}";

                await chatService.SendGroupMessageAsync(cancelMessage, member.Account);
                logger.LogInformation(@"用户撤销投注成功，用户: {Account}, 期号: {Issue}, 撤销数量: {Count}, 退还金额: {Amount}",
                    member.Account, currentIssueTime.Issue, cancelledCount, refundAmount);
            }
            else
            {
                await chatService.SendGroupMessageAsync("❌ 撤销失败，请稍后再试", member.Account);
                logger.LogWarning(@"用户撤销投注失败，用户: {Account}, 期号: {Issue}", member.Account, currentIssueTime.Issue);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理撤销投注时发生异常，用户: {Account}", member.Account);
            await chatService.SendGroupMessageAsync("❌ 撤销投注失败，请稍后再试", member.Account);
        }
    }

    /// <summary>
    /// 发送摊图处理器
    /// </summary>
    private async Task SendTanImageFullHandler()
    {
        try
        {
            logger.LogInformation(@"发送摊图指令被调用");

            // 检查完整摊图文件是否存在
            var tan77Path = ImageConstants.TanRows77ImagePath;
            var tan66Path = ImageConstants.TanRows66ImagePath;

            if (File.Exists(tan77Path))
            {
                await chatService.SendImageAsync(tan77Path);
                logger.LogInformation(@"发送7行完整摊图成功: {ImagePath}", tan77Path);
            }
            else if (File.Exists(tan66Path))
            {
                await chatService.SendImageAsync(tan66Path);
                logger.LogInformation(@"发送6行完整摊图成功: {ImagePath}", tan66Path);
            }
            else
            {
                await chatService.SendGroupMessageAsync("🎲 完整摊图暂未生成，请稍后再试");
                logger.LogWarning(@"完整摊图文件不存在: {Path77}, {Path66}", tan77Path, tan66Path);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"发送完整摊图时发生异常");
            await chatService.SendGroupMessageAsync("🎲 发送完整摊图失败，请稍后再试");
        }
    }

    /// <summary>
    /// 发送结算报告处理器
    /// </summary>
    private async Task SendSettleReportHandler()
    {
        try
        {
            logger.LogInformation(@"发送结算报告指令被调用");

            // 获取今日的投注统计数据
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);

            // 查询今日所有投注订单
            var todayBetOrders = await fSql.Select<BetOrder>()
                .Where(o => o.CreatedTime >= today && o.CreatedTime < tomorrow)
                .ToListAsync();

            if (!todayBetOrders.Any())
            {
                await chatService.SendGroupMessageAsync("📊 今日暂无投注数据");
                return;
            }

            // 统计数据
            var totalBetAmount = todayBetOrders.Where(o => o.Status != EnumBetOrderStatus.Cancelled).Sum(o => o.Amount);
            var totalWinAmount = todayBetOrders.Where(o => o.Status == EnumBetOrderStatus.Win).Sum(o => o.ActualWinAmount ?? 0);
            var totalProfitLoss = todayBetOrders.Where(o => o.Status != EnumBetOrderStatus.Cancelled).Sum(o => CalculateProfitLoss(o));
            var winCount = todayBetOrders.Count(o => o.Status == EnumBetOrderStatus.Win);
            var loseCount = todayBetOrders.Count(o => o.Status == EnumBetOrderStatus.Lose);
            var pendingCount = todayBetOrders.Count(o => o.Status == EnumBetOrderStatus.Confirmed);
            var cancelledCount = todayBetOrders.Count(o => o.Status == EnumBetOrderStatus.Cancelled);
            var uniqueUsers = todayBetOrders.Select(o => o.Account).Distinct().Count();

            // 计算胜率
            var totalSettled = winCount + loseCount;
            var winRate = totalSettled > 0 ? (decimal)winCount / totalSettled * 100 : 0;

            var reportMessage = $@"📊 今日结算报告
━━━━━━━━━━━━━━━━
💰 投注总额: {totalBetAmount:F2}
🎯 中奖金额: {totalWinAmount:F2}
📈 盈亏总计: {totalProfitLoss:F2}
━━━━━━━━━━━━━━━━
✅ 中奖次数: {winCount}
❌ 失败次数: {loseCount}
⏳ 待开奖: {pendingCount}
🚫 已撤销: {cancelledCount}
━━━━━━━━━━━━━━━━
📊 总投注: {todayBetOrders.Count}
👥 参与人数: {uniqueUsers}
🎯 胜率: {winRate:F1}%
━━━━━━━━━━━━━━━━
📅 统计时间: {today:yyyy-MM-dd}";

            await chatService.SendGroupMessageAsync(reportMessage);
            logger.LogInformation(@"发送今日结算报告成功，投注总额: {TotalAmount}, 参与人数: {UserCount}",
                totalBetAmount, uniqueUsers);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"发送结算报告时发生异常");
            await chatService.SendGroupMessageAsync("📊 结算报告生成失败，请稍后再试");
        }
    }

    /// <summary>
    /// 上分指令处理器
    /// </summary>
    private async Task 上分指令Handler(Member member, InternalMessage message)
    {
        await ProcessDepositRequestAsync(message);
    }

    /// <summary>
    /// 下分指令处理器
    /// </summary>
    private async Task 下分指令Handler(Member member, InternalMessage message)
    {
        await ProcessWithdrawRequestAsync(message);
    }

    /// <summary>
    /// 下注指令处理器
    /// </summary>
    private async Task 下注指令Handler(Member member, InternalMessage message)
    {
        await ProcessBetRequestAsync(message);
    }

    #endregion
}