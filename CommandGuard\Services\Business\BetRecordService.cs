using AiHelper;
using CommandGuard.Enums;
using CommandGuard.Interfaces.Business;
using CommandGuard.Models;
using CommandGuard.ViewModels;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Business;

/// <summary>
/// 投注记录服务实现
/// 提供投注记录的查询和统计功能
/// </summary>
public class BetRecordService(
    ILogger<BetRecordService> logger,
    IFreeSql fSql,
    IFinancialService financialService,
    IMemberService memberService) : IBetRecordService
{
    #region 投注订单相关

    /// <summary>
    /// 创建投注订单 - 处理用户投注请求
    ///
    /// 功能：
    /// - 验证投注参数的有效性
    /// - 检查用户余额是否充足
    /// - 创建投注订单记录
    /// - 扣减用户余额
    /// - 记录财务流水
    /// - 支持事务回滚
    ///
    /// 业务流程：
    /// 1. 参数验证（金额、期号、玩法等）
    /// 2. 用户余额检查
    /// 3. 创建订单记录
    /// 4. 扣减用户余额
    /// 5. 记录财务流水
    /// 6. 返回订单ID
    /// </summary>
    public async Task<long> CreateBetOrderAsync(string account, string nickName, string issue, string playItem, decimal amount, decimal odds, string originalMessage, string messageId)
    {
        // 参数验证
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException(@"用户账号不能为空", nameof(account));
        if (string.IsNullOrWhiteSpace(issue))
            throw new ArgumentException(@"投注期号不能为空", nameof(issue));
        if (string.IsNullOrWhiteSpace(playItem))
            throw new ArgumentException(@"投注项目不能为空", nameof(playItem));
        if (amount <= 0)
            throw new ArgumentException(@"投注金额必须大于0", nameof(amount));
        if (odds <= 0)
            throw new ArgumentException(@"赔率必须大于0", nameof(odds));

        try
        {
            // 获取用户信息（包含回水比例）
            var member = await memberService.GetMemberAsync(account).ConfigureAwait(false);
            if (member == null)
            {
                logger.LogWarning(@"用户不存在，无法创建投注订单，用户: {Account}", account);
                return -1;
            }

            // 获取用户当前余额
            var currentBalance = await financialService.GetCurrentBalanceAsync(account).ConfigureAwait(false);

            // 检查余额是否足够
            if (currentBalance < amount)
            {
                logger.LogWarning(@"用户余额不足，无法创建投注订单，用户: {Account}, 余额: {Balance}, 投注金额: {Amount}",
                    account, currentBalance, amount);
                return -1;
            }

            // 扣除投注金额
            var deductSuccess = await financialService.DecreaseBalanceAsync(
                    account, amount, @"BetOrder", 0, // 临时使用0，后面会更新
                    $@"投注扣款，项目: {playItem}, 金额: {amount}", @"System", @"投注订单")
                .ConfigureAwait(false);

            if (!deductSuccess)
            {
                logger.LogError(@"扣除投注金额失败，用户: {Account}, 金额: {Amount}", account, amount);
                return -1;
            }

            // 创建投注订单
            var betOrder = new BetOrder
            {
                OrderNumber = Ai.GetRandomPassword(9),
                NickName = nickName.Trim(),
                Account = account.Trim(),
                Issue = issue.Trim(),
                PlayItem = playItem.Trim(),
                Amount = amount,
                Odds = odds,
                OriginalMessage = originalMessage.Trim(),
                MessageId = messageId,
                Status = EnumBetOrderStatus.Confirmed, // 直接确认
                BalanceBeforeBet = currentBalance,
                BalanceAfterBet = currentBalance - amount,
                CreatedTime = DateTime.Now,
                ConfirmedTime = DateTime.Now,
                Processor = @"System"
            };

            // 计算回水金额（中奖金额在结算时设置）
            betOrder.CalculateRebateAmount(member.RebatePercent);

            // 插入数据库
            var orderId = await fSql.Insert<BetOrder>()
                .AppendData(betOrder)
                .ExecuteIdentityAsync()
                .ConfigureAwait(false);

            logger.LogInformation(@"创建投注订单成功，用户: {Account}, 期号: {Issue}, 项目: {PlayItem}, 金额: {Amount}, 订单ID: {OrderId}",
                account, issue, playItem, amount, orderId);

            return orderId;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"创建投注订单失败，用户: {Account}, 期号: {Issue}, 项目: {PlayItem}, 金额: {Amount}",
                account, issue, playItem, amount);
            throw;
        }
    }

    /// <summary>
    /// 确认投注订单
    /// </summary>
    public async Task<bool> ConfirmBetOrderAsync(long orderId, string processor, string processNote = "")
    {
        try
        {
            var updateResult = await fSql.Update<BetOrder>()
                .Set(o => o.Status, EnumBetOrderStatus.Confirmed)
                .Set(o => o.ConfirmedTime, DateTime.Now)
                .Set(o => o.Processor, processor)
                .Set(o => o.ProcessNote, processNote)
                .Where(o => o.Id == orderId && o.Status == EnumBetOrderStatus.Confirmed)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            if (updateResult > 0)
            {
                logger.LogInformation(@"确认投注订单成功，订单ID: {OrderId}, 处理人: {Processor}", orderId, processor);
                return true;
            }

            logger.LogWarning(@"确认投注订单失败，订单不存在或状态不正确，订单ID: {OrderId}", orderId);
            return false;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"确认投注订单失败，订单ID: {OrderId}", orderId);
            return false;
        }
    }

    /// <summary>
    /// 取消投注订单
    /// </summary>
    public async Task<bool> CancelBetOrderAsync(long orderId, string processor, string processNote = "")
    {
        try
        {
            // 获取订单信息
            var order = await fSql.Select<BetOrder>()
                .Where(o => o.Id == orderId)
                .ToOneAsync()
                .ConfigureAwait(false);

            if (order == null)
            {
                logger.LogWarning(@"取消投注订单失败，订单不存在，订单ID: {OrderId}", orderId);
                return false;
            }

            if (order.Status != EnumBetOrderStatus.Confirmed)
            {
                logger.LogWarning(@"取消投注订单失败，订单状态不允许取消，订单ID: {OrderId}, 状态: {Status}",
                    orderId, order.Status);
                return false;
            }

            // 更新订单状态
            var updateResult = await fSql.Update<BetOrder>()
                .Set(o => o.Status, EnumBetOrderStatus.Cancelled)
                .Set(o => o.SettledTime, DateTime.Now)
                .Set(o => o.Processor, processor)
                .Set(o => o.ProcessNote, processNote)
                .Where(o => o.Id == orderId)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            if (updateResult > 0)
            {
                // 退还投注金额
                var refundSuccess = await financialService.IncreaseBalanceAsync(
                        order.Account, order.Amount, @"BetOrder", orderId,
                        $@"投注订单取消退款，项目: {order.PlayItem}, 金额: {order.Amount}", processor, @"投注订单取消")
                    .ConfigureAwait(false);

                if (refundSuccess)
                {
                    logger.LogInformation(@"取消投注订单成功，订单ID: {OrderId}, 处理人: {Processor}", orderId, processor);
                    return true;
                }

                logger.LogError(@"取消投注订单退款失败，订单ID: {OrderId}", orderId);
            }

            return false;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"取消投注订单失败，订单ID: {OrderId}", orderId);
            return false;
        }
    }

    /// <summary>
    /// 拒绝投注订单
    /// </summary>
    public async Task<bool> RejectBetOrderAsync(long orderId, string processor, string processNote = "")
    {
        try
        {
            // 获取订单信息
            var order = await fSql.Select<BetOrder>()
                .Where(o => o.Id == orderId && o.Status == EnumBetOrderStatus.Confirmed)
                .ToOneAsync()
                .ConfigureAwait(false);

            if (order == null)
            {
                logger.LogWarning(@"拒绝投注订单失败，订单不存在或状态不正确，订单ID: {OrderId}", orderId);
                return false;
            }

            // 更新订单状态
            var updateResult = await fSql.Update<BetOrder>()
                .Set(o => o.Status, EnumBetOrderStatus.Cancelled)
                .Set(o => o.SettledTime, DateTime.Now)
                .Set(o => o.Processor, processor)
                .Set(o => o.ProcessNote, processNote)
                .Where(o => o.Id == orderId)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            if (updateResult > 0)
            {
                // 退还投注金额
                var refundSuccess = await financialService.IncreaseBalanceAsync(
                        order.Account, order.Amount, @"BetOrder", orderId,
                        $@"投注订单被拒绝退款，项目: {order.PlayItem}, 金额: {order.Amount}", processor, @"投注订单拒绝")
                    .ConfigureAwait(false);

                if (refundSuccess)
                {
                    logger.LogInformation(@"拒绝投注订单成功，订单ID: {OrderId}, 处理人: {Processor}", orderId, processor);
                    return true;
                }

                logger.LogError(@"拒绝投注订单退款失败，订单ID: {OrderId}", orderId);
            }

            return false;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"拒绝投注订单失败，订单ID: {OrderId}", orderId);
            return false;
        }
    }

    /// <summary>
    /// 结算投注订单 - 根据开奖结果处理订单
    ///
    /// 功能：
    /// - 根据开奖结果判断输赢
    /// - 计算中奖金额
    /// - 更新订单状态
    /// - 更新用户余额（中奖时增加）
    /// - 记录财务流水
    /// - 支持事务回滚
    ///
    /// 结算类型：
    /// - 中奖：增加用户余额，记录中奖金额
    /// - 未中奖：订单状态更新为失败
    /// - 异常：保持待结算状态
    ///
    /// 业务流程：
    /// 1. 验证订单状态
    /// 2. 计算结算结果
    /// 3. 更新订单信息
    /// 4. 处理用户余额
    /// 5. 记录财务流水
    /// </summary>
    public async Task<bool> SettleBetOrderAsync(long orderId, EnumBetOrderStatus settlementResult, decimal winAmount, string drawResult, string processor)
    {
        try
        {
            // 获取订单信息
            var order = await fSql.Select<BetOrder>()
                .Where(o => o.Id == orderId && o.Status == EnumBetOrderStatus.Confirmed)
                .ToOneAsync()
                .ConfigureAwait(false);

            if (order == null)
            {
                logger.LogWarning(@"结算投注订单失败，订单不存在或状态不正确，订单ID: {OrderId}", orderId);
                return false;
            }

            // 更新订单结算信息
            switch (settlementResult)
            {
                case EnumBetOrderStatus.Win:
                    order.SetWinInfo(winAmount, drawResult);
                    break;
                case EnumBetOrderStatus.Lose:
                    order.SetLostInfo(drawResult);
                    break;
                case EnumBetOrderStatus.Draw:
                    order.SetDrawInfo(drawResult);
                    break;
                default:
                    logger.LogWarning(@"无效的结算结果，订单ID: {OrderId}, 结算结果: {SettlementResult}", orderId, settlementResult);
                    return false;
            }

            // 更新订单状态
            var updateResult = await fSql.Update<BetOrder>()
                .SetSource(order)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            if (updateResult <= 0)
            {
                logger.LogWarning(@"更新投注订单失败，订单ID: {OrderId}", orderId);
                return false;
            }

            // 如果中奖或和局，更新会员余额
            if ((settlementResult == EnumBetOrderStatus.Win || settlementResult == EnumBetOrderStatus.Draw) && winAmount > 0)
            {
                var description = settlementResult == EnumBetOrderStatus.Win ? @"投注中奖" : @"投注和局退款";
                var balanceUpdateSuccess = await financialService.IncreaseBalanceAsync(
                    order.Account, winAmount, @"BetOrder", orderId,
                    @$"{description}-订单{orderId}", processor, description);

                if (!balanceUpdateSuccess)
                {
                    logger.LogError(@"更新会员余额失败，订单ID: {OrderId}, 账号: {Account}, 金额: {Amount}, 类型: {Type}",
                        orderId, order.Account, winAmount, description);
                    return false;
                }

                logger.LogInformation(@"会员 {Account} {Description}，订单ID: {OrderId}, 金额: {Amount}, 余额已更新",
                    order.Account, description, orderId, winAmount);
            }

            logger.LogInformation(@"结算投注订单成功，订单ID: {OrderId}, 账号: {Account}, 结算结果: {SettlementResult}, 金额: {Amount}",
                orderId, order.Account, settlementResult, winAmount);

            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"结算投注订单失败，订单ID: {OrderId}", orderId);
            return false;
        }
    }

    /// <summary>
    /// 派奖投注订单
    /// </summary>
    public async Task<bool> PayoutBetOrderAsync(long orderId, string processor)
    {
        try
        {
            // 获取订单信息
            var order = await fSql.Select<BetOrder>()
                .Where(o => o.Id == orderId && (o.Status == EnumBetOrderStatus.Win || o.Status == EnumBetOrderStatus.Draw))
                .ToOneAsync()
                .ConfigureAwait(false);

            if (order == null)
            {
                logger.LogWarning(@"派奖投注订单失败，订单不存在或状态不正确，订单ID: {OrderId}", orderId);
                return false;
            }

            if (!order.ActualWinAmount.HasValue || order.ActualWinAmount <= 0)
            {
                logger.LogWarning(@"派奖投注订单失败，中奖金额无效，订单ID: {OrderId}", orderId);
                return false;
            }

            // 派发中奖金额
            var payoutSuccess = await financialService.IncreaseBalanceAsync(
                    order.Account, order.ActualWinAmount.Value, @"BetOrder", orderId,
                    $@"投注中奖派奖，项目: {order.PlayItem}, 中奖金额: {order.ActualWinAmount.Value}", processor, @"投注派奖")
                .ConfigureAwait(false);

            if (payoutSuccess)
            {
                // 更新订单派奖时间（状态保持不变）
                var updateResult = await fSql.Update<BetOrder>()
                    .Set(o => o.PaidTime, DateTime.Now)
                    .Set(o => o.Processor, processor)
                    .Where(o => o.Id == orderId)
                    .ExecuteAffrowsAsync()
                    .ConfigureAwait(false);

                if (updateResult > 0)
                {
                    logger.LogInformation(@"派奖投注订单成功，订单ID: {OrderId}, 派奖金额: {PayoutAmount}, 处理人: {Processor}",
                        orderId, order.ActualWinAmount.Value, processor);
                    return true;
                }
            }

            logger.LogError(@"派奖投注订单失败，订单ID: {OrderId}", orderId);
            return false;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"派奖投注订单失败，订单ID: {OrderId}", orderId);
            return false;
        }
    }

    /// <summary>
    /// 获取待处理的投注订单列表
    /// </summary>
    public async Task<List<BetOrder>> GetPendingBetOrdersAsync()
    {
        try
        {
            return await fSql.Select<BetOrder>()
                .Where(o => o.Status == EnumBetOrderStatus.Confirmed)
                .OrderBy(o => o.CreatedTime)
                .ToListAsync()
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取待处理投注订单列表失败");
            return [];
        }
    }

    /// <summary>
    /// 获取指定期号的投注订单列表
    /// </summary>
    public async Task<List<BetOrder>> GetBetOrdersByIssueAsync(string issue)
    {
        try
        {
            return await fSql.Select<BetOrder>()
                .Where(o => o.Issue == issue)
                .OrderBy(o => o.CreatedTime)
                .ToListAsync()
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取期号投注订单列表失败，期号: {Issue}", issue);
            return [];
        }
    }

    /// <summary>
    /// 获取指定用户的投注订单列表
    /// </summary>
    public async Task<List<BetOrder>> GetBetOrdersByAccountAsync(string account, int limit = 0)
    {
        try
        {
            var query = fSql.Select<BetOrder>()
                .Where(o => o.Account == account)
                .OrderByDescending(o => o.CreatedTime);

            // 如果limit大于0，则应用限制；否则返回所有记录
            if (limit > 0)
            {
                query = query.Take(limit);
            }

            return await query.ToListAsync().ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取用户投注订单列表失败，用户: {Account}", account);
            return [];
        }
    }

    /// <summary>
    /// 获取指定用户和期号的投注订单
    /// </summary>
    public async Task<List<BetOrder>> GetBetOrdersByAccountAndIssueAsync(string account, string issue)
    {
        try
        {
            return await fSql.Select<BetOrder>()
                .Where(o => o.Account == account && o.Issue == issue && o.Status == EnumBetOrderStatus.Confirmed)
                .OrderByDescending(o => o.CreatedTime)
                .ToListAsync()
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取用户指定期号投注订单失败，用户: {Account}, 期号: {Issue}", account, issue);
            return [];
        }
    }

    /// <summary>
    /// 根据ID获取投注订单
    /// </summary>
    public async Task<BetOrder?> GetBetOrderAsync(long orderId)
    {
        try
        {
            return await fSql.Select<BetOrder>()
                .Where(o => o.Id == orderId)
                .ToOneAsync()
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取投注订单失败，订单ID: {OrderId}", orderId);
            return null;
        }
    }

    /// <summary>
    /// 获取当前期的投注订单列表
    /// </summary>
    public async Task<List<BetOrder>> GetCurrentIssueBetOrdersAsync(string currentIssue)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(currentIssue))
            {
                logger.LogWarning(@"获取当前期投注订单失败，期号为空");
                return [];
            }

            return await fSql.Select<BetOrder>()
                .Where(o => o.Issue == currentIssue)
                .OrderByDescending(o => o.CreatedTime)
                .ToListAsync()
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取当前期投注订单列表失败，期号: {Issue}", currentIssue);
            return [];
        }
    }

    /// <summary>
    /// 获取当前期的增量投注订单列表（指定时间之后的数据）
    /// </summary>
    public async Task<List<BetOrder>> GetCurrentIssueBetOrdersIncrementalAsync(string currentIssue, DateTime lastUpdateTime)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(currentIssue))
            {
                logger.LogWarning(@"获取当前期增量投注订单失败，期号为空");
                return [];
            }

            return await fSql.Select<BetOrder>()
                .Where(o => o.Issue == currentIssue &&
                            (o.CreatedTime > lastUpdateTime ||
                             o.FlightTime > lastUpdateTime ||
                             o.FlightCompletedTime > lastUpdateTime))
                .OrderByDescending(o => o.CreatedTime)
                .ToListAsync()
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取当前期增量投注订单列表失败，期号: {Issue}, 最后更新时间: {LastUpdateTime}",
                currentIssue, lastUpdateTime);
            return [];
        }
    }

    /// <summary>
    /// 获取所有未结算的投注订单列表
    /// </summary>
    public async Task<List<BetOrder>> GetUnSettledBetOrdersAsync()
    {
        try
        {
            return await fSql.Select<BetOrder>()
                .Where(o => o.Status == EnumBetOrderStatus.Confirmed)
                .OrderBy(o => o.Issue)
                .OrderBy(o => o.CreatedTime)
                .ToListAsync()
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取未结算投注订单列表失败");
            return [];
        }
    }

    #endregion

    #region 飞单状态管理

    /// <summary>
    /// 更新指定期号的所有投注订单的飞单状态
    /// </summary>
    /// <param name="issue">期号</param>
    /// <param name="enumFlightStatus">飞单状态</param>
    /// <param name="flightNote">飞单备注</param>
    /// <returns>更新成功的记录数</returns>
    public async Task<int> UpdateBetOrderFlightStatusByIssueAsync(string issue, EnumFlightOrderStatus enumFlightStatus, string flightNote = "")
    {
        try
        {
            if (string.IsNullOrWhiteSpace(issue))
            {
                logger.LogWarning(@"更新飞单状态失败，期号为空");
                return 0;
            }

            var now = DateTime.Now;
            var updateResult = await fSql.Update<BetOrder>()
                .Set(o => o.EnumFlightStatus, enumFlightStatus)
                .Set(o => o.FlightCompletedTime, now)
                .Set(o => o.FlightNote, flightNote)
                .Where(o => o.Issue == issue && o.Status == EnumBetOrderStatus.Confirmed)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            logger.LogInformation(@"更新期号 {Issue} 的飞单状态完成，状态: {Status}, 更新记录数: {Count}, 备注: {Note}",
                issue, enumFlightStatus.GetDescription(), updateResult, flightNote);

            return updateResult;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"更新期号 {Issue} 的飞单状态时发生异常，状态: {Status}", issue, enumFlightStatus);
            return 0;
        }
    }

    /// <summary>
    /// 更新单个投注订单的飞单状态
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="enumFlightStatus">飞单状态</param>
    /// <param name="flightNote">飞单备注</param>
    /// <returns>是否更新成功</returns>
    public async Task<bool> UpdateBetOrderFlightStatusAsync(long orderId, EnumFlightOrderStatus enumFlightStatus, string flightNote = "")
    {
        try
        {
            var now = DateTime.Now;
            var updateResult = await fSql.Update<BetOrder>()
                .Set(o => o.EnumFlightStatus, enumFlightStatus)
                .Set(o => o.FlightCompletedTime, now)
                .Set(o => o.FlightNote, flightNote)
                .Where(o => o.Id == orderId)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            if (updateResult > 0)
            {
                logger.LogInformation(@"更新订单 {OrderId} 的飞单状态成功，状态: {Status}, 备注: {Note}",
                    orderId, enumFlightStatus.GetDescription(), flightNote);
                return true;
            }

            logger.LogWarning(@"更新订单 {OrderId} 的飞单状态失败，订单不存在", orderId);
            return false;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"更新订单 {OrderId} 的飞单状态时发生异常，状态: {Status}", orderId, enumFlightStatus);
            return false;
        }
    }

    #endregion

    /// <summary>
    /// 根据条件查询投注记录
    /// </summary>
    public async Task<List<BetRecordViewModel>> QueryBetRecordsAsync(
        DateTime startTime,
        DateTime endTime,
        string? issue = null,
        string? account = null,
        bool includeFakeUsers = false)
    {
        try
        {
            logger.LogInformation(@"开始查询投注记录，时间范围: {StartTime} - {EndTime}, 期号: {Issue}, 账号: {Account}, 包含假人: {IncludeFakeUsers}",
                startTime, endTime, issue ?? "全部", account ?? "全部", includeFakeUsers);

            // 从数据库查询真实的投注记录数据
            var realData = await QueryRealBetRecordsFromDatabaseAsync(startTime, endTime, issue, account, includeFakeUsers);

            logger.LogInformation(@"查询投注记录完成，共找到 {Count} 条记录", realData.Count);
            return realData;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"查询投注记录时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 计算有效投注总额
    /// </summary>
    public decimal CalculateValidBetAmount(List<BetRecordViewModel> betRecords)
    {
        if (betRecords == null || betRecords.Count == 0)
            return 0;

        // 有效投注是指开奖后赢了或输了的注单，和局的注单不计算在内
        return betRecords
            .Where(record => record.IsValidBet)
            .Sum(record => record.Amount);
    }

    /// <summary>
    /// 计算总盈亏
    /// </summary>
    public decimal CalculateTotalProfitLoss(List<BetRecordViewModel> betRecords)
    {
        if (betRecords == null || betRecords.Count == 0)
            return 0;

        return betRecords.Sum(record => record.ProfitLoss);
    }

    /// <summary>
    /// 获取投注记录统计信息
    /// </summary>
    public (decimal ValidBetAmount, decimal TotalProfitLoss) GetBetRecordStatistics(List<BetRecordViewModel> betRecords)
    {
        var validBetAmount = CalculateValidBetAmount(betRecords);
        var totalProfitLoss = CalculateTotalProfitLoss(betRecords);

        logger.LogDebug(@"投注记录统计 - 有效投注总额: {ValidBetAmount:F2}, 总盈亏: {TotalProfitLoss:F2}",
            validBetAmount, totalProfitLoss);

        return (validBetAmount, totalProfitLoss);
    }

    /// <summary>
    /// 从数据库查询真实的投注记录数据
    /// </summary>
    private async Task<List<BetRecordViewModel>> QueryRealBetRecordsFromDatabaseAsync(
        DateTime startTime,
        DateTime endTime,
        string? issue,
        string? account,
        bool includeFakeUsers)
    {
        try
        {
            // 构建查询条件
            var query = fSql.Select<BetOrder>()
                .Where(b => b.CreatedTime >= startTime && b.CreatedTime <= endTime);

            // 添加期号过滤
            if (!string.IsNullOrEmpty(issue))
            {
                query = query.Where(b => b.Issue == issue);
            }

            // 添加账号过滤
            if (!string.IsNullOrEmpty(account))
            {
                query = query.Where(b => b.Account.Contains(account));
            }

            // 获取投注订单数据
            var betOrders = await query.OrderByDescending(b => b.CreatedTime).ToListAsync();

            // 获取所有相关会员信息
            var memberAccounts = betOrders.Select(b => b.Account).Distinct().ToList();
            var members = await memberService.GetMembersAsync();
            var memberDict = members.ToDictionary(m => m.Account, m => m);

            // 转换为视图模型
            var result = new List<BetRecordViewModel>();
            foreach (var betOrder in betOrders)
            {
                var member = memberDict.GetValueOrDefault(betOrder.Account);

                // 根据includeFakeUsers参数过滤假人
                if (!includeFakeUsers && member?.UserType == @"假人")
                    continue;

                result.Add(new BetRecordViewModel
                {
                    Id = betOrder.Id,
                    OrderNumber = betOrder.OrderNumber,
                    Issue = betOrder.Issue,
                    Account = betOrder.Account,
                    NickName = !string.IsNullOrWhiteSpace(betOrder.NickName) ? betOrder.NickName : (member?.NickName ?? betOrder.Account),
                    PlayItem = betOrder.PlayItem,
                    Amount = betOrder.Amount,
                    Odds = betOrder.Odds,
                    WinningNumbers = betOrder.DrawResult,
                    RebateAmount = betOrder.RebateAmount,
                    SettlementStatus = ConvertToSettlementStatus(betOrder.Status),
                    CreatedTime = betOrder.CreatedTime
                });
            }

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"从数据库查询投注记录失败");
            return [];
        }
    }

    /// <summary>
    /// 将BetOrderStatus转换为BetOrderStatus（用于结算状态显示）
    /// </summary>
    private static EnumBetOrderStatus ConvertToSettlementStatus(EnumBetOrderStatus status)
    {
        return status; // 现在直接返回原状态，因为BetOrderStatus已包含所有结算状态
    }
}