using CommandGuard.Enums;
using CommandGuard.Interfaces.Business;
using CommandGuard.Models;
using CommandGuard.ViewModels;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Business;

/// <summary>
/// 上下分记录服务实现
/// 提供上下分记录的查询和统计功能
/// </summary>
public class DepositWithdrawRecordService(
    ILogger<DepositWithdrawRecordService> logger,
    IFreeSql fSql,
    IFinancialService financialService,
    IMemberService memberService) : IDepositWithdrawRecordService
{
    #region 上分申请相关

    /// <summary>
    /// 创建上分申请 - 用户充值申请处理
    ///
    /// 功能：
    /// - 验证申请参数的有效性
    /// - 创建上分申请记录
    /// - 生成唯一的申请单号
    /// - 设置初始状态为待审核
    ///
    /// 业务流程：
    /// 1. 参数验证（账号、金额等）
    /// 2. 获取或创建会员信息
    /// 3. 创建申请记录
    /// 4. 生成申请单号
    /// 5. 返回申请ID
    ///
    /// 申请状态：初始为Pending（待审核）
    /// </summary>
    public async Task<long> CreateDepositRequestAsync(string account, decimal amount, string originalMessage, string messageId)
    {
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException(@"用户账号不能为空", nameof(account));

        if (amount <= 0)
            throw new ArgumentException(@"申请金额必须大于0", nameof(amount));

        try
        {
            var request = new DepositRequest
            {
                Account = account.Trim(),
                Amount = amount,
                OriginalMessage = originalMessage.Trim(),
                MessageId = messageId,
                Status = EnumDepositStatus.Pending,
                CreatedTime = DateTime.Now
            };

            var requestId = await fSql.Insert<DepositRequest>()
                .AppendData(request)
                .ExecuteIdentityAsync()
                .ConfigureAwait(false);

            logger.LogInformation(@"创建上分申请成功，用户: {Account}, 金额: {Amount}, 申请ID: {RequestId}",
                account, amount, requestId);

            return requestId;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"创建上分申请失败，用户: {Account}, 金额: {Amount}", account, amount);
            throw;
        }
    }

    /// <summary>
    /// 审核上分申请 - 管理员审核充值申请
    ///
    /// 功能：
    /// - 更新申请的审核状态
    /// - 记录审核人和审核意见
    /// - 审核通过时自动执行上分操作
    /// - 更新用户余额和财务记录
    ///
    /// 审核流程：
    /// 1. 验证申请状态（必须是待审核）
    /// 2. 更新审核信息
    /// 3. 如果审核通过，执行上分操作
    /// 4. 记录操作日志
    ///
    /// 返回值：true表示审核成功，false表示审核失败
    /// </summary>
    public async Task<bool> ReviewDepositRequestAsync(long requestId, bool approved, string reviewer, string? reviewNote = null)
    {
        try
        {
            var request = await GetDepositRequestAsync(requestId).ConfigureAwait(false);
            if (request == null)
            {
                logger.LogWarning(@"上分申请不存在，ID: {RequestId}", requestId);
                return false;
            }

            if (request.Status != EnumDepositStatus.Pending)
            {
                logger.LogWarning(@"上分申请状态不是待审核，无法处理，ID: {RequestId}, 状态: {Status}",
                    requestId, request.Status);
                return false;
            }

            // 更新申请状态
            var newStatus = approved ? EnumDepositStatus.Approved : EnumDepositStatus.Rejected;
            await fSql.Update<DepositRequest>()
                .Set(r => r.Status, newStatus)
                .Set(r => r.ReviewTime, DateTime.Now)
                .Set(r => r.Reviewer, reviewer)
                .Set(r => r.ReviewNote, reviewNote ?? string.Empty)
                .Where(r => r.Id == requestId)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            // 如果批准，处理上分
            if (approved)
            {
                var success = await financialService.ProcessDepositAsync(
                        request.Account, request.Amount, requestId, reviewer, reviewNote ?? string.Empty)
                    .ConfigureAwait(false);

                if (!success)
                {
                    logger.LogError(@"处理上分失败，申请ID: {RequestId}", requestId);
                    return false;
                }
            }

            logger.LogInformation(@"上分申请审核完成，ID: {RequestId}, 结果: {Result}, 审核人: {Reviewer}",
                requestId, approved ? @"批准" : @"拒绝", reviewer);

            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"审核上分申请失败，ID: {RequestId}", requestId);
            return false;
        }
    }

    /// <summary>
    /// 获取待审核的上分申请列表
    /// </summary>
    public async Task<List<DepositRequest>> GetPendingDepositRequestsAsync()
    {
        try
        {
            return await fSql.Select<DepositRequest>()
                .Where(r => r.Status == EnumDepositStatus.Pending)
                .OrderBy(r => r.CreatedTime)
                .ToListAsync()
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取待审核上分申请列表失败");
            return [];
        }
    }

    /// <summary>
    /// 根据ID获取上分申请
    /// </summary>
    public async Task<DepositRequest?> GetDepositRequestAsync(long requestId)
    {
        try
        {
            return await fSql.Select<DepositRequest>()
                .Where(r => r.Id == requestId)
                .ToOneAsync()
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取上分申请失败，ID: {RequestId}", requestId);
            return null;
        }
    }

    #endregion

    #region 下分申请相关

    /// <summary>
    /// 创建下分申请
    /// </summary>
    public async Task<long> CreateWithdrawRequestAsync(string account, decimal amount, string originalMessage, string messageId)
    {
        if (string.IsNullOrWhiteSpace(account))
            throw new ArgumentException(@"用户账号不能为空", nameof(account));

        if (amount <= 0)
            throw new ArgumentException(@"申请金额必须大于0", nameof(amount));

        try
        {
            // 检查余额是否足够
            var sufficient = await financialService.CheckBalanceSufficientAsync(account, amount)
                .ConfigureAwait(false);

            if (!sufficient)
            {
                // 余额不足，创建余额不足状态的申请记录
                var insufficientRequest = new WithdrawRequest
                {
                    Account = account.Trim(),
                    Amount = amount,
                    OriginalMessage = originalMessage.Trim(),
                    MessageId = messageId,
                    Status = EnumWithdrawStatus.InsufficientBalance,
                    BalanceBeforeRequest = await financialService.GetCurrentBalanceAsync(account).ConfigureAwait(false),
                    IsFrozen = false,
                    CreatedTime = DateTime.Now
                };

                var insufficientId = await fSql.Insert<WithdrawRequest>()
                    .AppendData(insufficientRequest)
                    .ExecuteIdentityAsync()
                    .ConfigureAwait(false);

                logger.LogWarning(@"下分申请余额不足，用户: {Account}, 申请金额: {Amount}, 当前余额: {Balance}",
                    account, amount, insufficientRequest.BalanceBeforeRequest);

                return insufficientId;
            }

            // 余额足够，先冻结金额，再创建申请
            var currentBalance = await financialService.GetCurrentBalanceAsync(account).ConfigureAwait(false);

            // 减少用户余额（冻结）
            var freezeSuccess = await financialService.DecreaseBalanceAsync(
                    account, amount, @"WithdrawRequest", 0, // 临时使用0，后面会更新
                    $@"下分申请冻结金额，申请金额: {amount}", @"System", @"下分申请")
                .ConfigureAwait(false);

            if (!freezeSuccess)
            {
                logger.LogError(@"冻结下分金额失败，用户: {Account}, 金额: {Amount}", account, amount);
                return -1;
            }

            var request = new WithdrawRequest
            {
                Account = account.Trim(),
                Amount = amount,
                OriginalMessage = originalMessage.Trim(),
                MessageId = messageId,
                Status = EnumWithdrawStatus.Pending,
                BalanceBeforeRequest = currentBalance,
                IsFrozen = true,
                CreatedTime = DateTime.Now
            };

            var requestId = await fSql.Insert<WithdrawRequest>()
                .AppendData(request)
                .ExecuteIdentityAsync()
                .ConfigureAwait(false);

            logger.LogInformation(@"创建下分申请成功，用户: {Account}, 金额: {Amount}, 申请ID: {RequestId}",
                account, amount, requestId);

            return requestId;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"创建下分申请失败，用户: {Account}, 金额: {Amount}", account, amount);
            throw;
        }
    }

    /// <summary>
    /// 审核下分申请
    /// </summary>
    public async Task<bool> ReviewWithdrawRequestAsync(long requestId, bool approved, string reviewer, string? reviewNote = null)
    {
        try
        {
            var request = await GetWithdrawRequestAsync(requestId).ConfigureAwait(false);
            if (request == null)
            {
                logger.LogWarning(@"下分申请不存在，ID: {RequestId}", requestId);
                return false;
            }

            if (request.Status != EnumWithdrawStatus.Pending)
            {
                logger.LogWarning(@"下分申请状态不是待审核，无法处理，ID: {RequestId}, 状态: {Status}",
                    requestId, request.Status);
                return false;
            }

            var newStatus = approved ? EnumWithdrawStatus.Approved : EnumWithdrawStatus.Rejected;

            // 更新申请状态
            await fSql.Update<WithdrawRequest>()
                .Set(r => r.Status, newStatus)
                .Set(r => r.ReviewTime, DateTime.Now)
                .Set(r => r.Reviewer, reviewer)
                .Set(r => r.ReviewNote, reviewNote ?? string.Empty)
                .Where(r => r.Id == requestId)
                .ExecuteAffrowsAsync()
                .ConfigureAwait(false);

            if (!approved)
            {
                // 拒绝申请，需要退还冻结的金额
                var refundSuccess = await financialService.IncreaseBalanceAsync(
                        request.Account, request.Amount, @"WithdrawRequest", requestId,
                        $@"下分申请被拒绝，退还冻结金额: {request.Amount}", reviewer, @"下分申请拒绝")
                    .ConfigureAwait(false);

                if (!refundSuccess)
                {
                    logger.LogError(@"退还冻结金额失败，申请ID: {RequestId}", requestId);
                    return false;
                }
            }

            logger.LogInformation(@"下分申请审核完成，ID: {RequestId}, 结果: {Result}, 审核人: {Reviewer}",
                requestId, approved ? @"批准" : @"拒绝", reviewer);

            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"审核下分申请失败，ID: {RequestId}", requestId);
            return false;
        }
    }

    /// <summary>
    /// 获取待审核的下分申请列表
    /// </summary>
    public async Task<List<WithdrawRequest>> GetPendingWithdrawRequestsAsync()
    {
        try
        {
            return await fSql.Select<WithdrawRequest>()
                .Where(r => r.Status == EnumWithdrawStatus.Pending)
                .OrderBy(r => r.CreatedTime)
                .ToListAsync()
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取待审核下分申请列表失败");
            return [];
        }
    }

    /// <summary>
    /// 根据ID获取下分申请
    /// </summary>
    public async Task<WithdrawRequest?> GetWithdrawRequestAsync(long requestId)
    {
        try
        {
            return await fSql.Select<WithdrawRequest>()
                .Where(r => r.Id == requestId)
                .ToOneAsync()
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取下分申请失败，ID: {RequestId}", requestId);
            return null;
        }
    }

    #endregion

    
    
    
    /// <summary>
    /// 根据条件查询上下分记录
    /// </summary>
    public async Task<List<DepositWithdrawRecordViewModel>> QueryDepositWithdrawRecordsAsync(
        DateTime startTime,
        DateTime endTime,
        string? account = null,
        string? type = null,
        bool includeFakeUsers = false)
    {
        try
        {
            logger.LogInformation(@"开始查询上下分记录，时间范围: {StartTime} - {EndTime}, 账号: {Account}, 类型: {Type}, 包含假人: {IncludeFakeUsers}",
                startTime, endTime, account ?? "全部", type ?? "全部", includeFakeUsers);

            // 从数据库查询真实的上下分记录数据
            var realData = await QueryRealDepositWithdrawRecordsFromDatabaseAsync(startTime, endTime, account, type, includeFakeUsers);

            logger.LogInformation(@"查询上下分记录完成，共找到 {Count} 条记录", realData.Count);
            return realData;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"查询上下分记录时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 计算总上分金额（只统计已通过的记录）
    /// </summary>
    public decimal CalculateTotalDepositAmount(List<DepositWithdrawRecordViewModel> records)
    {
        if (records == null || records.Count == 0)
            return 0;

        return records
            .Where(record => record.Type == @"上分" && record.Status == EnumDepositStatus.Approved)
            .Sum(record => record.Amount);
    }

    /// <summary>
    /// 计算总下分金额（只统计已通过的记录）
    /// </summary>
    public decimal CalculateTotalWithdrawAmount(List<DepositWithdrawRecordViewModel> records)
    {
        if (records == null || records.Count == 0)
            return 0;

        return records
            .Where(record => record.Type == @"下分" && record.Status == EnumDepositStatus.Approved)
            .Sum(record => record.Amount);
    }

    /// <summary>
    /// 计算总积分（所有查询用户的可用积分总和）
    /// </summary>
    public async Task<decimal> CalculateTotalBalanceAsync(List<DepositWithdrawRecordViewModel> records)
    {
        if (records == null || records.Count == 0)
            return 0;

        // 获取所有唯一的用户账号
        var uniqueAccounts = records.Select(r => r.Account).Distinct().ToList();

        // 查询这些用户的当前余额
        var members = await fSql.Select<Member>()
            .Where(m => uniqueAccounts.Contains(m.Account) && !m.Deleted)
            .ToListAsync()
            .ConfigureAwait(false);

        return members.Sum(m => m.Balance);
    }

    /// <summary>
    /// 计算总回水金额
    /// </summary>
    public decimal CalculateTotalRebateAmount(List<DepositWithdrawRecordViewModel> records)
    {
        if (records == null || records.Count == 0)
            return 0;

        return records
            .Where(record => record.Type == @"回水")
            .Sum(record => record.Amount);
    }

    /// <summary>
    /// 获取上下分记录统计信息（只统计已通过的记录）
    /// </summary>
    public async Task<(decimal TotalDeposit, decimal TotalWithdraw, decimal TotalRebate, decimal TotalBalance)> GetDepositWithdrawStatisticsAsync(List<DepositWithdrawRecordViewModel> records)
    {
        var totalDeposit = CalculateTotalDepositAmount(records);
        var totalWithdraw = CalculateTotalWithdrawAmount(records);
        var totalRebate = CalculateTotalRebateAmount(records);
        var totalBalance = await CalculateTotalBalanceAsync(records);

        logger.LogDebug(@"上下分记录统计 - 总上分: {TotalDeposit:F2}, 总下分: {TotalWithdraw:F2}, 总回水: {TotalRebate:F2}, 总积分: {TotalBalance:F2}",
            totalDeposit, totalWithdraw, totalRebate, totalBalance);

        return (totalDeposit, totalWithdraw, totalRebate, totalBalance);
    }

    /// <summary>
    /// 获取指定用户的累计上下分金额
    /// </summary>
    public async Task<(decimal TotalDeposit, decimal TotalWithdraw)> GetUserTotalDepositWithdrawAsync(string account)
    {
        try
        {
            // 从数据库查询用户的历史累计数据
            var records = await QueryRealDepositWithdrawRecordsFromDatabaseAsync(
                DateTime.MinValue, DateTime.MaxValue, account, null, true);

            var totalDeposit = records.Where(r => r.Type == @"上分").Sum(r => r.Amount);
            var totalWithdraw = records.Where(r => r.Type == @"下分").Sum(r => r.Amount);

            return (totalDeposit, totalWithdraw);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取用户累计上下分金额失败，账号: {Account}", account);
            return (0, 0);
        }
    }

    /// <summary>
    /// 从数据库查询真实的上下分记录数据
    /// </summary>
    private async Task<List<DepositWithdrawRecordViewModel>> QueryRealDepositWithdrawRecordsFromDatabaseAsync(
        DateTime startTime,
        DateTime endTime,
        string? account,
        string? type,
        bool includeFakeUsers)
    {
        try
        {
            var result = new List<DepositWithdrawRecordViewModel>();

            // 查询上分记录
            var depositQuery = fSql.Select<DepositRequest>()
                .Where(d => d.CreatedTime >= startTime && d.CreatedTime <= endTime);

            if (!string.IsNullOrEmpty(account))
            {
                depositQuery = depositQuery.Where(d => d.Account.Contains(account));
            }

            if (string.IsNullOrEmpty(type) || type == @"上分")
            {
                // 查询上分申请记录
                var depositRecords = await depositQuery.OrderByDescending(d => d.CreatedTime).ToListAsync();

                foreach (var record in depositRecords)
                {
                    result.Add(new DepositWithdrawRecordViewModel
                    {
                        Id = record.Id,
                        OrderNumber = GenerateDepositOrderNumber(record.Id, record.CreatedTime),
                        Account = record.Account,
                        Type = @"上分",
                        Amount = record.Amount,
                        Status = record.Status,
                        CreatedTime = record.CreatedTime,
                        ProcessedTime = record.ReviewTime // 设置处理时间为审核时间
                    });
                }
            }

            // 查询下分记录
            var withdrawQuery = fSql.Select<WithdrawRequest>()
                .Where(w => w.CreatedTime >= startTime && w.CreatedTime <= endTime);

            if (!string.IsNullOrEmpty(account))
            {
                withdrawQuery = withdrawQuery.Where(w => w.Account.Contains(account));
            }

            if (string.IsNullOrEmpty(type) || type == @"下分")
            {
                var withdrawRecords = await withdrawQuery.OrderByDescending(w => w.CreatedTime).ToListAsync();

                foreach (var record in withdrawRecords)
                {
                    result.Add(new DepositWithdrawRecordViewModel
                    {
                        Id = record.Id,
                        OrderNumber = GenerateWithdrawOrderNumber(record.Id, record.CreatedTime),
                        Account = record.Account,
                        Type = @"下分",
                        Amount = record.Amount,
                        Status = ConvertWithdrawToDepositStatus(record.Status),
                        CreatedTime = record.CreatedTime,
                        ProcessedTime = record.ReviewTime // 设置处理时间为审核时间
                    });
                }
            }

            // 查询回水记录
            if (string.IsNullOrEmpty(type) || type == @"回水")
            {
                var rebateQuery = fSql.Select<FinancialRecord>()
                    .Where(f => f.Type == EnumFinancialType.Rebate)
                    .Where(f => f.CreatedTime >= startTime && f.CreatedTime <= endTime);

                if (!string.IsNullOrEmpty(account))
                {
                    rebateQuery = rebateQuery.Where(f => f.Account.Contains(account));
                }

                var rebateRecords = await rebateQuery.OrderByDescending(f => f.CreatedTime).ToListAsync();

                foreach (var record in rebateRecords)
                {
                    result.Add(new DepositWithdrawRecordViewModel
                    {
                        Id = record.Id,
                        OrderNumber = GenerateRebateOrderNumber(record.Id, record.CreatedTime),
                        Account = record.Account,
                        Type = @"回水",
                        Amount = record.Amount,
                        Status = EnumDepositStatus.Approved, // 回水记录默认为已通过状态
                        CreatedTime = record.CreatedTime,
                        ProcessedTime = record.CreatedTime // 回水记录的处理时间等于创建时间
                    });
                }
            }

            // 获取所有相关会员信息并设置昵称
            var memberAccounts = result.Select(r => r.Account).Distinct().ToList();
            var members = await memberService.GetMembersAsync();
            var memberDict = members.ToDictionary(m => m.Account, m => m);

            // 计算每个会员的历史累计总上分和总下分
            var memberTotals = new Dictionary<string, (decimal totalDeposit, decimal totalWithdraw)>();
            foreach (var memberAccount in memberAccounts)
            {
                // 查询该会员的所有历史上分记录
                var allDepositRecords = await fSql.Select<DepositRequest>()
                    .Where(d => d.Account == memberAccount)
                    .ToListAsync();

                // 查询该会员的所有历史下分记录
                var allWithdrawRecords = await fSql.Select<WithdrawRequest>()
                    .Where(w => w.Account == memberAccount)
                    .ToListAsync();

                var totalDeposit = allDepositRecords.Sum(r => r.Amount);
                var totalWithdraw = allWithdrawRecords.Sum(r => r.Amount);
                memberTotals[memberAccount] = (totalDeposit, totalWithdraw);
            }

            // 过滤假人并设置昵称和累计金额
            var filteredResult = new List<DepositWithdrawRecordViewModel>();
            foreach (var record in result)
            {
                var member = memberDict.GetValueOrDefault(record.Account);

                // 根据includeFakeUsers参数过滤假人
                if (!includeFakeUsers && member?.UserType == @"假人")
                    continue;

                record.NickName = member?.NickName ?? record.Account;

                // 设置是否为假人
                record.IsFakeUser = member?.UserType == @"假人";

                // 设置累计总上分和总下分
                if (memberTotals.TryGetValue(record.Account, out var totals))
                {
                    record.TotalDeposit = totals.totalDeposit;
                    record.TotalWithdraw = totals.totalWithdraw;
                }

                filteredResult.Add(record);
            }

            return filteredResult.OrderByDescending(r => r.CreatedTime).ToList();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"从数据库查询上下分记录失败");
            return [];
        }
    }

    /// <summary>
    /// 将WithdrawStatus转换为DepositStatus枚举
    /// </summary>
    private static EnumDepositStatus ConvertWithdrawToDepositStatus(EnumWithdrawStatus status)
    {
        return status switch
        {
            EnumWithdrawStatus.Pending => EnumDepositStatus.Pending,
            EnumWithdrawStatus.Approved => EnumDepositStatus.Approved,
            EnumWithdrawStatus.Rejected => EnumDepositStatus.Rejected,
            EnumWithdrawStatus.InsufficientBalance => EnumDepositStatus.Rejected,
            _ => EnumDepositStatus.Pending
        };
    }

    /// <summary>
    /// 生成上分申请单号
    /// 格式：DEP + 年月日 + 申请ID（6位补零）
    /// 例如：DEP20241211000123
    /// </summary>
    /// <param name="requestId">申请ID</param>
    /// <param name="createdTime">创建时间</param>
    /// <returns>上分申请单号</returns>
    private static string GenerateDepositOrderNumber(long requestId, DateTime createdTime)
    {
        var dateStr = createdTime.ToString(@"yyyyMMdd");
        var requestIdStr = requestId.ToString(@"D6"); // 6位数字，不足补零
        return $@"DEP{dateStr}{requestIdStr}";
    }

    /// <summary>
    /// 生成下分申请单号
    /// 格式：WTD + 年月日 + 申请ID（6位补零）
    /// 例如：WTD20241211000123
    /// </summary>
    /// <param name="requestId">申请ID</param>
    /// <param name="createdTime">创建时间</param>
    /// <returns>下分申请单号</returns>
    private static string GenerateWithdrawOrderNumber(long requestId, DateTime createdTime)
    {
        var dateStr = createdTime.ToString(@"yyyyMMdd");
        var requestIdStr = requestId.ToString(@"D6"); // 6位数字，不足补零
        return $@"WTD{dateStr}{requestIdStr}";
    }

    /// <summary>
    /// 生成回水记录单号
    /// 格式：RBT + 年月日 + 记录ID（6位补零）
    /// 例如：RBT20241211000123
    /// </summary>
    /// <param name="recordId">记录ID</param>
    /// <param name="createdTime">创建时间</param>
    /// <returns>回水记录单号</returns>
    private static string GenerateRebateOrderNumber(long recordId, DateTime createdTime)
    {
        var dateStr = createdTime.ToString(@"yyyyMMdd");
        var recordIdStr = recordId.ToString(@"D6"); // 6位数字，不足补零
        return $@"RBT{dateStr}{recordIdStr}";
    }
}