﻿using CommandGuard.Configuration;
using CommandGuard.Enums;
using CommandGuard.Interfaces.Chat;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services.Chat;

/// <summary>
/// Chat服务实现 - 简化版本，直接管理平台选择
/// </summary>
public class ChatService(
    ILogger<ChatService> logger,
    MyQqPlatformService myQqPlatform,
    OneChatPlatformService oneChatPlatform) : IChatService
{
    /// <summary>
    /// 获取当前配置的聊天平台助手
    /// </summary>
    /// <returns>当前平台助手实例</returns>
    private IChatPlatformHelper GetCurrentPlatformHelper()
    {
        return RuntimeConfiguration.SelectedChatApp switch
        {
            EnumChatApp.MyQQ => myQqPlatform,
            EnumChatApp.一起聊吧 => oneChatPlatform,
            _ => throw new NotSupportedException($@"不支持的聊天平台: {RuntimeConfiguration.SelectedChatApp}")
        };
    }

    /// <summary>
    /// 获取机器人信息 - 初始化机器人账号和昵称
    ///
    /// 功能：
    /// - 从当前聊天平台获取机器人信息
    /// - 更新RuntimeConfiguration中的机器人信息
    /// - 用于系统初始化和状态显示
    ///
    /// 调用时机：
    /// - 系统启动时
    /// - 切换聊天平台时
    /// - 需要刷新机器人信息时
    /// </summary>
    public async Task GetRobotInfoAsync()
    {
        try
        {
            // 获取当前平台的助手实例
            var platformHelper = GetCurrentPlatformHelper();
            logger.LogDebug("开始获取机器人信息，平台: {Platform}", platformHelper.GetPlatformName());

            // 调用平台特定的实现
            await platformHelper.GetRobotInfoAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "GetRobotInfoAsync");
        }
    }

    /// <summary>
    /// 获取群组信息
    /// </summary>
    public async Task GetGroupDicAsync()
    {
        try
        {
            // 获取当前平台的助手实例
            var platformHelper = GetCurrentPlatformHelper();
            logger.LogDebug("开始获取群组信息，平台: {Platform}", platformHelper.GetPlatformName());

            // 调用平台特定的实现
            await platformHelper.GetGroupDicAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "GetGroupDicAsync");
        }
    }

    /// <summary>
    /// 获取用户昵称 
    /// </summary>
    /// <param name="account"></param>
    /// <returns></returns>
    public async Task<string> GetNickNameAsync(string account)
    {
        try
        {
            // 获取当前平台的助手实例
            var platformHelper = GetCurrentPlatformHelper();
            logger.LogDebug("开始获取用户昵称，账号: {Account}，平台: {Platform}", account, platformHelper.GetPlatformName());

            // 调用平台特定的实现
            return await platformHelper.GetNickNameAsync(account);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "GetNickNameAsync");
            return account; // 异常时返回账号作为昵称
        }
    }

    /// <summary>
    /// 发送群组消息 - 向工作群发送普通消息
    ///
    /// 功能：
    /// - 向当前工作群组发送文本消息
    /// - 自动选择当前配置的聊天平台
    /// - 记录发送日志和异常处理
    ///
    /// 使用场景：
    /// - 开盘通知
    /// - 封盘提醒
    /// - 投注统计
    /// - 系统通知
    /// </summary>
    /// <param name="message">要发送的消息内容</param>
    public async Task SendGroupMessageAsync(string message)
    {
        try
        {
            if (string.IsNullOrEmpty(message))
            {
                logger.LogWarning("尝试发送空消息，已忽略");
                return;
            }

            // 获取当前平台的助手实例
            var platformHelper = GetCurrentPlatformHelper();
            logger.LogDebug("发送群消息，平台: {Platform}，消息长度: {Length}", platformHelper.GetPlatformName(), message.Length);

            // 调用平台特定的实现
            await platformHelper.SendGroupMessageAsync(message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "SendGroupMessageAsync");
            // await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "RobotHelper.SendGroupMessageAsync" + Ai.中括号右, ex.ToString());
        }
    }

    /// <summary>
    /// 发送@群消息
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="atAccount">要@的用户</param>
    public async Task SendGroupMessageAsync(string message, string atAccount)
    {
        try
        {
            if (string.IsNullOrEmpty(message))
            {
                logger.LogWarning("尝试发送空@消息，已忽略");
                return;
            }

            // 获取当前平台的助手实例
            var platformHelper = GetCurrentPlatformHelper();
            logger.LogDebug("发送@群消息，平台: {Platform}，@用户: {AtAccount}，消息长度: {Length}",
                platformHelper.GetPlatformName(), atAccount, message.Length);

            // 调用平台特定的实现
            await platformHelper.SendGroupMessageAsync(message, atAccount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "SendGroupMessageAsync");
            // await _customLogService.AddLogAsync(EnumLogType.机器人, Ai.中括号左 + "RobotHelper.SendGroupMessageAsync" + Ai.中括号右, ex.ToString());
        }
    }

    /// <summary>
    /// 发送图片消息
    /// </summary>
    /// <param name="imgPath">图片路径</param>
    public async Task SendImageAsync(string imgPath)
    {
        try
        {
            // 获取当前平台的助手实例
            var platformHelper = GetCurrentPlatformHelper();
            logger.LogDebug("发送图片消息，平台: {Platform}，图片路径: {ImgPath}",
                platformHelper.GetPlatformName(), imgPath);

            // 调用平台特定的实现
            await platformHelper.SendImageAsync(imgPath);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "SendImageAsync");
        }
    }
}