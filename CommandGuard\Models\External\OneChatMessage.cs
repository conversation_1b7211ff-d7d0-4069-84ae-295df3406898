using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using CommandGuard.Enums;

namespace CommandGuard.Models.External;

/// <summary>
/// OneChat平台消息模型
/// 对应OneChat平台传输过来的消息格式
/// </summary>
public class OneChatMessage
{
    /// <summary>
    /// 消息ID
    /// OneChat平台的消息唯一标识
    /// </summary>
    [JsonPropertyName("Id")]
    public long Id { get; set; }

    /// <summary>
    /// 用户唯一标识
    /// OneChat平台的用户ID
    /// </summary>
    [JsonPropertyName("Uid")]
    [Required(ErrorMessage = @"用户ID不能为空")]
    [StringLength(50, ErrorMessage = @"用户ID长度不能超过50个字符")]
    public string Uid { get; set; } = string.Empty;

    /// <summary>
    /// 用户昵称
    /// OneChat平台的用户显示名称
    /// </summary>
    [JsonPropertyName("NickName")]
    [StringLength(100, ErrorMessage = @"用户昵称长度不能超过100个字符")]
    public string NickName { get; set; } = string.Empty;

    /// <summary>
    /// 消息内容
    /// 用户发送的消息文本内容
    /// </summary>
    [JsonPropertyName("Content")]
    [Required(ErrorMessage = @"消息内容不能为空")]
    [StringLength(1000, ErrorMessage = @"消息内容长度不能超过1000个字符")]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 消息类型
    /// OneChat平台的消息类型标识
    /// 0: 文本消息, 1: 图片消息, 2: 语音消息等
    /// </summary>
    [JsonPropertyName("Type")]
    public int Type { get; set; }

    /// <summary>
    /// 消息创建时间
    /// OneChat平台的消息发送时间
    /// </summary>
    [JsonPropertyName("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 验证消息数据的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public (bool IsValid, string ErrorMessage) Validate()
    {
        if (string.IsNullOrWhiteSpace(Uid))
        {
            return (false, @"用户ID不能为空");
        }

        if (string.IsNullOrWhiteSpace(Content))
        {
            return (false, @"消息内容不能为空");
        }

        if (Uid.Length > 50)
        {
            return (false, @"用户ID长度不能超过50个字符");
        }

        if (!string.IsNullOrEmpty(NickName) && NickName.Length > 100)
        {
            return (false, @"用户昵称长度不能超过100个字符");
        }

        if (Content.Length > 1000)
        {
            return (false, @"消息内容长度不能超过1000个字符");
        }

        if (CreatedAt == default)
        {
            return (false, @"消息创建时间无效");
        }

        return (true, string.Empty);
    }

    /// <summary>
    /// 转换为内部消息模型
    /// </summary>
    /// <returns>内部消息模型</returns>
    public InternalMessage ToInternalMessage()
    {
        return new InternalMessage
        {
            Id = Guid.NewGuid().ToString(),
            Account = Uid,
            NickName = NickName,
            Content = Content,
            Status = EnumMessageStatus.Pending,
            CreatedTime = DateTime.Now,
            ExternalMessageId = Id.ToString(),
            ExternalPlatform = @"OneChat",
            ExternalCreatedTime = CreatedAt
        };
    }

    /// <summary>
    /// 获取消息类型描述
    /// </summary>
    /// <returns>消息类型描述</returns>
    public string GetTypeDescription()
    {
        return Type switch
        {
            0 => @"文本消息",
            1 => @"图片消息",
            2 => @"语音消息",
            3 => @"视频消息",
            4 => @"文件消息",
            5 => @"表情消息",
            _ => @"未知类型"
        };
    }

    /// <summary>
    /// 判断是否为文本消息
    /// </summary>
    /// <returns>是否为文本消息</returns>
    public bool IsTextMessage()
    {
        return Type == 0;
    }

    /// <summary>
    /// 获取用户显示名称
    /// 优先使用昵称，如果昵称为空则使用用户ID
    /// </summary>
    /// <returns>用户显示名称</returns>
    public string GetDisplayName()
    {
        return string.IsNullOrWhiteSpace(NickName) ? Uid : NickName;
    }

    /// <summary>
    /// 重写ToString方法，便于调试和日志记录
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return $@"OneChatMessage[Id={Id}, Uid={Uid}, NickName={NickName}, Type={Type}, Content={Content?.Substring(0, Math.Min(Content.Length, 50))}...]";
    }
}
