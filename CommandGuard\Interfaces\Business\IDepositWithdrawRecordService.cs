using CommandGuard.Models;
using CommandGuard.ViewModels;

namespace CommandGuard.Interfaces.Business;

/// <summary>
/// 上下分记录服务接口
/// 提供上下分记录的查询和统计功能
/// </summary>
public interface IDepositWithdrawRecordService
{
    #region 上分申请相关

    /// <summary>
    /// 创建上分申请
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <param name="amount">申请金额</param>
    /// <param name="originalMessage">原始消息内容</param>
    /// <param name="messageId">关联的消息ID</param>
    /// <returns>申请ID</returns>
    Task<long> CreateDepositRequestAsync(string account, decimal amount, string originalMessage, string messageId);

    /// <summary>
    /// 审核上分申请
    /// </summary>
    /// <param name="requestId">申请ID</param>
    /// <param name="approved">是否批准</param>
    /// <param name="reviewer">审核人员</param>
    /// <param name="reviewNote">审核备注</param>
    /// <returns>是否操作成功</returns>
    Task<bool> ReviewDepositRequestAsync(long requestId, bool approved, string reviewer, string reviewNote = "");

    /// <summary>
    /// 获取待审核的上分申请列表
    /// </summary>
    /// <returns>上分申请列表</returns>
    Task<List<DepositRequest>> GetPendingDepositRequestsAsync();

    /// <summary>
    /// 根据ID获取上分申请
    /// </summary>
    /// <param name="requestId">申请ID</param>
    /// <returns>上分申请信息</returns>
    Task<DepositRequest?> GetDepositRequestAsync(long requestId);

    #endregion

    #region 下分申请相关

    /// <summary>
    /// 创建下分申请
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <param name="amount">申请金额</param>
    /// <param name="originalMessage">原始消息内容</param>
    /// <param name="messageId">关联的消息ID</param>
    /// <returns>申请ID，如果余额不足返回-1</returns>
    Task<long> CreateWithdrawRequestAsync(string account, decimal amount, string originalMessage, string messageId);

    /// <summary>
    /// 审核下分申请
    /// </summary>
    /// <param name="requestId">申请ID</param>
    /// <param name="approved">是否批准</param>
    /// <param name="reviewer">审核人员</param>
    /// <param name="reviewNote">审核备注</param>
    /// <returns>是否操作成功</returns>
    Task<bool> ReviewWithdrawRequestAsync(long requestId, bool approved, string reviewer, string reviewNote = "");

    /// <summary>
    /// 获取待审核的下分申请列表
    /// </summary>
    /// <returns>下分申请列表</returns>
    Task<List<WithdrawRequest>> GetPendingWithdrawRequestsAsync();

    /// <summary>
    /// 根据ID获取下分申请
    /// </summary>
    /// <param name="requestId">申请ID</param>
    /// <returns>下分申请信息</returns>
    Task<WithdrawRequest?> GetWithdrawRequestAsync(long requestId);

    #endregion


    /// <summary>
    /// 根据条件查询上下分记录
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="account">账号（可选）</param>
    /// <param name="type">类型（可选，"上分"或"下分"）</param>
    /// <param name="includeFakeUsers">是否包含假人</param>
    /// <returns>上下分记录列表</returns>
    Task<List<DepositWithdrawRecordViewModel>> QueryDepositWithdrawRecordsAsync(
        DateTime startTime,
        DateTime endTime,
        string? account = null,
        string? type = null,
        bool includeFakeUsers = false);

    /// <summary>
    /// 计算总上分金额
    /// </summary>
    /// <param name="records">上下分记录列表</param>
    /// <returns>总上分金额</returns>
    decimal CalculateTotalDepositAmount(List<DepositWithdrawRecordViewModel> records);

    /// <summary>
    /// 计算总下分金额
    /// </summary>
    /// <param name="records">上下分记录列表</param>
    /// <returns>总下分金额</returns>
    decimal CalculateTotalWithdrawAmount(List<DepositWithdrawRecordViewModel> records);

    /// <summary>
    /// 计算总积分（所有查询用户的可用积分总和）
    /// </summary>
    /// <param name="records">上下分记录列表</param>
    /// <returns>总积分</returns>
    Task<decimal> CalculateTotalBalanceAsync(List<DepositWithdrawRecordViewModel> records);

    /// <summary>
    /// 计算总回水金额
    /// </summary>
    /// <param name="records">上下分记录列表</param>
    /// <returns>总回水金额</returns>
    decimal CalculateTotalRebateAmount(List<DepositWithdrawRecordViewModel> records);

    /// <summary>
    /// 获取上下分记录统计信息
    /// </summary>
    /// <param name="records">上下分记录列表</param>
    /// <returns>统计信息元组（总上分，总下分，总回水，总积分）</returns>
    Task<(decimal TotalDeposit, decimal TotalWithdraw, decimal TotalRebate, decimal TotalBalance)> GetDepositWithdrawStatisticsAsync(List<DepositWithdrawRecordViewModel> records);

    /// <summary>
    /// 获取指定用户的累计上下分金额
    /// </summary>
    /// <param name="account">用户账号</param>
    /// <returns>累计金额元组（总上分，总下分）</returns>
    Task<(decimal TotalDeposit, decimal TotalWithdraw)> GetUserTotalDepositWithdrawAsync(string account);
}